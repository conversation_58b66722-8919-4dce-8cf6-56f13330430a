
# 精简后的项目结构

## 📁 目录结构
```
weak_target_finetune/
├── src/                           # 核心源代码
│   ├── video_sequence_trainer.py  # 视频序列训练器（主要实现）
│   ├── evaluation_system.py       # 完整评估系统
│   └── verification_utils.py      # 验证工具
├── data/                          # 数据文件
│   ├── dataset/                   # 原始数据集
│   │   ├── images/               # 图像数据
│   │   └── labels/               # 标注数据
│   └── training_data.json        # 处理后的训练数据
├── models/                        # 模型文件
│   ├── base_model/               # 基础模型(qwen2.5-vl-7b)
│   └── finetuned_model/          # 微调后的模型
├── results/                       # 结果文件
│   └── evaluation_results.json   # 评估结果
├── configs/                       # 配置文件
│   └── class_mapping.json        # 类别映射
├── backup_before_cleanup/         # 清理前的备份
├── train.py                      # 主训练脚本
├── evaluate.py                   # 主评估脚本
├── cleanup.py                    # 清理脚本
└── README.md                     # 项目说明
```

## 🚀 核心文件说明

### src/video_sequence_trainer.py
- **功能**: 视频序列训练器，项目的核心实现
- **特性**: 支持所有8个序列，两套文件命名系统
- **策略**: 每隔10帧抽取1帧直到抽完

### src/evaluation_system.py  
- **功能**: 完整的评估系统
- **指标**: 精确率、召回率、F1分数
- **策略**: 线性插值 + 完整评估

### data/training_data.json
- **内容**: 所有8个序列的训练数据
- **格式**: 视频序列格式，包含帧信息和标注
- **大小**: 732帧，710个标注

## 📊 项目成果

- **总体F1分数**: 38.31%
- **最佳序列**: data07 (F1: 67.2%)
- **支持序列**: 8个完整序列
- **技术创新**: 视频序列+线性插值策略
