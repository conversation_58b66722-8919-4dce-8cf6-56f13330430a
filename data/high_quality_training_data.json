[{"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/0.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [68, 106, 73, 111], \"label\": \"drone\", \"frame_id\": \"0\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/5.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [66, 106, 71, 111], \"label\": \"drone\", \"frame_id\": \"5\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/10.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [62, 106, 67, 111], \"label\": \"drone\", \"frame_id\": \"10\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/15.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [70, 106, 75, 111], \"label\": \"drone\", \"frame_id\": \"15\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/20.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [67, 106, 72, 111], \"label\": \"drone\", \"frame_id\": \"20\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/25.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [63, 106, 68, 111], \"label\": \"drone\", \"frame_id\": \"25\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/30.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [68, 106, 73, 111], \"label\": \"drone\", \"frame_id\": \"30\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/35.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [68, 106, 73, 111], \"label\": \"drone\", \"frame_id\": \"35\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/40.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [64, 106, 69, 111], \"label\": \"drone\", \"frame_id\": \"40\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/45.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [64, 106, 69, 111], \"label\": \"drone\", \"frame_id\": \"45\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/50.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [55, 107, 60, 112], \"label\": \"drone\", \"frame_id\": \"50\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/55.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [59, 106, 64, 111], \"label\": \"drone\", \"frame_id\": \"55\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/60.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [59, 106, 64, 111], \"label\": \"drone\", \"frame_id\": \"60\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/65.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [70, 107, 75, 112], \"label\": \"drone\", \"frame_id\": \"65\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/70.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [67, 107, 72, 112], \"label\": \"drone\", \"frame_id\": \"70\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/75.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [62, 108, 67, 113], \"label\": \"drone\", \"frame_id\": \"75\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/80.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [64, 109, 69, 114], \"label\": \"drone\", \"frame_id\": \"80\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/85.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [65, 109, 70, 114], \"label\": \"drone\", \"frame_id\": \"85\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/90.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [62, 109, 67, 114], \"label\": \"drone\", \"frame_id\": \"90\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/95.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [64, 109, 69, 114], \"label\": \"drone\", \"frame_id\": \"95\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/100.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [68, 110, 73, 115], \"label\": \"drone\", \"frame_id\": \"100\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/105.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [65, 110, 70, 115], \"label\": \"drone\", \"frame_id\": \"105\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/110.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [61, 109, 66, 114], \"label\": \"drone\", \"frame_id\": \"110\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/115.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [68, 109, 73, 114], \"label\": \"drone\", \"frame_id\": \"115\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/120.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [65, 109, 70, 114], \"label\": \"drone\", \"frame_id\": \"120\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/125.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [62, 110, 67, 115], \"label\": \"drone\", \"frame_id\": \"125\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/130.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [71, 110, 76, 115], \"label\": \"drone\", \"frame_id\": \"130\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/135.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [68, 110, 73, 115], \"label\": \"drone\", \"frame_id\": \"135\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/140.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [65, 109, 70, 114], \"label\": \"drone\", \"frame_id\": \"140\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/145.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [69, 109, 74, 114], \"label\": \"drone\", \"frame_id\": \"145\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/150.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [67, 110, 72, 115], \"label\": \"drone\", \"frame_id\": \"150\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/155.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [65, 110, 70, 115], \"label\": \"drone\", \"frame_id\": \"155\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/160.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [70, 110, 75, 115], \"label\": \"drone\", \"frame_id\": \"160\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/165.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [71, 111, 76, 116], \"label\": \"drone\", \"frame_id\": \"165\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/170.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [66, 111, 71, 116], \"label\": \"drone\", \"frame_id\": \"170\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/175.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [66, 111, 71, 116], \"label\": \"drone\", \"frame_id\": \"175\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/180.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [70, 111, 75, 116], \"label\": \"drone\", \"frame_id\": \"180\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/185.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [68, 111, 73, 116], \"label\": \"drone\", \"frame_id\": \"185\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/190.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [65, 111, 70, 116], \"label\": \"drone\", \"frame_id\": \"190\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/195.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [62, 111, 67, 116], \"label\": \"drone\", \"frame_id\": \"195\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/200.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [68, 111, 73, 116], \"label\": \"drone\", \"frame_id\": \"200\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/205.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [65, 111, 70, 116], \"label\": \"drone\", \"frame_id\": \"205\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/210.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [76, 111, 81, 116], \"label\": \"drone\", \"frame_id\": \"210\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/215.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [73, 111, 78, 116], \"label\": \"drone\", \"frame_id\": \"215\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/220.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [68, 111, 73, 116], \"label\": \"drone\", \"frame_id\": \"220\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/225.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [71, 111, 76, 116], \"label\": \"drone\", \"frame_id\": \"225\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/230.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [70, 111, 75, 116], \"label\": \"drone\", \"frame_id\": \"230\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/235.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [68, 111, 73, 116], \"label\": \"drone\", \"frame_id\": \"235\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/240.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [71, 111, 76, 116], \"label\": \"drone\", \"frame_id\": \"240\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/245.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [71, 111, 76, 116], \"label\": \"drone\", \"frame_id\": \"245\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/250.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [68, 111, 73, 116], \"label\": \"drone\", \"frame_id\": \"250\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/255.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [68, 111, 73, 116], \"label\": \"drone\", \"frame_id\": \"255\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/260.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [73, 112, 78, 117], \"label\": \"drone\", \"frame_id\": \"260\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/265.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [71, 112, 76, 117], \"label\": \"drone\", \"frame_id\": \"265\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/270.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [68, 112, 73, 117], \"label\": \"drone\", \"frame_id\": \"270\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/275.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [74, 111, 79, 116], \"label\": \"drone\", \"frame_id\": \"275\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/280.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [71, 111, 76, 116], \"label\": \"drone\", \"frame_id\": \"280\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/285.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [68, 112, 73, 117], \"label\": \"drone\", \"frame_id\": \"285\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/290.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [75, 112, 80, 117], \"label\": \"drone\", \"frame_id\": \"290\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/295.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [74, 112, 79, 117], \"label\": \"drone\", \"frame_id\": \"295\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/0.bmp"}, {"type": "image", "image": "dataset/images/data01/5.bmp"}, {"type": "image", "image": "dataset/images/data01/10.bmp"}, {"type": "image", "image": "dataset/images/data01/15.bmp"}, {"type": "image", "image": "dataset/images/data01/20.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data01\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [68, 106, 73, 111], \"label\": \"drone\", \"frame_id\": \"0\"}, {\"bbox\": [66, 106, 71, 111], \"label\": \"drone\", \"frame_id\": \"5\"}, {\"bbox\": [62, 106, 67, 111], \"label\": \"drone\", \"frame_id\": \"10\"}, {\"bbox\": [70, 106, 75, 111], \"label\": \"drone\", \"frame_id\": \"15\"}, {\"bbox\": [67, 106, 72, 111], \"label\": \"drone\", \"frame_id\": \"20\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/15.bmp"}, {"type": "image", "image": "dataset/images/data01/20.bmp"}, {"type": "image", "image": "dataset/images/data01/25.bmp"}, {"type": "image", "image": "dataset/images/data01/30.bmp"}, {"type": "image", "image": "dataset/images/data01/35.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data01\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [70, 106, 75, 111], \"label\": \"drone\", \"frame_id\": \"15\"}, {\"bbox\": [67, 106, 72, 111], \"label\": \"drone\", \"frame_id\": \"20\"}, {\"bbox\": [63, 106, 68, 111], \"label\": \"drone\", \"frame_id\": \"25\"}, {\"bbox\": [68, 106, 73, 111], \"label\": \"drone\", \"frame_id\": \"30\"}, {\"bbox\": [68, 106, 73, 111], \"label\": \"drone\", \"frame_id\": \"35\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/30.bmp"}, {"type": "image", "image": "dataset/images/data01/35.bmp"}, {"type": "image", "image": "dataset/images/data01/40.bmp"}, {"type": "image", "image": "dataset/images/data01/45.bmp"}, {"type": "image", "image": "dataset/images/data01/50.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data01\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [68, 106, 73, 111], \"label\": \"drone\", \"frame_id\": \"30\"}, {\"bbox\": [68, 106, 73, 111], \"label\": \"drone\", \"frame_id\": \"35\"}, {\"bbox\": [64, 106, 69, 111], \"label\": \"drone\", \"frame_id\": \"40\"}, {\"bbox\": [64, 106, 69, 111], \"label\": \"drone\", \"frame_id\": \"45\"}, {\"bbox\": [55, 107, 60, 112], \"label\": \"drone\", \"frame_id\": \"50\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/45.bmp"}, {"type": "image", "image": "dataset/images/data01/50.bmp"}, {"type": "image", "image": "dataset/images/data01/55.bmp"}, {"type": "image", "image": "dataset/images/data01/60.bmp"}, {"type": "image", "image": "dataset/images/data01/65.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data01\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [64, 106, 69, 111], \"label\": \"drone\", \"frame_id\": \"45\"}, {\"bbox\": [55, 107, 60, 112], \"label\": \"drone\", \"frame_id\": \"50\"}, {\"bbox\": [59, 106, 64, 111], \"label\": \"drone\", \"frame_id\": \"55\"}, {\"bbox\": [59, 106, 64, 111], \"label\": \"drone\", \"frame_id\": \"60\"}, {\"bbox\": [70, 107, 75, 112], \"label\": \"drone\", \"frame_id\": \"65\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/60.bmp"}, {"type": "image", "image": "dataset/images/data01/65.bmp"}, {"type": "image", "image": "dataset/images/data01/70.bmp"}, {"type": "image", "image": "dataset/images/data01/75.bmp"}, {"type": "image", "image": "dataset/images/data01/80.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data01\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [59, 106, 64, 111], \"label\": \"drone\", \"frame_id\": \"60\"}, {\"bbox\": [70, 107, 75, 112], \"label\": \"drone\", \"frame_id\": \"65\"}, {\"bbox\": [67, 107, 72, 112], \"label\": \"drone\", \"frame_id\": \"70\"}, {\"bbox\": [62, 108, 67, 113], \"label\": \"drone\", \"frame_id\": \"75\"}, {\"bbox\": [64, 109, 69, 114], \"label\": \"drone\", \"frame_id\": \"80\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/75.bmp"}, {"type": "image", "image": "dataset/images/data01/80.bmp"}, {"type": "image", "image": "dataset/images/data01/85.bmp"}, {"type": "image", "image": "dataset/images/data01/90.bmp"}, {"type": "image", "image": "dataset/images/data01/95.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data01\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [62, 108, 67, 113], \"label\": \"drone\", \"frame_id\": \"75\"}, {\"bbox\": [64, 109, 69, 114], \"label\": \"drone\", \"frame_id\": \"80\"}, {\"bbox\": [65, 109, 70, 114], \"label\": \"drone\", \"frame_id\": \"85\"}, {\"bbox\": [62, 109, 67, 114], \"label\": \"drone\", \"frame_id\": \"90\"}, {\"bbox\": [64, 109, 69, 114], \"label\": \"drone\", \"frame_id\": \"95\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/90.bmp"}, {"type": "image", "image": "dataset/images/data01/95.bmp"}, {"type": "image", "image": "dataset/images/data01/100.bmp"}, {"type": "image", "image": "dataset/images/data01/105.bmp"}, {"type": "image", "image": "dataset/images/data01/110.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data01\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [62, 109, 67, 114], \"label\": \"drone\", \"frame_id\": \"90\"}, {\"bbox\": [64, 109, 69, 114], \"label\": \"drone\", \"frame_id\": \"95\"}, {\"bbox\": [68, 110, 73, 115], \"label\": \"drone\", \"frame_id\": \"100\"}, {\"bbox\": [65, 110, 70, 115], \"label\": \"drone\", \"frame_id\": \"105\"}, {\"bbox\": [61, 109, 66, 114], \"label\": \"drone\", \"frame_id\": \"110\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/105.bmp"}, {"type": "image", "image": "dataset/images/data01/110.bmp"}, {"type": "image", "image": "dataset/images/data01/115.bmp"}, {"type": "image", "image": "dataset/images/data01/120.bmp"}, {"type": "image", "image": "dataset/images/data01/125.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data01\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [65, 110, 70, 115], \"label\": \"drone\", \"frame_id\": \"105\"}, {\"bbox\": [61, 109, 66, 114], \"label\": \"drone\", \"frame_id\": \"110\"}, {\"bbox\": [68, 109, 73, 114], \"label\": \"drone\", \"frame_id\": \"115\"}, {\"bbox\": [65, 109, 70, 114], \"label\": \"drone\", \"frame_id\": \"120\"}, {\"bbox\": [62, 110, 67, 115], \"label\": \"drone\", \"frame_id\": \"125\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/120.bmp"}, {"type": "image", "image": "dataset/images/data01/125.bmp"}, {"type": "image", "image": "dataset/images/data01/130.bmp"}, {"type": "image", "image": "dataset/images/data01/135.bmp"}, {"type": "image", "image": "dataset/images/data01/140.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data01\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [65, 109, 70, 114], \"label\": \"drone\", \"frame_id\": \"120\"}, {\"bbox\": [62, 110, 67, 115], \"label\": \"drone\", \"frame_id\": \"125\"}, {\"bbox\": [71, 110, 76, 115], \"label\": \"drone\", \"frame_id\": \"130\"}, {\"bbox\": [68, 110, 73, 115], \"label\": \"drone\", \"frame_id\": \"135\"}, {\"bbox\": [65, 109, 70, 114], \"label\": \"drone\", \"frame_id\": \"140\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/135.bmp"}, {"type": "image", "image": "dataset/images/data01/140.bmp"}, {"type": "image", "image": "dataset/images/data01/145.bmp"}, {"type": "image", "image": "dataset/images/data01/150.bmp"}, {"type": "image", "image": "dataset/images/data01/155.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data01\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [68, 110, 73, 115], \"label\": \"drone\", \"frame_id\": \"135\"}, {\"bbox\": [65, 109, 70, 114], \"label\": \"drone\", \"frame_id\": \"140\"}, {\"bbox\": [69, 109, 74, 114], \"label\": \"drone\", \"frame_id\": \"145\"}, {\"bbox\": [67, 110, 72, 115], \"label\": \"drone\", \"frame_id\": \"150\"}, {\"bbox\": [65, 110, 70, 115], \"label\": \"drone\", \"frame_id\": \"155\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/150.bmp"}, {"type": "image", "image": "dataset/images/data01/155.bmp"}, {"type": "image", "image": "dataset/images/data01/160.bmp"}, {"type": "image", "image": "dataset/images/data01/165.bmp"}, {"type": "image", "image": "dataset/images/data01/170.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data01\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [67, 110, 72, 115], \"label\": \"drone\", \"frame_id\": \"150\"}, {\"bbox\": [65, 110, 70, 115], \"label\": \"drone\", \"frame_id\": \"155\"}, {\"bbox\": [70, 110, 75, 115], \"label\": \"drone\", \"frame_id\": \"160\"}, {\"bbox\": [71, 111, 76, 116], \"label\": \"drone\", \"frame_id\": \"165\"}, {\"bbox\": [66, 111, 71, 116], \"label\": \"drone\", \"frame_id\": \"170\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/165.bmp"}, {"type": "image", "image": "dataset/images/data01/170.bmp"}, {"type": "image", "image": "dataset/images/data01/175.bmp"}, {"type": "image", "image": "dataset/images/data01/180.bmp"}, {"type": "image", "image": "dataset/images/data01/185.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data01\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [71, 111, 76, 116], \"label\": \"drone\", \"frame_id\": \"165\"}, {\"bbox\": [66, 111, 71, 116], \"label\": \"drone\", \"frame_id\": \"170\"}, {\"bbox\": [66, 111, 71, 116], \"label\": \"drone\", \"frame_id\": \"175\"}, {\"bbox\": [70, 111, 75, 116], \"label\": \"drone\", \"frame_id\": \"180\"}, {\"bbox\": [68, 111, 73, 116], \"label\": \"drone\", \"frame_id\": \"185\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/180.bmp"}, {"type": "image", "image": "dataset/images/data01/185.bmp"}, {"type": "image", "image": "dataset/images/data01/190.bmp"}, {"type": "image", "image": "dataset/images/data01/195.bmp"}, {"type": "image", "image": "dataset/images/data01/200.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data01\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [70, 111, 75, 116], \"label\": \"drone\", \"frame_id\": \"180\"}, {\"bbox\": [68, 111, 73, 116], \"label\": \"drone\", \"frame_id\": \"185\"}, {\"bbox\": [65, 111, 70, 116], \"label\": \"drone\", \"frame_id\": \"190\"}, {\"bbox\": [62, 111, 67, 116], \"label\": \"drone\", \"frame_id\": \"195\"}, {\"bbox\": [68, 111, 73, 116], \"label\": \"drone\", \"frame_id\": \"200\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/195.bmp"}, {"type": "image", "image": "dataset/images/data01/200.bmp"}, {"type": "image", "image": "dataset/images/data01/205.bmp"}, {"type": "image", "image": "dataset/images/data01/210.bmp"}, {"type": "image", "image": "dataset/images/data01/215.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data01\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [62, 111, 67, 116], \"label\": \"drone\", \"frame_id\": \"195\"}, {\"bbox\": [68, 111, 73, 116], \"label\": \"drone\", \"frame_id\": \"200\"}, {\"bbox\": [65, 111, 70, 116], \"label\": \"drone\", \"frame_id\": \"205\"}, {\"bbox\": [76, 111, 81, 116], \"label\": \"drone\", \"frame_id\": \"210\"}, {\"bbox\": [73, 111, 78, 116], \"label\": \"drone\", \"frame_id\": \"215\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/210.bmp"}, {"type": "image", "image": "dataset/images/data01/215.bmp"}, {"type": "image", "image": "dataset/images/data01/220.bmp"}, {"type": "image", "image": "dataset/images/data01/225.bmp"}, {"type": "image", "image": "dataset/images/data01/230.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data01\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [76, 111, 81, 116], \"label\": \"drone\", \"frame_id\": \"210\"}, {\"bbox\": [73, 111, 78, 116], \"label\": \"drone\", \"frame_id\": \"215\"}, {\"bbox\": [68, 111, 73, 116], \"label\": \"drone\", \"frame_id\": \"220\"}, {\"bbox\": [71, 111, 76, 116], \"label\": \"drone\", \"frame_id\": \"225\"}, {\"bbox\": [70, 111, 75, 116], \"label\": \"drone\", \"frame_id\": \"230\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/225.bmp"}, {"type": "image", "image": "dataset/images/data01/230.bmp"}, {"type": "image", "image": "dataset/images/data01/235.bmp"}, {"type": "image", "image": "dataset/images/data01/240.bmp"}, {"type": "image", "image": "dataset/images/data01/245.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data01\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [71, 111, 76, 116], \"label\": \"drone\", \"frame_id\": \"225\"}, {\"bbox\": [70, 111, 75, 116], \"label\": \"drone\", \"frame_id\": \"230\"}, {\"bbox\": [68, 111, 73, 116], \"label\": \"drone\", \"frame_id\": \"235\"}, {\"bbox\": [71, 111, 76, 116], \"label\": \"drone\", \"frame_id\": \"240\"}, {\"bbox\": [71, 111, 76, 116], \"label\": \"drone\", \"frame_id\": \"245\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/240.bmp"}, {"type": "image", "image": "dataset/images/data01/245.bmp"}, {"type": "image", "image": "dataset/images/data01/250.bmp"}, {"type": "image", "image": "dataset/images/data01/255.bmp"}, {"type": "image", "image": "dataset/images/data01/260.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data01\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [71, 111, 76, 116], \"label\": \"drone\", \"frame_id\": \"240\"}, {\"bbox\": [71, 111, 76, 116], \"label\": \"drone\", \"frame_id\": \"245\"}, {\"bbox\": [68, 111, 73, 116], \"label\": \"drone\", \"frame_id\": \"250\"}, {\"bbox\": [68, 111, 73, 116], \"label\": \"drone\", \"frame_id\": \"255\"}, {\"bbox\": [73, 112, 78, 117], \"label\": \"drone\", \"frame_id\": \"260\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/255.bmp"}, {"type": "image", "image": "dataset/images/data01/260.bmp"}, {"type": "image", "image": "dataset/images/data01/265.bmp"}, {"type": "image", "image": "dataset/images/data01/270.bmp"}, {"type": "image", "image": "dataset/images/data01/275.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data01\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [68, 111, 73, 116], \"label\": \"drone\", \"frame_id\": \"255\"}, {\"bbox\": [73, 112, 78, 117], \"label\": \"drone\", \"frame_id\": \"260\"}, {\"bbox\": [71, 112, 76, 117], \"label\": \"drone\", \"frame_id\": \"265\"}, {\"bbox\": [68, 112, 73, 117], \"label\": \"drone\", \"frame_id\": \"270\"}, {\"bbox\": [74, 111, 79, 116], \"label\": \"drone\", \"frame_id\": \"275\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/270.bmp"}, {"type": "image", "image": "dataset/images/data01/275.bmp"}, {"type": "image", "image": "dataset/images/data01/280.bmp"}, {"type": "image", "image": "dataset/images/data01/285.bmp"}, {"type": "image", "image": "dataset/images/data01/290.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data01\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [68, 112, 73, 117], \"label\": \"drone\", \"frame_id\": \"270\"}, {\"bbox\": [74, 111, 79, 116], \"label\": \"drone\", \"frame_id\": \"275\"}, {\"bbox\": [71, 111, 76, 116], \"label\": \"drone\", \"frame_id\": \"280\"}, {\"bbox\": [68, 112, 73, 117], \"label\": \"drone\", \"frame_id\": \"285\"}, {\"bbox\": [75, 112, 80, 117], \"label\": \"drone\", \"frame_id\": \"290\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/0.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [97, 88, 102, 93], \"label\": \"drone\", \"frame_id\": \"0\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/5.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [97, 87, 102, 92], \"label\": \"drone\", \"frame_id\": \"5\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/10.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [96, 90, 101, 95], \"label\": \"drone\", \"frame_id\": \"10\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/15.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [96, 90, 101, 95], \"label\": \"drone\", \"frame_id\": \"15\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/20.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [96, 89, 101, 94], \"label\": \"drone\", \"frame_id\": \"20\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/25.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [96, 89, 101, 94], \"label\": \"drone\", \"frame_id\": \"25\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/30.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [97, 89, 102, 94], \"label\": \"drone\", \"frame_id\": \"30\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/35.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [96, 89, 101, 94], \"label\": \"drone\", \"frame_id\": \"35\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/40.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [96, 88, 101, 93], \"label\": \"drone\", \"frame_id\": \"40\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/45.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [98, 91, 103, 96], \"label\": \"drone\", \"frame_id\": \"45\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/50.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [98, 89, 103, 94], \"label\": \"drone\", \"frame_id\": \"50\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/55.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [97, 89, 102, 94], \"label\": \"drone\", \"frame_id\": \"55\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/60.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [99, 91, 104, 96], \"label\": \"drone\", \"frame_id\": \"60\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/65.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [99, 90, 104, 95], \"label\": \"drone\", \"frame_id\": \"65\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/70.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [99, 89, 104, 94], \"label\": \"drone\", \"frame_id\": \"70\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/75.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [97, 87, 102, 92], \"label\": \"drone\", \"frame_id\": \"75\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/80.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [99, 88, 104, 93], \"label\": \"drone\", \"frame_id\": \"80\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/85.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [99, 94, 104, 99], \"label\": \"drone\", \"frame_id\": \"85\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/90.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [99, 92, 104, 97], \"label\": \"drone\", \"frame_id\": \"90\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/95.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [98, 91, 103, 96], \"label\": \"drone\", \"frame_id\": \"95\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/100.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [98, 92, 103, 97], \"label\": \"drone\", \"frame_id\": \"100\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/105.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [96, 92, 101, 97], \"label\": \"drone\", \"frame_id\": \"105\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/110.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [96, 91, 101, 96], \"label\": \"drone\", \"frame_id\": \"110\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/115.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [96, 94, 101, 99], \"label\": \"drone\", \"frame_id\": \"115\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/120.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [96, 93, 101, 98], \"label\": \"drone\", \"frame_id\": \"120\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/125.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [95, 92, 100, 97], \"label\": \"drone\", \"frame_id\": \"125\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/130.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [95, 92, 100, 97], \"label\": \"drone\", \"frame_id\": \"130\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/135.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [95, 91, 100, 96], \"label\": \"drone\", \"frame_id\": \"135\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/140.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [94, 90, 99, 95], \"label\": \"drone\", \"frame_id\": \"140\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/145.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [94, 89, 99, 94], \"label\": \"drone\", \"frame_id\": \"145\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/150.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [95, 94, 100, 99], \"label\": \"drone\", \"frame_id\": \"150\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/155.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [95, 92, 100, 97], \"label\": \"drone\", \"frame_id\": \"155\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/160.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [95, 91, 100, 96], \"label\": \"drone\", \"frame_id\": \"160\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/165.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [98, 94, 103, 99], \"label\": \"drone\", \"frame_id\": \"165\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/170.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [98, 92, 103, 97], \"label\": \"drone\", \"frame_id\": \"170\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/175.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [98, 91, 103, 96], \"label\": \"drone\", \"frame_id\": \"175\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/180.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [101, 91, 106, 96], \"label\": \"drone\", \"frame_id\": \"180\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/185.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [96, 90, 101, 95], \"label\": \"drone\", \"frame_id\": \"185\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/190.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [96, 91, 101, 96], \"label\": \"drone\", \"frame_id\": \"190\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/195.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [96, 88, 101, 93], \"label\": \"drone\", \"frame_id\": \"195\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/200.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [96, 91, 101, 96], \"label\": \"drone\", \"frame_id\": \"200\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/205.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [95, 92, 100, 97], \"label\": \"drone\", \"frame_id\": \"205\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/210.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [94, 91, 99, 96], \"label\": \"drone\", \"frame_id\": \"210\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/215.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [94, 91, 99, 96], \"label\": \"drone\", \"frame_id\": \"215\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/220.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [96, 95, 101, 100], \"label\": \"drone\", \"frame_id\": \"220\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/225.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [96, 94, 101, 99], \"label\": \"drone\", \"frame_id\": \"225\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/230.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [96, 92, 101, 97], \"label\": \"drone\", \"frame_id\": \"230\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/235.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [95, 93, 100, 98], \"label\": \"drone\", \"frame_id\": \"235\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/240.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [95, 93, 100, 98], \"label\": \"drone\", \"frame_id\": \"240\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/245.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [94, 92, 99, 97], \"label\": \"drone\", \"frame_id\": \"245\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/250.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [96, 95, 101, 100], \"label\": \"drone\", \"frame_id\": \"250\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/255.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [99, 97, 104, 102], \"label\": \"drone\", \"frame_id\": \"255\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/260.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [98, 96, 103, 101], \"label\": \"drone\", \"frame_id\": \"260\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/265.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [97, 95, 102, 100], \"label\": \"drone\", \"frame_id\": \"265\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/270.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [97, 95, 102, 100], \"label\": \"drone\", \"frame_id\": \"270\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/275.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [96, 94, 101, 99], \"label\": \"drone\", \"frame_id\": \"275\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/280.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [96, 93, 101, 98], \"label\": \"drone\", \"frame_id\": \"280\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/285.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [96, 98, 101, 103], \"label\": \"drone\", \"frame_id\": \"285\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/290.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [97, 97, 102, 102], \"label\": \"drone\", \"frame_id\": \"290\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/295.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [96, 96, 101, 101], \"label\": \"drone\", \"frame_id\": \"295\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/0.bmp"}, {"type": "image", "image": "dataset/images/data02/5.bmp"}, {"type": "image", "image": "dataset/images/data02/10.bmp"}, {"type": "image", "image": "dataset/images/data02/15.bmp"}, {"type": "image", "image": "dataset/images/data02/20.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data02\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [97, 88, 102, 93], \"label\": \"drone\", \"frame_id\": \"0\"}, {\"bbox\": [97, 87, 102, 92], \"label\": \"drone\", \"frame_id\": \"5\"}, {\"bbox\": [96, 90, 101, 95], \"label\": \"drone\", \"frame_id\": \"10\"}, {\"bbox\": [96, 90, 101, 95], \"label\": \"drone\", \"frame_id\": \"15\"}, {\"bbox\": [96, 89, 101, 94], \"label\": \"drone\", \"frame_id\": \"20\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/15.bmp"}, {"type": "image", "image": "dataset/images/data02/20.bmp"}, {"type": "image", "image": "dataset/images/data02/25.bmp"}, {"type": "image", "image": "dataset/images/data02/30.bmp"}, {"type": "image", "image": "dataset/images/data02/35.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data02\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [96, 90, 101, 95], \"label\": \"drone\", \"frame_id\": \"15\"}, {\"bbox\": [96, 89, 101, 94], \"label\": \"drone\", \"frame_id\": \"20\"}, {\"bbox\": [96, 89, 101, 94], \"label\": \"drone\", \"frame_id\": \"25\"}, {\"bbox\": [97, 89, 102, 94], \"label\": \"drone\", \"frame_id\": \"30\"}, {\"bbox\": [96, 89, 101, 94], \"label\": \"drone\", \"frame_id\": \"35\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/30.bmp"}, {"type": "image", "image": "dataset/images/data02/35.bmp"}, {"type": "image", "image": "dataset/images/data02/40.bmp"}, {"type": "image", "image": "dataset/images/data02/45.bmp"}, {"type": "image", "image": "dataset/images/data02/50.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data02\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [97, 89, 102, 94], \"label\": \"drone\", \"frame_id\": \"30\"}, {\"bbox\": [96, 89, 101, 94], \"label\": \"drone\", \"frame_id\": \"35\"}, {\"bbox\": [96, 88, 101, 93], \"label\": \"drone\", \"frame_id\": \"40\"}, {\"bbox\": [98, 91, 103, 96], \"label\": \"drone\", \"frame_id\": \"45\"}, {\"bbox\": [98, 89, 103, 94], \"label\": \"drone\", \"frame_id\": \"50\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/45.bmp"}, {"type": "image", "image": "dataset/images/data02/50.bmp"}, {"type": "image", "image": "dataset/images/data02/55.bmp"}, {"type": "image", "image": "dataset/images/data02/60.bmp"}, {"type": "image", "image": "dataset/images/data02/65.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data02\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [98, 91, 103, 96], \"label\": \"drone\", \"frame_id\": \"45\"}, {\"bbox\": [98, 89, 103, 94], \"label\": \"drone\", \"frame_id\": \"50\"}, {\"bbox\": [97, 89, 102, 94], \"label\": \"drone\", \"frame_id\": \"55\"}, {\"bbox\": [99, 91, 104, 96], \"label\": \"drone\", \"frame_id\": \"60\"}, {\"bbox\": [99, 90, 104, 95], \"label\": \"drone\", \"frame_id\": \"65\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/60.bmp"}, {"type": "image", "image": "dataset/images/data02/65.bmp"}, {"type": "image", "image": "dataset/images/data02/70.bmp"}, {"type": "image", "image": "dataset/images/data02/75.bmp"}, {"type": "image", "image": "dataset/images/data02/80.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data02\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [99, 91, 104, 96], \"label\": \"drone\", \"frame_id\": \"60\"}, {\"bbox\": [99, 90, 104, 95], \"label\": \"drone\", \"frame_id\": \"65\"}, {\"bbox\": [99, 89, 104, 94], \"label\": \"drone\", \"frame_id\": \"70\"}, {\"bbox\": [97, 87, 102, 92], \"label\": \"drone\", \"frame_id\": \"75\"}, {\"bbox\": [99, 88, 104, 93], \"label\": \"drone\", \"frame_id\": \"80\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/75.bmp"}, {"type": "image", "image": "dataset/images/data02/80.bmp"}, {"type": "image", "image": "dataset/images/data02/85.bmp"}, {"type": "image", "image": "dataset/images/data02/90.bmp"}, {"type": "image", "image": "dataset/images/data02/95.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data02\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [97, 87, 102, 92], \"label\": \"drone\", \"frame_id\": \"75\"}, {\"bbox\": [99, 88, 104, 93], \"label\": \"drone\", \"frame_id\": \"80\"}, {\"bbox\": [99, 94, 104, 99], \"label\": \"drone\", \"frame_id\": \"85\"}, {\"bbox\": [99, 92, 104, 97], \"label\": \"drone\", \"frame_id\": \"90\"}, {\"bbox\": [98, 91, 103, 96], \"label\": \"drone\", \"frame_id\": \"95\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/90.bmp"}, {"type": "image", "image": "dataset/images/data02/95.bmp"}, {"type": "image", "image": "dataset/images/data02/100.bmp"}, {"type": "image", "image": "dataset/images/data02/105.bmp"}, {"type": "image", "image": "dataset/images/data02/110.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data02\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [99, 92, 104, 97], \"label\": \"drone\", \"frame_id\": \"90\"}, {\"bbox\": [98, 91, 103, 96], \"label\": \"drone\", \"frame_id\": \"95\"}, {\"bbox\": [98, 92, 103, 97], \"label\": \"drone\", \"frame_id\": \"100\"}, {\"bbox\": [96, 92, 101, 97], \"label\": \"drone\", \"frame_id\": \"105\"}, {\"bbox\": [96, 91, 101, 96], \"label\": \"drone\", \"frame_id\": \"110\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/105.bmp"}, {"type": "image", "image": "dataset/images/data02/110.bmp"}, {"type": "image", "image": "dataset/images/data02/115.bmp"}, {"type": "image", "image": "dataset/images/data02/120.bmp"}, {"type": "image", "image": "dataset/images/data02/125.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data02\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [96, 92, 101, 97], \"label\": \"drone\", \"frame_id\": \"105\"}, {\"bbox\": [96, 91, 101, 96], \"label\": \"drone\", \"frame_id\": \"110\"}, {\"bbox\": [96, 94, 101, 99], \"label\": \"drone\", \"frame_id\": \"115\"}, {\"bbox\": [96, 93, 101, 98], \"label\": \"drone\", \"frame_id\": \"120\"}, {\"bbox\": [95, 92, 100, 97], \"label\": \"drone\", \"frame_id\": \"125\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/120.bmp"}, {"type": "image", "image": "dataset/images/data02/125.bmp"}, {"type": "image", "image": "dataset/images/data02/130.bmp"}, {"type": "image", "image": "dataset/images/data02/135.bmp"}, {"type": "image", "image": "dataset/images/data02/140.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data02\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [96, 93, 101, 98], \"label\": \"drone\", \"frame_id\": \"120\"}, {\"bbox\": [95, 92, 100, 97], \"label\": \"drone\", \"frame_id\": \"125\"}, {\"bbox\": [95, 92, 100, 97], \"label\": \"drone\", \"frame_id\": \"130\"}, {\"bbox\": [95, 91, 100, 96], \"label\": \"drone\", \"frame_id\": \"135\"}, {\"bbox\": [94, 90, 99, 95], \"label\": \"drone\", \"frame_id\": \"140\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/135.bmp"}, {"type": "image", "image": "dataset/images/data02/140.bmp"}, {"type": "image", "image": "dataset/images/data02/145.bmp"}, {"type": "image", "image": "dataset/images/data02/150.bmp"}, {"type": "image", "image": "dataset/images/data02/155.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data02\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [95, 91, 100, 96], \"label\": \"drone\", \"frame_id\": \"135\"}, {\"bbox\": [94, 90, 99, 95], \"label\": \"drone\", \"frame_id\": \"140\"}, {\"bbox\": [94, 89, 99, 94], \"label\": \"drone\", \"frame_id\": \"145\"}, {\"bbox\": [95, 94, 100, 99], \"label\": \"drone\", \"frame_id\": \"150\"}, {\"bbox\": [95, 92, 100, 97], \"label\": \"drone\", \"frame_id\": \"155\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/150.bmp"}, {"type": "image", "image": "dataset/images/data02/155.bmp"}, {"type": "image", "image": "dataset/images/data02/160.bmp"}, {"type": "image", "image": "dataset/images/data02/165.bmp"}, {"type": "image", "image": "dataset/images/data02/170.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data02\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [95, 94, 100, 99], \"label\": \"drone\", \"frame_id\": \"150\"}, {\"bbox\": [95, 92, 100, 97], \"label\": \"drone\", \"frame_id\": \"155\"}, {\"bbox\": [95, 91, 100, 96], \"label\": \"drone\", \"frame_id\": \"160\"}, {\"bbox\": [98, 94, 103, 99], \"label\": \"drone\", \"frame_id\": \"165\"}, {\"bbox\": [98, 92, 103, 97], \"label\": \"drone\", \"frame_id\": \"170\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/165.bmp"}, {"type": "image", "image": "dataset/images/data02/170.bmp"}, {"type": "image", "image": "dataset/images/data02/175.bmp"}, {"type": "image", "image": "dataset/images/data02/180.bmp"}, {"type": "image", "image": "dataset/images/data02/185.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data02\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [98, 94, 103, 99], \"label\": \"drone\", \"frame_id\": \"165\"}, {\"bbox\": [98, 92, 103, 97], \"label\": \"drone\", \"frame_id\": \"170\"}, {\"bbox\": [98, 91, 103, 96], \"label\": \"drone\", \"frame_id\": \"175\"}, {\"bbox\": [101, 91, 106, 96], \"label\": \"drone\", \"frame_id\": \"180\"}, {\"bbox\": [96, 90, 101, 95], \"label\": \"drone\", \"frame_id\": \"185\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/180.bmp"}, {"type": "image", "image": "dataset/images/data02/185.bmp"}, {"type": "image", "image": "dataset/images/data02/190.bmp"}, {"type": "image", "image": "dataset/images/data02/195.bmp"}, {"type": "image", "image": "dataset/images/data02/200.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data02\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [101, 91, 106, 96], \"label\": \"drone\", \"frame_id\": \"180\"}, {\"bbox\": [96, 90, 101, 95], \"label\": \"drone\", \"frame_id\": \"185\"}, {\"bbox\": [96, 91, 101, 96], \"label\": \"drone\", \"frame_id\": \"190\"}, {\"bbox\": [96, 88, 101, 93], \"label\": \"drone\", \"frame_id\": \"195\"}, {\"bbox\": [96, 91, 101, 96], \"label\": \"drone\", \"frame_id\": \"200\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/195.bmp"}, {"type": "image", "image": "dataset/images/data02/200.bmp"}, {"type": "image", "image": "dataset/images/data02/205.bmp"}, {"type": "image", "image": "dataset/images/data02/210.bmp"}, {"type": "image", "image": "dataset/images/data02/215.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data02\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [96, 88, 101, 93], \"label\": \"drone\", \"frame_id\": \"195\"}, {\"bbox\": [96, 91, 101, 96], \"label\": \"drone\", \"frame_id\": \"200\"}, {\"bbox\": [95, 92, 100, 97], \"label\": \"drone\", \"frame_id\": \"205\"}, {\"bbox\": [94, 91, 99, 96], \"label\": \"drone\", \"frame_id\": \"210\"}, {\"bbox\": [94, 91, 99, 96], \"label\": \"drone\", \"frame_id\": \"215\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/210.bmp"}, {"type": "image", "image": "dataset/images/data02/215.bmp"}, {"type": "image", "image": "dataset/images/data02/220.bmp"}, {"type": "image", "image": "dataset/images/data02/225.bmp"}, {"type": "image", "image": "dataset/images/data02/230.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data02\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [94, 91, 99, 96], \"label\": \"drone\", \"frame_id\": \"210\"}, {\"bbox\": [94, 91, 99, 96], \"label\": \"drone\", \"frame_id\": \"215\"}, {\"bbox\": [96, 95, 101, 100], \"label\": \"drone\", \"frame_id\": \"220\"}, {\"bbox\": [96, 94, 101, 99], \"label\": \"drone\", \"frame_id\": \"225\"}, {\"bbox\": [96, 92, 101, 97], \"label\": \"drone\", \"frame_id\": \"230\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/225.bmp"}, {"type": "image", "image": "dataset/images/data02/230.bmp"}, {"type": "image", "image": "dataset/images/data02/235.bmp"}, {"type": "image", "image": "dataset/images/data02/240.bmp"}, {"type": "image", "image": "dataset/images/data02/245.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data02\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [96, 94, 101, 99], \"label\": \"drone\", \"frame_id\": \"225\"}, {\"bbox\": [96, 92, 101, 97], \"label\": \"drone\", \"frame_id\": \"230\"}, {\"bbox\": [95, 93, 100, 98], \"label\": \"drone\", \"frame_id\": \"235\"}, {\"bbox\": [95, 93, 100, 98], \"label\": \"drone\", \"frame_id\": \"240\"}, {\"bbox\": [94, 92, 99, 97], \"label\": \"drone\", \"frame_id\": \"245\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/240.bmp"}, {"type": "image", "image": "dataset/images/data02/245.bmp"}, {"type": "image", "image": "dataset/images/data02/250.bmp"}, {"type": "image", "image": "dataset/images/data02/255.bmp"}, {"type": "image", "image": "dataset/images/data02/260.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data02\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [95, 93, 100, 98], \"label\": \"drone\", \"frame_id\": \"240\"}, {\"bbox\": [94, 92, 99, 97], \"label\": \"drone\", \"frame_id\": \"245\"}, {\"bbox\": [96, 95, 101, 100], \"label\": \"drone\", \"frame_id\": \"250\"}, {\"bbox\": [99, 97, 104, 102], \"label\": \"drone\", \"frame_id\": \"255\"}, {\"bbox\": [98, 96, 103, 101], \"label\": \"drone\", \"frame_id\": \"260\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/255.bmp"}, {"type": "image", "image": "dataset/images/data02/260.bmp"}, {"type": "image", "image": "dataset/images/data02/265.bmp"}, {"type": "image", "image": "dataset/images/data02/270.bmp"}, {"type": "image", "image": "dataset/images/data02/275.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data02\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [99, 97, 104, 102], \"label\": \"drone\", \"frame_id\": \"255\"}, {\"bbox\": [98, 96, 103, 101], \"label\": \"drone\", \"frame_id\": \"260\"}, {\"bbox\": [97, 95, 102, 100], \"label\": \"drone\", \"frame_id\": \"265\"}, {\"bbox\": [97, 95, 102, 100], \"label\": \"drone\", \"frame_id\": \"270\"}, {\"bbox\": [96, 94, 101, 99], \"label\": \"drone\", \"frame_id\": \"275\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/270.bmp"}, {"type": "image", "image": "dataset/images/data02/275.bmp"}, {"type": "image", "image": "dataset/images/data02/280.bmp"}, {"type": "image", "image": "dataset/images/data02/285.bmp"}, {"type": "image", "image": "dataset/images/data02/290.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data02\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [97, 95, 102, 100], \"label\": \"drone\", \"frame_id\": \"270\"}, {\"bbox\": [96, 94, 101, 99], \"label\": \"drone\", \"frame_id\": \"275\"}, {\"bbox\": [96, 93, 101, 98], \"label\": \"drone\", \"frame_id\": \"280\"}, {\"bbox\": [96, 98, 101, 103], \"label\": \"drone\", \"frame_id\": \"285\"}, {\"bbox\": [97, 97, 102, 102], \"label\": \"drone\", \"frame_id\": \"290\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/0.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [97, 78, 102, 83], \"label\": \"drone\", \"frame_id\": \"0\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/5.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [104, 82, 109, 87], \"label\": \"drone\", \"frame_id\": \"5\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/10.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [104, 79, 109, 84], \"label\": \"drone\", \"frame_id\": \"10\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/15.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [102, 78, 107, 83], \"label\": \"drone\", \"frame_id\": \"15\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/20.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [101, 85, 106, 90], \"label\": \"drone\", \"frame_id\": \"20\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/25.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [100, 82, 105, 87], \"label\": \"drone\", \"frame_id\": \"25\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/30.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [100, 80, 105, 85], \"label\": \"drone\", \"frame_id\": \"30\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/35.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [103, 84, 108, 89], \"label\": \"drone\", \"frame_id\": \"35\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/40.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [104, 83, 109, 88], \"label\": \"drone\", \"frame_id\": \"40\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/45.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [103, 80, 108, 85], \"label\": \"drone\", \"frame_id\": \"45\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/50.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [101, 83, 106, 88], \"label\": \"drone\", \"frame_id\": \"50\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/55.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [103, 82, 108, 87], \"label\": \"drone\", \"frame_id\": \"55\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/60.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [103, 80, 108, 85], \"label\": \"drone\", \"frame_id\": \"60\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/65.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [103, 79, 108, 84], \"label\": \"drone\", \"frame_id\": \"65\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/70.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [102, 84, 107, 89], \"label\": \"drone\", \"frame_id\": \"70\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/75.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [101, 82, 106, 87], \"label\": \"drone\", \"frame_id\": \"75\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/80.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [100, 80, 105, 85], \"label\": \"drone\", \"frame_id\": \"80\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/85.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [100, 81, 105, 86], \"label\": \"drone\", \"frame_id\": \"85\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/90.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [99, 79, 104, 84], \"label\": \"drone\", \"frame_id\": \"90\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/95.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [98, 78, 103, 83], \"label\": \"drone\", \"frame_id\": \"95\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/100.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [101, 86, 106, 91], \"label\": \"drone\", \"frame_id\": \"100\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/105.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [99, 79, 104, 84], \"label\": \"drone\", \"frame_id\": \"105\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/110.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [99, 78, 104, 83], \"label\": \"drone\", \"frame_id\": \"110\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/115.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [98, 79, 103, 84], \"label\": \"drone\", \"frame_id\": \"115\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/120.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [99, 84, 104, 89], \"label\": \"drone\", \"frame_id\": \"120\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/125.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [100, 86, 105, 91], \"label\": \"drone\", \"frame_id\": \"125\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/130.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [100, 84, 105, 89], \"label\": \"drone\", \"frame_id\": \"130\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/135.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [100, 82, 105, 87], \"label\": \"drone\", \"frame_id\": \"135\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/140.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [103, 84, 108, 89], \"label\": \"drone\", \"frame_id\": \"140\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/145.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [104, 83, 109, 88], \"label\": \"drone\", \"frame_id\": \"145\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/150.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [103, 81, 108, 86], \"label\": \"drone\", \"frame_id\": \"150\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/155.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [102, 86, 107, 91], \"label\": \"drone\", \"frame_id\": \"155\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/160.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [101, 84, 106, 89], \"label\": \"drone\", \"frame_id\": \"160\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/165.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [100, 83, 105, 88], \"label\": \"drone\", \"frame_id\": \"165\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/170.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [99, 91, 104, 96], \"label\": \"drone\", \"frame_id\": \"170\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/175.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [102, 89, 107, 94], \"label\": \"drone\", \"frame_id\": \"175\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/180.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [103, 87, 108, 92], \"label\": \"drone\", \"frame_id\": \"180\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/185.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [102, 89, 107, 94], \"label\": \"drone\", \"frame_id\": \"185\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/190.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [101, 89, 106, 94], \"label\": \"drone\", \"frame_id\": \"190\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/195.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [100, 87, 105, 92], \"label\": \"drone\", \"frame_id\": \"195\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/200.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [98, 88, 103, 93], \"label\": \"drone\", \"frame_id\": \"200\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/205.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [96, 89, 101, 94], \"label\": \"drone\", \"frame_id\": \"205\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/210.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [95, 87, 100, 92], \"label\": \"drone\", \"frame_id\": \"210\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/215.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [94, 86, 99, 91], \"label\": \"drone\", \"frame_id\": \"215\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/220.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [94, 92, 99, 97], \"label\": \"drone\", \"frame_id\": \"220\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/225.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [95, 90, 100, 95], \"label\": \"drone\", \"frame_id\": \"225\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/230.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [98, 88, 103, 93], \"label\": \"drone\", \"frame_id\": \"230\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/235.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [99, 91, 104, 96], \"label\": \"drone\", \"frame_id\": \"235\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/240.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [99, 89, 104, 94], \"label\": \"drone\", \"frame_id\": \"240\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/245.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [99, 87, 104, 92], \"label\": \"drone\", \"frame_id\": \"245\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/250.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [101, 92, 106, 97], \"label\": \"drone\", \"frame_id\": \"250\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/255.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [99, 85, 104, 90], \"label\": \"drone\", \"frame_id\": \"255\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/260.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [98, 86, 103, 91], \"label\": \"drone\", \"frame_id\": \"260\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/265.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [97, 90, 102, 95], \"label\": \"drone\", \"frame_id\": \"265\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/270.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [96, 90, 101, 95], \"label\": \"drone\", \"frame_id\": \"270\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/275.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [96, 89, 101, 94], \"label\": \"drone\", \"frame_id\": \"275\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/280.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [95, 89, 100, 94], \"label\": \"drone\", \"frame_id\": \"280\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/285.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [96, 89, 101, 94], \"label\": \"drone\", \"frame_id\": \"285\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/290.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [99, 88, 104, 93], \"label\": \"drone\", \"frame_id\": \"290\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/295.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [99, 88, 104, 93], \"label\": \"drone\", \"frame_id\": \"295\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/0.bmp"}, {"type": "image", "image": "dataset/images/data04/5.bmp"}, {"type": "image", "image": "dataset/images/data04/10.bmp"}, {"type": "image", "image": "dataset/images/data04/15.bmp"}, {"type": "image", "image": "dataset/images/data04/20.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data04\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [97, 78, 102, 83], \"label\": \"drone\", \"frame_id\": \"0\"}, {\"bbox\": [104, 82, 109, 87], \"label\": \"drone\", \"frame_id\": \"5\"}, {\"bbox\": [104, 79, 109, 84], \"label\": \"drone\", \"frame_id\": \"10\"}, {\"bbox\": [102, 78, 107, 83], \"label\": \"drone\", \"frame_id\": \"15\"}, {\"bbox\": [101, 85, 106, 90], \"label\": \"drone\", \"frame_id\": \"20\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/15.bmp"}, {"type": "image", "image": "dataset/images/data04/20.bmp"}, {"type": "image", "image": "dataset/images/data04/25.bmp"}, {"type": "image", "image": "dataset/images/data04/30.bmp"}, {"type": "image", "image": "dataset/images/data04/35.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data04\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [102, 78, 107, 83], \"label\": \"drone\", \"frame_id\": \"15\"}, {\"bbox\": [101, 85, 106, 90], \"label\": \"drone\", \"frame_id\": \"20\"}, {\"bbox\": [100, 82, 105, 87], \"label\": \"drone\", \"frame_id\": \"25\"}, {\"bbox\": [100, 80, 105, 85], \"label\": \"drone\", \"frame_id\": \"30\"}, {\"bbox\": [103, 84, 108, 89], \"label\": \"drone\", \"frame_id\": \"35\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/30.bmp"}, {"type": "image", "image": "dataset/images/data04/35.bmp"}, {"type": "image", "image": "dataset/images/data04/40.bmp"}, {"type": "image", "image": "dataset/images/data04/45.bmp"}, {"type": "image", "image": "dataset/images/data04/50.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data04\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [100, 80, 105, 85], \"label\": \"drone\", \"frame_id\": \"30\"}, {\"bbox\": [103, 84, 108, 89], \"label\": \"drone\", \"frame_id\": \"35\"}, {\"bbox\": [104, 83, 109, 88], \"label\": \"drone\", \"frame_id\": \"40\"}, {\"bbox\": [103, 80, 108, 85], \"label\": \"drone\", \"frame_id\": \"45\"}, {\"bbox\": [101, 83, 106, 88], \"label\": \"drone\", \"frame_id\": \"50\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/45.bmp"}, {"type": "image", "image": "dataset/images/data04/50.bmp"}, {"type": "image", "image": "dataset/images/data04/55.bmp"}, {"type": "image", "image": "dataset/images/data04/60.bmp"}, {"type": "image", "image": "dataset/images/data04/65.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data04\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [103, 80, 108, 85], \"label\": \"drone\", \"frame_id\": \"45\"}, {\"bbox\": [101, 83, 106, 88], \"label\": \"drone\", \"frame_id\": \"50\"}, {\"bbox\": [103, 82, 108, 87], \"label\": \"drone\", \"frame_id\": \"55\"}, {\"bbox\": [103, 80, 108, 85], \"label\": \"drone\", \"frame_id\": \"60\"}, {\"bbox\": [103, 79, 108, 84], \"label\": \"drone\", \"frame_id\": \"65\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/60.bmp"}, {"type": "image", "image": "dataset/images/data04/65.bmp"}, {"type": "image", "image": "dataset/images/data04/70.bmp"}, {"type": "image", "image": "dataset/images/data04/75.bmp"}, {"type": "image", "image": "dataset/images/data04/80.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data04\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [103, 80, 108, 85], \"label\": \"drone\", \"frame_id\": \"60\"}, {\"bbox\": [103, 79, 108, 84], \"label\": \"drone\", \"frame_id\": \"65\"}, {\"bbox\": [102, 84, 107, 89], \"label\": \"drone\", \"frame_id\": \"70\"}, {\"bbox\": [101, 82, 106, 87], \"label\": \"drone\", \"frame_id\": \"75\"}, {\"bbox\": [100, 80, 105, 85], \"label\": \"drone\", \"frame_id\": \"80\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/75.bmp"}, {"type": "image", "image": "dataset/images/data04/80.bmp"}, {"type": "image", "image": "dataset/images/data04/85.bmp"}, {"type": "image", "image": "dataset/images/data04/90.bmp"}, {"type": "image", "image": "dataset/images/data04/95.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data04\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [101, 82, 106, 87], \"label\": \"drone\", \"frame_id\": \"75\"}, {\"bbox\": [100, 80, 105, 85], \"label\": \"drone\", \"frame_id\": \"80\"}, {\"bbox\": [100, 81, 105, 86], \"label\": \"drone\", \"frame_id\": \"85\"}, {\"bbox\": [99, 79, 104, 84], \"label\": \"drone\", \"frame_id\": \"90\"}, {\"bbox\": [98, 78, 103, 83], \"label\": \"drone\", \"frame_id\": \"95\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/90.bmp"}, {"type": "image", "image": "dataset/images/data04/95.bmp"}, {"type": "image", "image": "dataset/images/data04/100.bmp"}, {"type": "image", "image": "dataset/images/data04/105.bmp"}, {"type": "image", "image": "dataset/images/data04/110.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data04\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [99, 79, 104, 84], \"label\": \"drone\", \"frame_id\": \"90\"}, {\"bbox\": [98, 78, 103, 83], \"label\": \"drone\", \"frame_id\": \"95\"}, {\"bbox\": [101, 86, 106, 91], \"label\": \"drone\", \"frame_id\": \"100\"}, {\"bbox\": [99, 79, 104, 84], \"label\": \"drone\", \"frame_id\": \"105\"}, {\"bbox\": [99, 78, 104, 83], \"label\": \"drone\", \"frame_id\": \"110\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/105.bmp"}, {"type": "image", "image": "dataset/images/data04/110.bmp"}, {"type": "image", "image": "dataset/images/data04/115.bmp"}, {"type": "image", "image": "dataset/images/data04/120.bmp"}, {"type": "image", "image": "dataset/images/data04/125.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data04\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [99, 79, 104, 84], \"label\": \"drone\", \"frame_id\": \"105\"}, {\"bbox\": [99, 78, 104, 83], \"label\": \"drone\", \"frame_id\": \"110\"}, {\"bbox\": [98, 79, 103, 84], \"label\": \"drone\", \"frame_id\": \"115\"}, {\"bbox\": [99, 84, 104, 89], \"label\": \"drone\", \"frame_id\": \"120\"}, {\"bbox\": [100, 86, 105, 91], \"label\": \"drone\", \"frame_id\": \"125\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/120.bmp"}, {"type": "image", "image": "dataset/images/data04/125.bmp"}, {"type": "image", "image": "dataset/images/data04/130.bmp"}, {"type": "image", "image": "dataset/images/data04/135.bmp"}, {"type": "image", "image": "dataset/images/data04/140.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data04\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [99, 84, 104, 89], \"label\": \"drone\", \"frame_id\": \"120\"}, {\"bbox\": [100, 86, 105, 91], \"label\": \"drone\", \"frame_id\": \"125\"}, {\"bbox\": [100, 84, 105, 89], \"label\": \"drone\", \"frame_id\": \"130\"}, {\"bbox\": [100, 82, 105, 87], \"label\": \"drone\", \"frame_id\": \"135\"}, {\"bbox\": [103, 84, 108, 89], \"label\": \"drone\", \"frame_id\": \"140\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/135.bmp"}, {"type": "image", "image": "dataset/images/data04/140.bmp"}, {"type": "image", "image": "dataset/images/data04/145.bmp"}, {"type": "image", "image": "dataset/images/data04/150.bmp"}, {"type": "image", "image": "dataset/images/data04/155.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data04\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [100, 82, 105, 87], \"label\": \"drone\", \"frame_id\": \"135\"}, {\"bbox\": [103, 84, 108, 89], \"label\": \"drone\", \"frame_id\": \"140\"}, {\"bbox\": [104, 83, 109, 88], \"label\": \"drone\", \"frame_id\": \"145\"}, {\"bbox\": [103, 81, 108, 86], \"label\": \"drone\", \"frame_id\": \"150\"}, {\"bbox\": [102, 86, 107, 91], \"label\": \"drone\", \"frame_id\": \"155\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/150.bmp"}, {"type": "image", "image": "dataset/images/data04/155.bmp"}, {"type": "image", "image": "dataset/images/data04/160.bmp"}, {"type": "image", "image": "dataset/images/data04/165.bmp"}, {"type": "image", "image": "dataset/images/data04/170.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data04\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [103, 81, 108, 86], \"label\": \"drone\", \"frame_id\": \"150\"}, {\"bbox\": [102, 86, 107, 91], \"label\": \"drone\", \"frame_id\": \"155\"}, {\"bbox\": [101, 84, 106, 89], \"label\": \"drone\", \"frame_id\": \"160\"}, {\"bbox\": [100, 83, 105, 88], \"label\": \"drone\", \"frame_id\": \"165\"}, {\"bbox\": [99, 91, 104, 96], \"label\": \"drone\", \"frame_id\": \"170\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/165.bmp"}, {"type": "image", "image": "dataset/images/data04/170.bmp"}, {"type": "image", "image": "dataset/images/data04/175.bmp"}, {"type": "image", "image": "dataset/images/data04/180.bmp"}, {"type": "image", "image": "dataset/images/data04/185.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data04\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [100, 83, 105, 88], \"label\": \"drone\", \"frame_id\": \"165\"}, {\"bbox\": [99, 91, 104, 96], \"label\": \"drone\", \"frame_id\": \"170\"}, {\"bbox\": [102, 89, 107, 94], \"label\": \"drone\", \"frame_id\": \"175\"}, {\"bbox\": [103, 87, 108, 92], \"label\": \"drone\", \"frame_id\": \"180\"}, {\"bbox\": [102, 89, 107, 94], \"label\": \"drone\", \"frame_id\": \"185\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/180.bmp"}, {"type": "image", "image": "dataset/images/data04/185.bmp"}, {"type": "image", "image": "dataset/images/data04/190.bmp"}, {"type": "image", "image": "dataset/images/data04/195.bmp"}, {"type": "image", "image": "dataset/images/data04/200.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data04\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [103, 87, 108, 92], \"label\": \"drone\", \"frame_id\": \"180\"}, {\"bbox\": [102, 89, 107, 94], \"label\": \"drone\", \"frame_id\": \"185\"}, {\"bbox\": [101, 89, 106, 94], \"label\": \"drone\", \"frame_id\": \"190\"}, {\"bbox\": [100, 87, 105, 92], \"label\": \"drone\", \"frame_id\": \"195\"}, {\"bbox\": [98, 88, 103, 93], \"label\": \"drone\", \"frame_id\": \"200\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/195.bmp"}, {"type": "image", "image": "dataset/images/data04/200.bmp"}, {"type": "image", "image": "dataset/images/data04/205.bmp"}, {"type": "image", "image": "dataset/images/data04/210.bmp"}, {"type": "image", "image": "dataset/images/data04/215.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data04\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [100, 87, 105, 92], \"label\": \"drone\", \"frame_id\": \"195\"}, {\"bbox\": [98, 88, 103, 93], \"label\": \"drone\", \"frame_id\": \"200\"}, {\"bbox\": [96, 89, 101, 94], \"label\": \"drone\", \"frame_id\": \"205\"}, {\"bbox\": [95, 87, 100, 92], \"label\": \"drone\", \"frame_id\": \"210\"}, {\"bbox\": [94, 86, 99, 91], \"label\": \"drone\", \"frame_id\": \"215\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/210.bmp"}, {"type": "image", "image": "dataset/images/data04/215.bmp"}, {"type": "image", "image": "dataset/images/data04/220.bmp"}, {"type": "image", "image": "dataset/images/data04/225.bmp"}, {"type": "image", "image": "dataset/images/data04/230.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data04\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [95, 87, 100, 92], \"label\": \"drone\", \"frame_id\": \"210\"}, {\"bbox\": [94, 86, 99, 91], \"label\": \"drone\", \"frame_id\": \"215\"}, {\"bbox\": [94, 92, 99, 97], \"label\": \"drone\", \"frame_id\": \"220\"}, {\"bbox\": [95, 90, 100, 95], \"label\": \"drone\", \"frame_id\": \"225\"}, {\"bbox\": [98, 88, 103, 93], \"label\": \"drone\", \"frame_id\": \"230\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/225.bmp"}, {"type": "image", "image": "dataset/images/data04/230.bmp"}, {"type": "image", "image": "dataset/images/data04/235.bmp"}, {"type": "image", "image": "dataset/images/data04/240.bmp"}, {"type": "image", "image": "dataset/images/data04/245.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data04\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [95, 90, 100, 95], \"label\": \"drone\", \"frame_id\": \"225\"}, {\"bbox\": [98, 88, 103, 93], \"label\": \"drone\", \"frame_id\": \"230\"}, {\"bbox\": [99, 91, 104, 96], \"label\": \"drone\", \"frame_id\": \"235\"}, {\"bbox\": [99, 89, 104, 94], \"label\": \"drone\", \"frame_id\": \"240\"}, {\"bbox\": [99, 87, 104, 92], \"label\": \"drone\", \"frame_id\": \"245\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/240.bmp"}, {"type": "image", "image": "dataset/images/data04/245.bmp"}, {"type": "image", "image": "dataset/images/data04/250.bmp"}, {"type": "image", "image": "dataset/images/data04/255.bmp"}, {"type": "image", "image": "dataset/images/data04/260.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data04\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [99, 89, 104, 94], \"label\": \"drone\", \"frame_id\": \"240\"}, {\"bbox\": [99, 87, 104, 92], \"label\": \"drone\", \"frame_id\": \"245\"}, {\"bbox\": [101, 92, 106, 97], \"label\": \"drone\", \"frame_id\": \"250\"}, {\"bbox\": [99, 85, 104, 90], \"label\": \"drone\", \"frame_id\": \"255\"}, {\"bbox\": [98, 86, 103, 91], \"label\": \"drone\", \"frame_id\": \"260\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/255.bmp"}, {"type": "image", "image": "dataset/images/data04/260.bmp"}, {"type": "image", "image": "dataset/images/data04/265.bmp"}, {"type": "image", "image": "dataset/images/data04/270.bmp"}, {"type": "image", "image": "dataset/images/data04/275.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data04\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [99, 85, 104, 90], \"label\": \"drone\", \"frame_id\": \"255\"}, {\"bbox\": [98, 86, 103, 91], \"label\": \"drone\", \"frame_id\": \"260\"}, {\"bbox\": [97, 90, 102, 95], \"label\": \"drone\", \"frame_id\": \"265\"}, {\"bbox\": [96, 90, 101, 95], \"label\": \"drone\", \"frame_id\": \"270\"}, {\"bbox\": [96, 89, 101, 94], \"label\": \"drone\", \"frame_id\": \"275\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data04/270.bmp"}, {"type": "image", "image": "dataset/images/data04/275.bmp"}, {"type": "image", "image": "dataset/images/data04/280.bmp"}, {"type": "image", "image": "dataset/images/data04/285.bmp"}, {"type": "image", "image": "dataset/images/data04/290.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data04\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [96, 90, 101, 95], \"label\": \"drone\", \"frame_id\": \"270\"}, {\"bbox\": [96, 89, 101, 94], \"label\": \"drone\", \"frame_id\": \"275\"}, {\"bbox\": [95, 89, 100, 94], \"label\": \"drone\", \"frame_id\": \"280\"}, {\"bbox\": [96, 89, 101, 94], \"label\": \"drone\", \"frame_id\": \"285\"}, {\"bbox\": [99, 88, 104, 93], \"label\": \"drone\", \"frame_id\": \"290\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/0.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [92, 123, 97, 128], \"label\": \"drone\", \"frame_id\": \"0\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/5.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [90, 124, 95, 129], \"label\": \"drone\", \"frame_id\": \"5\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/10.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [90, 122, 95, 127], \"label\": \"drone\", \"frame_id\": \"10\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/15.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [89, 123, 94, 128], \"label\": \"drone\", \"frame_id\": \"15\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/20.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [90, 124, 95, 129], \"label\": \"drone\", \"frame_id\": \"20\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/25.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [91, 122, 96, 127], \"label\": \"drone\", \"frame_id\": \"25\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/30.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [91, 123, 96, 128], \"label\": \"drone\", \"frame_id\": \"30\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/35.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [90, 124, 95, 129], \"label\": \"drone\", \"frame_id\": \"35\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/40.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [90, 121, 95, 126], \"label\": \"drone\", \"frame_id\": \"40\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/45.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [93, 120, 98, 125], \"label\": \"drone\", \"frame_id\": \"45\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/50.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [93, 121, 98, 126], \"label\": \"drone\", \"frame_id\": \"50\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/55.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [93, 122, 98, 127], \"label\": \"drone\", \"frame_id\": \"55\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/60.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [19, 162, 24, 167], \"label\": \"drone\", \"frame_id\": \"60\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/65.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [80, 117, 85, 122], \"label\": \"drone\", \"frame_id\": \"65\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/70.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [95, 120, 100, 125], \"label\": \"drone\", \"frame_id\": \"70\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/75.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [94, 121, 99, 126], \"label\": \"drone\", \"frame_id\": \"75\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/80.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [94, 122, 99, 127], \"label\": \"drone\", \"frame_id\": \"80\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/85.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [96, 123, 101, 128], \"label\": \"drone\", \"frame_id\": \"85\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/90.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [95, 124, 100, 129], \"label\": \"drone\", \"frame_id\": \"90\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/95.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [93, 124, 98, 129], \"label\": \"drone\", \"frame_id\": \"95\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/100.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [92, 123, 97, 128], \"label\": \"drone\", \"frame_id\": \"100\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/105.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [90, 124, 95, 129], \"label\": \"drone\", \"frame_id\": \"105\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/110.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [89, 124, 94, 129], \"label\": \"drone\", \"frame_id\": \"110\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/115.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [87, 125, 92, 130], \"label\": \"drone\", \"frame_id\": \"115\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/120.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [91, 124, 96, 129], \"label\": \"drone\", \"frame_id\": \"120\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/125.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [89, 125, 94, 130], \"label\": \"drone\", \"frame_id\": \"125\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/130.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [88, 124, 93, 129], \"label\": \"drone\", \"frame_id\": \"130\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/135.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [90, 124, 95, 129], \"label\": \"drone\", \"frame_id\": \"135\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/140.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [90, 125, 95, 130], \"label\": \"drone\", \"frame_id\": \"140\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/145.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [87, 126, 92, 131], \"label\": \"drone\", \"frame_id\": \"145\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/150.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [91, 125, 96, 130], \"label\": \"drone\", \"frame_id\": \"150\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/155.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [92, 126, 97, 131], \"label\": \"drone\", \"frame_id\": \"155\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/160.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [91, 127, 96, 132], \"label\": \"drone\", \"frame_id\": \"160\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/165.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [91, 125, 96, 130], \"label\": \"drone\", \"frame_id\": \"165\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/170.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [92, 126, 97, 131], \"label\": \"drone\", \"frame_id\": \"170\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/175.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [90, 126, 95, 131], \"label\": \"drone\", \"frame_id\": \"175\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/180.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [90, 124, 95, 129], \"label\": \"drone\", \"frame_id\": \"180\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/185.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [90, 125, 95, 130], \"label\": \"drone\", \"frame_id\": \"185\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/190.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [89, 125, 94, 130], \"label\": \"drone\", \"frame_id\": \"190\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/195.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [88, 125, 93, 130], \"label\": \"drone\", \"frame_id\": \"195\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/200.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [89, 125, 94, 130], \"label\": \"drone\", \"frame_id\": \"200\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/205.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [81, 129, 86, 134], \"label\": \"drone\", \"frame_id\": \"205\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/210.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [85, 125, 90, 130], \"label\": \"drone\", \"frame_id\": \"210\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/215.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [90, 124, 95, 129], \"label\": \"drone\", \"frame_id\": \"215\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/220.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [88, 125, 93, 130], \"label\": \"drone\", \"frame_id\": \"220\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/225.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [87, 126, 92, 131], \"label\": \"drone\", \"frame_id\": \"225\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/230.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [93, 124, 98, 129], \"label\": \"drone\", \"frame_id\": \"230\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/235.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [92, 125, 97, 130], \"label\": \"drone\", \"frame_id\": \"235\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/240.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [91, 127, 96, 132], \"label\": \"drone\", \"frame_id\": \"240\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/245.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [88, 124, 93, 129], \"label\": \"drone\", \"frame_id\": \"245\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/250.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [86, 125, 91, 130], \"label\": \"drone\", \"frame_id\": \"250\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/255.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [84, 127, 89, 132], \"label\": \"drone\", \"frame_id\": \"255\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/260.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [86, 125, 91, 130], \"label\": \"drone\", \"frame_id\": \"260\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/265.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [88, 126, 93, 131], \"label\": \"drone\", \"frame_id\": \"265\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/270.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [87, 127, 92, 132], \"label\": \"drone\", \"frame_id\": \"270\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/275.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [90, 125, 95, 130], \"label\": \"drone\", \"frame_id\": \"275\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/280.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [92, 126, 97, 131], \"label\": \"drone\", \"frame_id\": \"280\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/285.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [90, 127, 95, 132], \"label\": \"drone\", \"frame_id\": \"285\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/290.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [88, 126, 93, 131], \"label\": \"drone\", \"frame_id\": \"290\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/295.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [88, 126, 93, 131], \"label\": \"drone\", \"frame_id\": \"295\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/0.bmp"}, {"type": "image", "image": "dataset/images/data06/5.bmp"}, {"type": "image", "image": "dataset/images/data06/10.bmp"}, {"type": "image", "image": "dataset/images/data06/15.bmp"}, {"type": "image", "image": "dataset/images/data06/20.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data06\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [92, 123, 97, 128], \"label\": \"drone\", \"frame_id\": \"0\"}, {\"bbox\": [90, 124, 95, 129], \"label\": \"drone\", \"frame_id\": \"5\"}, {\"bbox\": [90, 122, 95, 127], \"label\": \"drone\", \"frame_id\": \"10\"}, {\"bbox\": [89, 123, 94, 128], \"label\": \"drone\", \"frame_id\": \"15\"}, {\"bbox\": [90, 124, 95, 129], \"label\": \"drone\", \"frame_id\": \"20\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/15.bmp"}, {"type": "image", "image": "dataset/images/data06/20.bmp"}, {"type": "image", "image": "dataset/images/data06/25.bmp"}, {"type": "image", "image": "dataset/images/data06/30.bmp"}, {"type": "image", "image": "dataset/images/data06/35.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data06\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [89, 123, 94, 128], \"label\": \"drone\", \"frame_id\": \"15\"}, {\"bbox\": [90, 124, 95, 129], \"label\": \"drone\", \"frame_id\": \"20\"}, {\"bbox\": [91, 122, 96, 127], \"label\": \"drone\", \"frame_id\": \"25\"}, {\"bbox\": [91, 123, 96, 128], \"label\": \"drone\", \"frame_id\": \"30\"}, {\"bbox\": [90, 124, 95, 129], \"label\": \"drone\", \"frame_id\": \"35\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/30.bmp"}, {"type": "image", "image": "dataset/images/data06/35.bmp"}, {"type": "image", "image": "dataset/images/data06/40.bmp"}, {"type": "image", "image": "dataset/images/data06/45.bmp"}, {"type": "image", "image": "dataset/images/data06/50.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data06\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [91, 123, 96, 128], \"label\": \"drone\", \"frame_id\": \"30\"}, {\"bbox\": [90, 124, 95, 129], \"label\": \"drone\", \"frame_id\": \"35\"}, {\"bbox\": [90, 121, 95, 126], \"label\": \"drone\", \"frame_id\": \"40\"}, {\"bbox\": [93, 120, 98, 125], \"label\": \"drone\", \"frame_id\": \"45\"}, {\"bbox\": [93, 121, 98, 126], \"label\": \"drone\", \"frame_id\": \"50\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/45.bmp"}, {"type": "image", "image": "dataset/images/data06/50.bmp"}, {"type": "image", "image": "dataset/images/data06/55.bmp"}, {"type": "image", "image": "dataset/images/data06/60.bmp"}, {"type": "image", "image": "dataset/images/data06/65.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data06\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [93, 120, 98, 125], \"label\": \"drone\", \"frame_id\": \"45\"}, {\"bbox\": [93, 121, 98, 126], \"label\": \"drone\", \"frame_id\": \"50\"}, {\"bbox\": [93, 122, 98, 127], \"label\": \"drone\", \"frame_id\": \"55\"}, {\"bbox\": [19, 162, 24, 167], \"label\": \"drone\", \"frame_id\": \"60\"}, {\"bbox\": [80, 117, 85, 122], \"label\": \"drone\", \"frame_id\": \"65\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/60.bmp"}, {"type": "image", "image": "dataset/images/data06/65.bmp"}, {"type": "image", "image": "dataset/images/data06/70.bmp"}, {"type": "image", "image": "dataset/images/data06/75.bmp"}, {"type": "image", "image": "dataset/images/data06/80.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data06\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [19, 162, 24, 167], \"label\": \"drone\", \"frame_id\": \"60\"}, {\"bbox\": [80, 117, 85, 122], \"label\": \"drone\", \"frame_id\": \"65\"}, {\"bbox\": [95, 120, 100, 125], \"label\": \"drone\", \"frame_id\": \"70\"}, {\"bbox\": [94, 121, 99, 126], \"label\": \"drone\", \"frame_id\": \"75\"}, {\"bbox\": [94, 122, 99, 127], \"label\": \"drone\", \"frame_id\": \"80\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/75.bmp"}, {"type": "image", "image": "dataset/images/data06/80.bmp"}, {"type": "image", "image": "dataset/images/data06/85.bmp"}, {"type": "image", "image": "dataset/images/data06/90.bmp"}, {"type": "image", "image": "dataset/images/data06/95.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data06\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [94, 121, 99, 126], \"label\": \"drone\", \"frame_id\": \"75\"}, {\"bbox\": [94, 122, 99, 127], \"label\": \"drone\", \"frame_id\": \"80\"}, {\"bbox\": [96, 123, 101, 128], \"label\": \"drone\", \"frame_id\": \"85\"}, {\"bbox\": [95, 124, 100, 129], \"label\": \"drone\", \"frame_id\": \"90\"}, {\"bbox\": [93, 124, 98, 129], \"label\": \"drone\", \"frame_id\": \"95\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/90.bmp"}, {"type": "image", "image": "dataset/images/data06/95.bmp"}, {"type": "image", "image": "dataset/images/data06/100.bmp"}, {"type": "image", "image": "dataset/images/data06/105.bmp"}, {"type": "image", "image": "dataset/images/data06/110.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data06\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [95, 124, 100, 129], \"label\": \"drone\", \"frame_id\": \"90\"}, {\"bbox\": [93, 124, 98, 129], \"label\": \"drone\", \"frame_id\": \"95\"}, {\"bbox\": [92, 123, 97, 128], \"label\": \"drone\", \"frame_id\": \"100\"}, {\"bbox\": [90, 124, 95, 129], \"label\": \"drone\", \"frame_id\": \"105\"}, {\"bbox\": [89, 124, 94, 129], \"label\": \"drone\", \"frame_id\": \"110\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/105.bmp"}, {"type": "image", "image": "dataset/images/data06/110.bmp"}, {"type": "image", "image": "dataset/images/data06/115.bmp"}, {"type": "image", "image": "dataset/images/data06/120.bmp"}, {"type": "image", "image": "dataset/images/data06/125.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data06\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [90, 124, 95, 129], \"label\": \"drone\", \"frame_id\": \"105\"}, {\"bbox\": [89, 124, 94, 129], \"label\": \"drone\", \"frame_id\": \"110\"}, {\"bbox\": [87, 125, 92, 130], \"label\": \"drone\", \"frame_id\": \"115\"}, {\"bbox\": [91, 124, 96, 129], \"label\": \"drone\", \"frame_id\": \"120\"}, {\"bbox\": [89, 125, 94, 130], \"label\": \"drone\", \"frame_id\": \"125\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/120.bmp"}, {"type": "image", "image": "dataset/images/data06/125.bmp"}, {"type": "image", "image": "dataset/images/data06/130.bmp"}, {"type": "image", "image": "dataset/images/data06/135.bmp"}, {"type": "image", "image": "dataset/images/data06/140.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data06\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [91, 124, 96, 129], \"label\": \"drone\", \"frame_id\": \"120\"}, {\"bbox\": [89, 125, 94, 130], \"label\": \"drone\", \"frame_id\": \"125\"}, {\"bbox\": [88, 124, 93, 129], \"label\": \"drone\", \"frame_id\": \"130\"}, {\"bbox\": [90, 124, 95, 129], \"label\": \"drone\", \"frame_id\": \"135\"}, {\"bbox\": [90, 125, 95, 130], \"label\": \"drone\", \"frame_id\": \"140\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/135.bmp"}, {"type": "image", "image": "dataset/images/data06/140.bmp"}, {"type": "image", "image": "dataset/images/data06/145.bmp"}, {"type": "image", "image": "dataset/images/data06/150.bmp"}, {"type": "image", "image": "dataset/images/data06/155.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data06\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [90, 124, 95, 129], \"label\": \"drone\", \"frame_id\": \"135\"}, {\"bbox\": [90, 125, 95, 130], \"label\": \"drone\", \"frame_id\": \"140\"}, {\"bbox\": [87, 126, 92, 131], \"label\": \"drone\", \"frame_id\": \"145\"}, {\"bbox\": [91, 125, 96, 130], \"label\": \"drone\", \"frame_id\": \"150\"}, {\"bbox\": [92, 126, 97, 131], \"label\": \"drone\", \"frame_id\": \"155\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/150.bmp"}, {"type": "image", "image": "dataset/images/data06/155.bmp"}, {"type": "image", "image": "dataset/images/data06/160.bmp"}, {"type": "image", "image": "dataset/images/data06/165.bmp"}, {"type": "image", "image": "dataset/images/data06/170.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data06\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [91, 125, 96, 130], \"label\": \"drone\", \"frame_id\": \"150\"}, {\"bbox\": [92, 126, 97, 131], \"label\": \"drone\", \"frame_id\": \"155\"}, {\"bbox\": [91, 127, 96, 132], \"label\": \"drone\", \"frame_id\": \"160\"}, {\"bbox\": [91, 125, 96, 130], \"label\": \"drone\", \"frame_id\": \"165\"}, {\"bbox\": [92, 126, 97, 131], \"label\": \"drone\", \"frame_id\": \"170\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/165.bmp"}, {"type": "image", "image": "dataset/images/data06/170.bmp"}, {"type": "image", "image": "dataset/images/data06/175.bmp"}, {"type": "image", "image": "dataset/images/data06/180.bmp"}, {"type": "image", "image": "dataset/images/data06/185.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data06\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [91, 125, 96, 130], \"label\": \"drone\", \"frame_id\": \"165\"}, {\"bbox\": [92, 126, 97, 131], \"label\": \"drone\", \"frame_id\": \"170\"}, {\"bbox\": [90, 126, 95, 131], \"label\": \"drone\", \"frame_id\": \"175\"}, {\"bbox\": [90, 124, 95, 129], \"label\": \"drone\", \"frame_id\": \"180\"}, {\"bbox\": [90, 125, 95, 130], \"label\": \"drone\", \"frame_id\": \"185\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/180.bmp"}, {"type": "image", "image": "dataset/images/data06/185.bmp"}, {"type": "image", "image": "dataset/images/data06/190.bmp"}, {"type": "image", "image": "dataset/images/data06/195.bmp"}, {"type": "image", "image": "dataset/images/data06/200.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data06\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [90, 124, 95, 129], \"label\": \"drone\", \"frame_id\": \"180\"}, {\"bbox\": [90, 125, 95, 130], \"label\": \"drone\", \"frame_id\": \"185\"}, {\"bbox\": [89, 125, 94, 130], \"label\": \"drone\", \"frame_id\": \"190\"}, {\"bbox\": [88, 125, 93, 130], \"label\": \"drone\", \"frame_id\": \"195\"}, {\"bbox\": [89, 125, 94, 130], \"label\": \"drone\", \"frame_id\": \"200\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/195.bmp"}, {"type": "image", "image": "dataset/images/data06/200.bmp"}, {"type": "image", "image": "dataset/images/data06/205.bmp"}, {"type": "image", "image": "dataset/images/data06/210.bmp"}, {"type": "image", "image": "dataset/images/data06/215.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data06\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [88, 125, 93, 130], \"label\": \"drone\", \"frame_id\": \"195\"}, {\"bbox\": [89, 125, 94, 130], \"label\": \"drone\", \"frame_id\": \"200\"}, {\"bbox\": [81, 129, 86, 134], \"label\": \"drone\", \"frame_id\": \"205\"}, {\"bbox\": [85, 125, 90, 130], \"label\": \"drone\", \"frame_id\": \"210\"}, {\"bbox\": [90, 124, 95, 129], \"label\": \"drone\", \"frame_id\": \"215\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/210.bmp"}, {"type": "image", "image": "dataset/images/data06/215.bmp"}, {"type": "image", "image": "dataset/images/data06/220.bmp"}, {"type": "image", "image": "dataset/images/data06/225.bmp"}, {"type": "image", "image": "dataset/images/data06/230.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data06\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [85, 125, 90, 130], \"label\": \"drone\", \"frame_id\": \"210\"}, {\"bbox\": [90, 124, 95, 129], \"label\": \"drone\", \"frame_id\": \"215\"}, {\"bbox\": [88, 125, 93, 130], \"label\": \"drone\", \"frame_id\": \"220\"}, {\"bbox\": [87, 126, 92, 131], \"label\": \"drone\", \"frame_id\": \"225\"}, {\"bbox\": [93, 124, 98, 129], \"label\": \"drone\", \"frame_id\": \"230\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/225.bmp"}, {"type": "image", "image": "dataset/images/data06/230.bmp"}, {"type": "image", "image": "dataset/images/data06/235.bmp"}, {"type": "image", "image": "dataset/images/data06/240.bmp"}, {"type": "image", "image": "dataset/images/data06/245.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data06\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [87, 126, 92, 131], \"label\": \"drone\", \"frame_id\": \"225\"}, {\"bbox\": [93, 124, 98, 129], \"label\": \"drone\", \"frame_id\": \"230\"}, {\"bbox\": [92, 125, 97, 130], \"label\": \"drone\", \"frame_id\": \"235\"}, {\"bbox\": [91, 127, 96, 132], \"label\": \"drone\", \"frame_id\": \"240\"}, {\"bbox\": [88, 124, 93, 129], \"label\": \"drone\", \"frame_id\": \"245\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/240.bmp"}, {"type": "image", "image": "dataset/images/data06/245.bmp"}, {"type": "image", "image": "dataset/images/data06/250.bmp"}, {"type": "image", "image": "dataset/images/data06/255.bmp"}, {"type": "image", "image": "dataset/images/data06/260.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data06\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [91, 127, 96, 132], \"label\": \"drone\", \"frame_id\": \"240\"}, {\"bbox\": [88, 124, 93, 129], \"label\": \"drone\", \"frame_id\": \"245\"}, {\"bbox\": [86, 125, 91, 130], \"label\": \"drone\", \"frame_id\": \"250\"}, {\"bbox\": [84, 127, 89, 132], \"label\": \"drone\", \"frame_id\": \"255\"}, {\"bbox\": [86, 125, 91, 130], \"label\": \"drone\", \"frame_id\": \"260\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/255.bmp"}, {"type": "image", "image": "dataset/images/data06/260.bmp"}, {"type": "image", "image": "dataset/images/data06/265.bmp"}, {"type": "image", "image": "dataset/images/data06/270.bmp"}, {"type": "image", "image": "dataset/images/data06/275.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data06\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [84, 127, 89, 132], \"label\": \"drone\", \"frame_id\": \"255\"}, {\"bbox\": [86, 125, 91, 130], \"label\": \"drone\", \"frame_id\": \"260\"}, {\"bbox\": [88, 126, 93, 131], \"label\": \"drone\", \"frame_id\": \"265\"}, {\"bbox\": [87, 127, 92, 132], \"label\": \"drone\", \"frame_id\": \"270\"}, {\"bbox\": [90, 125, 95, 130], \"label\": \"drone\", \"frame_id\": \"275\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/270.bmp"}, {"type": "image", "image": "dataset/images/data06/275.bmp"}, {"type": "image", "image": "dataset/images/data06/280.bmp"}, {"type": "image", "image": "dataset/images/data06/285.bmp"}, {"type": "image", "image": "dataset/images/data06/290.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data06\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [87, 127, 92, 132], \"label\": \"drone\", \"frame_id\": \"270\"}, {\"bbox\": [90, 125, 95, 130], \"label\": \"drone\", \"frame_id\": \"275\"}, {\"bbox\": [92, 126, 97, 131], \"label\": \"drone\", \"frame_id\": \"280\"}, {\"bbox\": [90, 127, 95, 132], \"label\": \"drone\", \"frame_id\": \"285\"}, {\"bbox\": [88, 126, 93, 131], \"label\": \"drone\", \"frame_id\": \"290\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/0.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [164, 137, 194, 168], \"label\": \"drone\", \"frame_id\": \"0\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/5.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [160, 128, 190, 158], \"label\": \"drone\", \"frame_id\": \"5\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/10.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [148, 117, 179, 148], \"label\": \"drone\", \"frame_id\": \"10\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/15.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [145, 108, 176, 139], \"label\": \"drone\", \"frame_id\": \"15\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/20.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [143, 98, 173, 128], \"label\": \"drone\", \"frame_id\": \"20\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/25.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [140, 89, 170, 120], \"label\": \"drone\", \"frame_id\": \"25\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/30.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [136, 80, 167, 110], \"label\": \"drone\", \"frame_id\": \"30\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/35.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [132, 71, 163, 101], \"label\": \"drone\", \"frame_id\": \"35\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/40.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [128, 60, 159, 91], \"label\": \"drone\", \"frame_id\": \"40\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/45.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [124, 51, 155, 82], \"label\": \"drone\", \"frame_id\": \"45\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/50.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [119, 42, 149, 72], \"label\": \"drone\", \"frame_id\": \"50\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/55.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [113, 34, 144, 64], \"label\": \"drone\", \"frame_id\": \"55\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/60.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [108, 24, 138, 55], \"label\": \"drone\", \"frame_id\": \"60\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/65.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [103, 15, 133, 46], \"label\": \"drone\", \"frame_id\": \"65\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/70.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [100, 24, 130, 55], \"label\": \"drone\", \"frame_id\": \"70\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/75.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [97, 24, 128, 54], \"label\": \"drone\", \"frame_id\": \"75\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/80.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [92, 15, 123, 45], \"label\": \"drone\", \"frame_id\": \"80\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/85.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [87, 5, 117, 36], \"label\": \"drone\", \"frame_id\": \"85\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/90.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [89, 24, 120, 55], \"label\": \"drone\", \"frame_id\": \"90\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/95.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [84, 15, 115, 46], \"label\": \"drone\", \"frame_id\": \"95\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/100.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [77, 7, 108, 37], \"label\": \"drone\", \"frame_id\": \"100\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/105.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [84, 32, 114, 62], \"label\": \"drone\", \"frame_id\": \"105\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/110.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [80, 23, 110, 53], \"label\": \"drone\", \"frame_id\": \"110\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/115.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [74, 15, 104, 45], \"label\": \"drone\", \"frame_id\": \"115\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/120.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [88, 33, 119, 64], \"label\": \"drone\", \"frame_id\": \"120\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/125.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [82, 25, 112, 56], \"label\": \"drone\", \"frame_id\": \"125\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/130.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [76, 18, 106, 48], \"label\": \"drone\", \"frame_id\": \"130\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/135.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [63, 17, 94, 48], \"label\": \"drone\", \"frame_id\": \"135\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/140.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [63, 37, 94, 68], \"label\": \"drone\", \"frame_id\": \"140\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/145.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [59, 32, 90, 63], \"label\": \"drone\", \"frame_id\": \"145\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/150.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [79, 50, 109, 80], \"label\": \"drone\", \"frame_id\": \"150\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/155.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [72, 44, 103, 75], \"label\": \"drone\", \"frame_id\": \"155\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/160.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [64, 40, 95, 70], \"label\": \"drone\", \"frame_id\": \"160\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/165.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [60, 39, 91, 69], \"label\": \"drone\", \"frame_id\": \"165\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/170.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [88, 55, 119, 85], \"label\": \"drone\", \"frame_id\": \"170\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/175.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [81, 49, 112, 80], \"label\": \"drone\", \"frame_id\": \"175\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/180.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [76, 44, 106, 75], \"label\": \"drone\", \"frame_id\": \"180\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/185.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [82, 57, 112, 88], \"label\": \"drone\", \"frame_id\": \"185\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/190.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [76, 53, 107, 84], \"label\": \"drone\", \"frame_id\": \"190\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/195.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [71, 48, 101, 79], \"label\": \"drone\", \"frame_id\": \"195\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/200.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [80, 61, 111, 92], \"label\": \"drone\", \"frame_id\": \"200\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/205.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [74, 56, 104, 87], \"label\": \"drone\", \"frame_id\": \"205\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/210.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [69, 52, 100, 83], \"label\": \"drone\", \"frame_id\": \"210\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/215.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [84, 66, 115, 96], \"label\": \"drone\", \"frame_id\": \"215\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/220.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [79, 63, 109, 93], \"label\": \"drone\", \"frame_id\": \"220\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/225.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [72, 58, 102, 88], \"label\": \"drone\", \"frame_id\": \"225\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/230.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [73, 59, 104, 90], \"label\": \"drone\", \"frame_id\": \"230\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/235.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [82, 59, 112, 90], \"label\": \"drone\", \"frame_id\": \"235\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/240.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [79, 56, 109, 87], \"label\": \"drone\", \"frame_id\": \"240\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/245.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [80, 64, 110, 95], \"label\": \"drone\", \"frame_id\": \"245\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/250.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [76, 61, 106, 92], \"label\": \"drone\", \"frame_id\": \"250\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/255.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [69, 57, 100, 88], \"label\": \"drone\", \"frame_id\": \"255\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/260.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [64, 53, 95, 84], \"label\": \"drone\", \"frame_id\": \"260\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/265.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [81, 63, 112, 93], \"label\": \"drone\", \"frame_id\": \"265\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/270.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [76, 58, 106, 88], \"label\": \"drone\", \"frame_id\": \"270\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/275.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [71, 53, 101, 84], \"label\": \"drone\", \"frame_id\": \"275\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/280.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [82, 63, 112, 93], \"label\": \"drone\", \"frame_id\": \"280\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/285.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [68, 49, 98, 80], \"label\": \"drone\", \"frame_id\": \"285\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/290.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [79, 57, 109, 88], \"label\": \"drone\", \"frame_id\": \"290\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/295.bmp"}, {"type": "text", "text": "检测红外图像中的弱小目标。\n\n目标特征：\n- 尺寸小（5-20像素）\n- 在红外图像中为亮点或暗点\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 精确定位每个目标\n2. 边界框紧贴目标\n3. 坐标格式[x1,y1,x2,y2]\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [73, 53, 104, 84], \"label\": \"drone\", \"frame_id\": \"295\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/0.bmp"}, {"type": "image", "image": "dataset/images/data07/5.bmp"}, {"type": "image", "image": "dataset/images/data07/10.bmp"}, {"type": "image", "image": "dataset/images/data07/15.bmp"}, {"type": "image", "image": "dataset/images/data07/20.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data07\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [164, 137, 194, 168], \"label\": \"drone\", \"frame_id\": \"0\"}, {\"bbox\": [160, 128, 190, 158], \"label\": \"drone\", \"frame_id\": \"5\"}, {\"bbox\": [148, 117, 179, 148], \"label\": \"drone\", \"frame_id\": \"10\"}, {\"bbox\": [145, 108, 176, 139], \"label\": \"drone\", \"frame_id\": \"15\"}, {\"bbox\": [143, 98, 173, 128], \"label\": \"drone\", \"frame_id\": \"20\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/15.bmp"}, {"type": "image", "image": "dataset/images/data07/20.bmp"}, {"type": "image", "image": "dataset/images/data07/25.bmp"}, {"type": "image", "image": "dataset/images/data07/30.bmp"}, {"type": "image", "image": "dataset/images/data07/35.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data07\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [145, 108, 176, 139], \"label\": \"drone\", \"frame_id\": \"15\"}, {\"bbox\": [143, 98, 173, 128], \"label\": \"drone\", \"frame_id\": \"20\"}, {\"bbox\": [140, 89, 170, 120], \"label\": \"drone\", \"frame_id\": \"25\"}, {\"bbox\": [136, 80, 167, 110], \"label\": \"drone\", \"frame_id\": \"30\"}, {\"bbox\": [132, 71, 163, 101], \"label\": \"drone\", \"frame_id\": \"35\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/30.bmp"}, {"type": "image", "image": "dataset/images/data07/35.bmp"}, {"type": "image", "image": "dataset/images/data07/40.bmp"}, {"type": "image", "image": "dataset/images/data07/45.bmp"}, {"type": "image", "image": "dataset/images/data07/50.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data07\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [136, 80, 167, 110], \"label\": \"drone\", \"frame_id\": \"30\"}, {\"bbox\": [132, 71, 163, 101], \"label\": \"drone\", \"frame_id\": \"35\"}, {\"bbox\": [128, 60, 159, 91], \"label\": \"drone\", \"frame_id\": \"40\"}, {\"bbox\": [124, 51, 155, 82], \"label\": \"drone\", \"frame_id\": \"45\"}, {\"bbox\": [119, 42, 149, 72], \"label\": \"drone\", \"frame_id\": \"50\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/45.bmp"}, {"type": "image", "image": "dataset/images/data07/50.bmp"}, {"type": "image", "image": "dataset/images/data07/55.bmp"}, {"type": "image", "image": "dataset/images/data07/60.bmp"}, {"type": "image", "image": "dataset/images/data07/65.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data07\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [124, 51, 155, 82], \"label\": \"drone\", \"frame_id\": \"45\"}, {\"bbox\": [119, 42, 149, 72], \"label\": \"drone\", \"frame_id\": \"50\"}, {\"bbox\": [113, 34, 144, 64], \"label\": \"drone\", \"frame_id\": \"55\"}, {\"bbox\": [108, 24, 138, 55], \"label\": \"drone\", \"frame_id\": \"60\"}, {\"bbox\": [103, 15, 133, 46], \"label\": \"drone\", \"frame_id\": \"65\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/60.bmp"}, {"type": "image", "image": "dataset/images/data07/65.bmp"}, {"type": "image", "image": "dataset/images/data07/70.bmp"}, {"type": "image", "image": "dataset/images/data07/75.bmp"}, {"type": "image", "image": "dataset/images/data07/80.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data07\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [108, 24, 138, 55], \"label\": \"drone\", \"frame_id\": \"60\"}, {\"bbox\": [103, 15, 133, 46], \"label\": \"drone\", \"frame_id\": \"65\"}, {\"bbox\": [100, 24, 130, 55], \"label\": \"drone\", \"frame_id\": \"70\"}, {\"bbox\": [97, 24, 128, 54], \"label\": \"drone\", \"frame_id\": \"75\"}, {\"bbox\": [92, 15, 123, 45], \"label\": \"drone\", \"frame_id\": \"80\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/75.bmp"}, {"type": "image", "image": "dataset/images/data07/80.bmp"}, {"type": "image", "image": "dataset/images/data07/85.bmp"}, {"type": "image", "image": "dataset/images/data07/90.bmp"}, {"type": "image", "image": "dataset/images/data07/95.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data07\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [97, 24, 128, 54], \"label\": \"drone\", \"frame_id\": \"75\"}, {\"bbox\": [92, 15, 123, 45], \"label\": \"drone\", \"frame_id\": \"80\"}, {\"bbox\": [87, 5, 117, 36], \"label\": \"drone\", \"frame_id\": \"85\"}, {\"bbox\": [89, 24, 120, 55], \"label\": \"drone\", \"frame_id\": \"90\"}, {\"bbox\": [84, 15, 115, 46], \"label\": \"drone\", \"frame_id\": \"95\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/90.bmp"}, {"type": "image", "image": "dataset/images/data07/95.bmp"}, {"type": "image", "image": "dataset/images/data07/100.bmp"}, {"type": "image", "image": "dataset/images/data07/105.bmp"}, {"type": "image", "image": "dataset/images/data07/110.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data07\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [89, 24, 120, 55], \"label\": \"drone\", \"frame_id\": \"90\"}, {\"bbox\": [84, 15, 115, 46], \"label\": \"drone\", \"frame_id\": \"95\"}, {\"bbox\": [77, 7, 108, 37], \"label\": \"drone\", \"frame_id\": \"100\"}, {\"bbox\": [84, 32, 114, 62], \"label\": \"drone\", \"frame_id\": \"105\"}, {\"bbox\": [80, 23, 110, 53], \"label\": \"drone\", \"frame_id\": \"110\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/105.bmp"}, {"type": "image", "image": "dataset/images/data07/110.bmp"}, {"type": "image", "image": "dataset/images/data07/115.bmp"}, {"type": "image", "image": "dataset/images/data07/120.bmp"}, {"type": "image", "image": "dataset/images/data07/125.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data07\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [84, 32, 114, 62], \"label\": \"drone\", \"frame_id\": \"105\"}, {\"bbox\": [80, 23, 110, 53], \"label\": \"drone\", \"frame_id\": \"110\"}, {\"bbox\": [74, 15, 104, 45], \"label\": \"drone\", \"frame_id\": \"115\"}, {\"bbox\": [88, 33, 119, 64], \"label\": \"drone\", \"frame_id\": \"120\"}, {\"bbox\": [82, 25, 112, 56], \"label\": \"drone\", \"frame_id\": \"125\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/120.bmp"}, {"type": "image", "image": "dataset/images/data07/125.bmp"}, {"type": "image", "image": "dataset/images/data07/130.bmp"}, {"type": "image", "image": "dataset/images/data07/135.bmp"}, {"type": "image", "image": "dataset/images/data07/140.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data07\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [88, 33, 119, 64], \"label\": \"drone\", \"frame_id\": \"120\"}, {\"bbox\": [82, 25, 112, 56], \"label\": \"drone\", \"frame_id\": \"125\"}, {\"bbox\": [76, 18, 106, 48], \"label\": \"drone\", \"frame_id\": \"130\"}, {\"bbox\": [63, 17, 94, 48], \"label\": \"drone\", \"frame_id\": \"135\"}, {\"bbox\": [63, 37, 94, 68], \"label\": \"drone\", \"frame_id\": \"140\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/135.bmp"}, {"type": "image", "image": "dataset/images/data07/140.bmp"}, {"type": "image", "image": "dataset/images/data07/145.bmp"}, {"type": "image", "image": "dataset/images/data07/150.bmp"}, {"type": "image", "image": "dataset/images/data07/155.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data07\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [63, 17, 94, 48], \"label\": \"drone\", \"frame_id\": \"135\"}, {\"bbox\": [63, 37, 94, 68], \"label\": \"drone\", \"frame_id\": \"140\"}, {\"bbox\": [59, 32, 90, 63], \"label\": \"drone\", \"frame_id\": \"145\"}, {\"bbox\": [79, 50, 109, 80], \"label\": \"drone\", \"frame_id\": \"150\"}, {\"bbox\": [72, 44, 103, 75], \"label\": \"drone\", \"frame_id\": \"155\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/150.bmp"}, {"type": "image", "image": "dataset/images/data07/155.bmp"}, {"type": "image", "image": "dataset/images/data07/160.bmp"}, {"type": "image", "image": "dataset/images/data07/165.bmp"}, {"type": "image", "image": "dataset/images/data07/170.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data07\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [79, 50, 109, 80], \"label\": \"drone\", \"frame_id\": \"150\"}, {\"bbox\": [72, 44, 103, 75], \"label\": \"drone\", \"frame_id\": \"155\"}, {\"bbox\": [64, 40, 95, 70], \"label\": \"drone\", \"frame_id\": \"160\"}, {\"bbox\": [60, 39, 91, 69], \"label\": \"drone\", \"frame_id\": \"165\"}, {\"bbox\": [88, 55, 119, 85], \"label\": \"drone\", \"frame_id\": \"170\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/165.bmp"}, {"type": "image", "image": "dataset/images/data07/170.bmp"}, {"type": "image", "image": "dataset/images/data07/175.bmp"}, {"type": "image", "image": "dataset/images/data07/180.bmp"}, {"type": "image", "image": "dataset/images/data07/185.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data07\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [60, 39, 91, 69], \"label\": \"drone\", \"frame_id\": \"165\"}, {\"bbox\": [88, 55, 119, 85], \"label\": \"drone\", \"frame_id\": \"170\"}, {\"bbox\": [81, 49, 112, 80], \"label\": \"drone\", \"frame_id\": \"175\"}, {\"bbox\": [76, 44, 106, 75], \"label\": \"drone\", \"frame_id\": \"180\"}, {\"bbox\": [82, 57, 112, 88], \"label\": \"drone\", \"frame_id\": \"185\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/180.bmp"}, {"type": "image", "image": "dataset/images/data07/185.bmp"}, {"type": "image", "image": "dataset/images/data07/190.bmp"}, {"type": "image", "image": "dataset/images/data07/195.bmp"}, {"type": "image", "image": "dataset/images/data07/200.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data07\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [76, 44, 106, 75], \"label\": \"drone\", \"frame_id\": \"180\"}, {\"bbox\": [82, 57, 112, 88], \"label\": \"drone\", \"frame_id\": \"185\"}, {\"bbox\": [76, 53, 107, 84], \"label\": \"drone\", \"frame_id\": \"190\"}, {\"bbox\": [71, 48, 101, 79], \"label\": \"drone\", \"frame_id\": \"195\"}, {\"bbox\": [80, 61, 111, 92], \"label\": \"drone\", \"frame_id\": \"200\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/195.bmp"}, {"type": "image", "image": "dataset/images/data07/200.bmp"}, {"type": "image", "image": "dataset/images/data07/205.bmp"}, {"type": "image", "image": "dataset/images/data07/210.bmp"}, {"type": "image", "image": "dataset/images/data07/215.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data07\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [71, 48, 101, 79], \"label\": \"drone\", \"frame_id\": \"195\"}, {\"bbox\": [80, 61, 111, 92], \"label\": \"drone\", \"frame_id\": \"200\"}, {\"bbox\": [74, 56, 104, 87], \"label\": \"drone\", \"frame_id\": \"205\"}, {\"bbox\": [69, 52, 100, 83], \"label\": \"drone\", \"frame_id\": \"210\"}, {\"bbox\": [84, 66, 115, 96], \"label\": \"drone\", \"frame_id\": \"215\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/210.bmp"}, {"type": "image", "image": "dataset/images/data07/215.bmp"}, {"type": "image", "image": "dataset/images/data07/220.bmp"}, {"type": "image", "image": "dataset/images/data07/225.bmp"}, {"type": "image", "image": "dataset/images/data07/230.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data07\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [69, 52, 100, 83], \"label\": \"drone\", \"frame_id\": \"210\"}, {\"bbox\": [84, 66, 115, 96], \"label\": \"drone\", \"frame_id\": \"215\"}, {\"bbox\": [79, 63, 109, 93], \"label\": \"drone\", \"frame_id\": \"220\"}, {\"bbox\": [72, 58, 102, 88], \"label\": \"drone\", \"frame_id\": \"225\"}, {\"bbox\": [73, 59, 104, 90], \"label\": \"drone\", \"frame_id\": \"230\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/225.bmp"}, {"type": "image", "image": "dataset/images/data07/230.bmp"}, {"type": "image", "image": "dataset/images/data07/235.bmp"}, {"type": "image", "image": "dataset/images/data07/240.bmp"}, {"type": "image", "image": "dataset/images/data07/245.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data07\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [72, 58, 102, 88], \"label\": \"drone\", \"frame_id\": \"225\"}, {\"bbox\": [73, 59, 104, 90], \"label\": \"drone\", \"frame_id\": \"230\"}, {\"bbox\": [82, 59, 112, 90], \"label\": \"drone\", \"frame_id\": \"235\"}, {\"bbox\": [79, 56, 109, 87], \"label\": \"drone\", \"frame_id\": \"240\"}, {\"bbox\": [80, 64, 110, 95], \"label\": \"drone\", \"frame_id\": \"245\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/240.bmp"}, {"type": "image", "image": "dataset/images/data07/245.bmp"}, {"type": "image", "image": "dataset/images/data07/250.bmp"}, {"type": "image", "image": "dataset/images/data07/255.bmp"}, {"type": "image", "image": "dataset/images/data07/260.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data07\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [79, 56, 109, 87], \"label\": \"drone\", \"frame_id\": \"240\"}, {\"bbox\": [80, 64, 110, 95], \"label\": \"drone\", \"frame_id\": \"245\"}, {\"bbox\": [76, 61, 106, 92], \"label\": \"drone\", \"frame_id\": \"250\"}, {\"bbox\": [69, 57, 100, 88], \"label\": \"drone\", \"frame_id\": \"255\"}, {\"bbox\": [64, 53, 95, 84], \"label\": \"drone\", \"frame_id\": \"260\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/255.bmp"}, {"type": "image", "image": "dataset/images/data07/260.bmp"}, {"type": "image", "image": "dataset/images/data07/265.bmp"}, {"type": "image", "image": "dataset/images/data07/270.bmp"}, {"type": "image", "image": "dataset/images/data07/275.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data07\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [69, 57, 100, 88], \"label\": \"drone\", \"frame_id\": \"255\"}, {\"bbox\": [64, 53, 95, 84], \"label\": \"drone\", \"frame_id\": \"260\"}, {\"bbox\": [81, 63, 112, 93], \"label\": \"drone\", \"frame_id\": \"265\"}, {\"bbox\": [76, 58, 106, 88], \"label\": \"drone\", \"frame_id\": \"270\"}, {\"bbox\": [71, 53, 101, 84], \"label\": \"drone\", \"frame_id\": \"275\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/270.bmp"}, {"type": "image", "image": "dataset/images/data07/275.bmp"}, {"type": "image", "image": "dataset/images/data07/280.bmp"}, {"type": "image", "image": "dataset/images/data07/285.bmp"}, {"type": "image", "image": "dataset/images/data07/290.bmp"}, {"type": "text", "text": "检测视频序列中的弱小目标。\n\n序列信息：\n- 序列：data07\n- 帧数：5\n- 抽取策略：选择有标注的帧\n\n目标特征：\n- 尺寸小（5-20像素）\n- 类别：drone, car, ship, bus, pedestrian, cyclist\n\n要求：\n1. 检测每帧中的所有目标\n2. 为每个检测标注frame_id\n3. 保持检测一致性\n\n输出JSON格式：\n[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [76, 58, 106, 88], \"label\": \"drone\", \"frame_id\": \"270\"}, {\"bbox\": [71, 53, 101, 84], \"label\": \"drone\", \"frame_id\": \"275\"}, {\"bbox\": [82, 63, 112, 93], \"label\": \"drone\", \"frame_id\": \"280\"}, {\"bbox\": [68, 49, 98, 80], \"label\": \"drone\", \"frame_id\": \"285\"}, {\"bbox\": [79, 57, 109, 88], \"label\": \"drone\", \"frame_id\": \"290\"}]"}]}]