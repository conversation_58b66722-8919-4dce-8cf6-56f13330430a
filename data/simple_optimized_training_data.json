[{"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/0.bmp"}, {"type": "text", "text": "检测这张红外图像中的弱小目标。\n\n弱小目标特征：\n- 尺寸很小（5-20像素）\n- 在红外图像中呈现为亮点或暗点\n- 目标类别：drone, car, ship, bus, pedestrian, cyclist\n\n输出格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [68, 106, 73, 111], \"label\": \"drone\", \"frame_id\": \"0\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/10.bmp"}, {"type": "text", "text": "检测这张红外图像中的弱小目标。\n\n弱小目标特征：\n- 尺寸很小（5-20像素）\n- 在红外图像中呈现为亮点或暗点\n- 目标类别：drone, car, ship, bus, pedestrian, cyclist\n\n输出格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [62, 106, 67, 111], \"label\": \"drone\", \"frame_id\": \"10\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/20.bmp"}, {"type": "text", "text": "检测这张红外图像中的弱小目标。\n\n弱小目标特征：\n- 尺寸很小（5-20像素）\n- 在红外图像中呈现为亮点或暗点\n- 目标类别：drone, car, ship, bus, pedestrian, cyclist\n\n输出格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [67, 106, 72, 111], \"label\": \"drone\", \"frame_id\": \"20\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/30.bmp"}, {"type": "text", "text": "检测这张红外图像中的弱小目标。\n\n弱小目标特征：\n- 尺寸很小（5-20像素）\n- 在红外图像中呈现为亮点或暗点\n- 目标类别：drone, car, ship, bus, pedestrian, cyclist\n\n输出格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [68, 106, 73, 111], \"label\": \"drone\", \"frame_id\": \"30\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data01/40.bmp"}, {"type": "text", "text": "检测这张红外图像中的弱小目标。\n\n弱小目标特征：\n- 尺寸很小（5-20像素）\n- 在红外图像中呈现为亮点或暗点\n- 目标类别：drone, car, ship, bus, pedestrian, cyclist\n\n输出格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [64, 106, 69, 111], \"label\": \"drone\", \"frame_id\": \"40\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/0.bmp"}, {"type": "text", "text": "检测这张红外图像中的弱小目标。\n\n弱小目标特征：\n- 尺寸很小（5-20像素）\n- 在红外图像中呈现为亮点或暗点\n- 目标类别：drone, car, ship, bus, pedestrian, cyclist\n\n输出格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [97, 88, 102, 93], \"label\": \"drone\", \"frame_id\": \"0\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/10.bmp"}, {"type": "text", "text": "检测这张红外图像中的弱小目标。\n\n弱小目标特征：\n- 尺寸很小（5-20像素）\n- 在红外图像中呈现为亮点或暗点\n- 目标类别：drone, car, ship, bus, pedestrian, cyclist\n\n输出格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [96, 90, 101, 95], \"label\": \"drone\", \"frame_id\": \"10\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/20.bmp"}, {"type": "text", "text": "检测这张红外图像中的弱小目标。\n\n弱小目标特征：\n- 尺寸很小（5-20像素）\n- 在红外图像中呈现为亮点或暗点\n- 目标类别：drone, car, ship, bus, pedestrian, cyclist\n\n输出格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [96, 89, 101, 94], \"label\": \"drone\", \"frame_id\": \"20\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/30.bmp"}, {"type": "text", "text": "检测这张红外图像中的弱小目标。\n\n弱小目标特征：\n- 尺寸很小（5-20像素）\n- 在红外图像中呈现为亮点或暗点\n- 目标类别：drone, car, ship, bus, pedestrian, cyclist\n\n输出格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [97, 89, 102, 94], \"label\": \"drone\", \"frame_id\": \"30\"}]"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data02/40.bmp"}, {"type": "text", "text": "检测这张红外图像中的弱小目标。\n\n弱小目标特征：\n- 尺寸很小（5-20像素）\n- 在红外图像中呈现为亮点或暗点\n- 目标类别：drone, car, ship, bus, pedestrian, cyclist\n\n输出格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}]\n\n开始检测："}]}, {"role": "assistant", "content": "[{\"bbox\": [96, 88, 101, 93], \"label\": \"drone\", \"frame_id\": \"40\"}]"}]}]