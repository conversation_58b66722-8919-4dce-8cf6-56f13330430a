#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主评估脚本 - 使用微调后模型进行真正的检测与评估
对所有8个序列进行真正的检测，使用前5%数据 + 线性插值策略
"""

import sys
import os
sys.path.append('src')

from correct_video_detector import main as correct_video_detector_main
from evaluation_system import CompleteEvaluationSystem

def main():
    print("=== 完整深度微调模型检测与评估系统 ===")
    print("🎯 目标序列: data01, data02, data04, data06, data07, data19, data23, data26")
    print("📊 检测策略: 每隔10帧抽取1帧进行检测")
    print("🔄 插值策略: 缺失帧通过线性插值推测位置")
    print("📈 测试范围: 每个序列前5%数据")
    print("🤖 模型: 使用完整深度微调后的Qwen2.5-VL模型 (732帧训练)")

    # 检查帧级微调模型是否存在
    base_model_path = "qwen2.5-vl-7b"
    lora_model_path = "output/frame_level_video_sequence_lora"

    if not os.path.exists(lora_model_path):
        print(f"\n❌ 未找到帧级微调后的模型: {lora_model_path}")
        print("请先运行 python train.py 进行帧级微调训练")
        return False

    print(f"\n✅ 找到帧级微调后的模型: {lora_model_path}")

    # 初始化完整检测器
    print("\n初始化完整深度微调检测器...")
    detector = RealVideoSequenceDetector(base_model_path, lora_model_path)

    # 进行真实检测
    print("\n开始真实检测...")
    sequences = ['data01', 'data02', 'data04', 'data06', 'data07', 'data19', 'data23', 'data26']
    all_detections = {}

    for seq_id in sequences:
        print(f"\n{'='*60}")
        print(f"检测序列: {seq_id}")
        print(f"{'='*60}")

        detections = detector.detect_sequence_with_interpolation(seq_id, test_ratio=0.05)
        all_detections[seq_id] = detections

        print(f"序列 {seq_id} 检测完成: {len(detections)} 个检测结果")

    # 保存检测结果
    import json
    os.makedirs("results", exist_ok=True)
    with open("results/frame_level_detection_results.json", 'w', encoding='utf-8') as f:
        json.dump(all_detections, f, ensure_ascii=False, indent=2)

    print("\n🎉 真正的帧级深度微调模型检测完成！")
    print("📊 检测结果已保存到 results/frame_level_detection_results.json")

    # 统计总体结果
    total_detections = sum(len(detections) for detections in all_detections.values())
    print(f"\n📈 检测统计:")
    print(f"   总检测数: {total_detections}")
    for seq_id, detections in all_detections.items():
        interpolated_count = sum(1 for d in detections if d.get('interpolated', False))
        direct_count = len(detections) - interpolated_count
        print(f"   {seq_id}: {len(detections)} 个检测 (直接: {direct_count}, 插值: {interpolated_count})")

    print("\n💡 这是使用完整深度微调模型的检测结果")
    print("包括：732帧训练数据 + 每隔10帧抽取检测 + 线性插值补全")

    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
