#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
正确的评估系统 - 使用视频序列微调后的模型进行检测和评估
"""

import os
import sys
from pathlib import Path

sys.path.append('src')

from correct_video_detector import main as correct_video_detector_main

def main():
    print("=== 正确的视频序列检测与评估系统 ===")
    print("🎯 目标序列: data01, data02, data04, data06, data07, data19, data23, data26")
    print("📊 检测策略: 每隔10帧抽取组成视频序列进行检测")
    print("🎬 检测方式: 真正的视频序列检测 (不是单帧+插值)")
    print("📈 测试范围: 每个序列前5%数据")
    print("🤖 模型: 使用视频序列微调后的Qwen2.5-VL模型")
    
    # 直接调用正确的视频检测器
    return correct_video_detector_main()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
