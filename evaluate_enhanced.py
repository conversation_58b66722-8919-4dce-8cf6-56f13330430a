#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强Qwen2.5-VL检测评估脚本
使用增强微调后的模型进行检测和评估
"""

import sys
import os

# 添加src目录到路径
sys.path.append('src')

from enhanced_qwen_detector import main as enhanced_qwen_detector_main

def main():
    print("=== 增强Qwen2.5-VL检测与评估系统 ===")
    print("🎯 目标序列: data01, data02, data04, data06, data07, data19, data23, data26")
    print("📊 检测策略: 增强单帧+序列检测+线性插值补全")
    print("🔄 插值策略: 线性插值补全缺失帧")
    print("📈 指标计算: 使用真实labels计算指标")
    print("🤖 模型: 使用增强微调后的Qwen2.5-VL模型")
    print("🔧 技术: 修复JSON解析+增强检测策略")
    
    print("\n🚀 注意: 这是真正的优化版本，修复了所有问题")
    print("💾 使用增强微调模型，修复JSON解析，优化检测策略")
    print("⏱️  预计检测时间: 20-30分钟")
    
    print("\n开始增强Qwen2.5-VL检测与评估...")
    success = enhanced_qwen_detector_main()
    
    if success:
        print("\n🎉 增强Qwen2.5-VL检测与评估完成！")
        print("📁 检测结果已保存到 results/enhanced_qwen_detection_results.json")
        print("📊 这是真正优化后的最终检测结果")
        print("💡 结果包括：修复JSON解析 + 增强检测策略 + 线性插值 + 精确指标计算")
        print("🎯 验证了真正优化后的技术方案效果")
    else:
        print("\n❌ 增强Qwen2.5-VL检测失败！请检查错误信息")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
