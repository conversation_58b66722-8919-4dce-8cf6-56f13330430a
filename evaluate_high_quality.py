#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高质量Qwen2.5-VL检测评估脚本
专注于提升检测精度和召回率
"""

import sys
import os

# 添加src目录到路径
sys.path.append('src')

from high_quality_detector import main as high_quality_detector_main

def main():
    print("=== 高质量Qwen2.5-VL检测与评估系统 ===")
    print("🎯 目标序列: data01, data02, data04, data06, data07, data19, data23, data26")
    print("📊 检测策略: 高质量检测+智能插值")
    print("🔄 插值策略: 基于运动模式的智能插值")
    print("📈 指标计算: 使用真实labels计算指标")
    print("🤖 模型: 使用高质量微调后的Qwen2.5-VL模型")
    print("🔧 技术: 精确JSON解析+质量过滤+智能插值")
    
    print("\n🚀 注意: 专注于提升检测质量，而不是数量")
    print("💾 使用高质量微调模型，精确检测，智能插值")
    print("⏱️  预计检测时间: 15-25分钟")
    
    print("\n开始高质量Qwen2.5-VL检测与评估...")
    success = high_quality_detector_main()
    
    if success:
        print("\n🎉 高质量Qwen2.5-VL检测与评估完成！")
        print("📁 检测结果已保存到 results/high_quality_qwen_detection_results.json")
        print("📊 这是高质量优化后的最终检测结果")
        print("💡 结果包括：精确JSON解析 + 质量过滤 + 智能插值 + 精确指标计算")
        print("🎯 专注于提升检测精度和召回率")
    else:
        print("\n❌ 高质量Qwen2.5-VL检测失败！请检查错误信息")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
