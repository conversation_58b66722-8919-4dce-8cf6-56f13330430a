#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
官方Qwen2.5-VL检测评估脚本
使用官方格式微调后的模型进行检测和评估
"""

import sys
import os

# 添加src目录到路径
sys.path.append('src')

from official_qwen_detector import main as official_qwen_detector_main

def main():
    print("=== 官方Qwen2.5-VL检测与评估系统 ===")
    print("🎯 目标序列: data01, data02, data04, data06, data07, data19, data23, data26")
    print("📊 检测策略: 对完整长序列进行一次性检测")
    print("🔄 插值策略: 线性插值补全缺失帧")
    print("📈 指标计算: 使用真实labels计算指标")
    print("🤖 模型: 使用官方格式微调后的Qwen2.5-VL模型")
    
    print("\n🚀 注意: 这将使用官方格式微调的Qwen2.5-VL模型进行检测")
    print("💾 每个序列进行完整长序列检测，然后线性插值补全")
    print("⏱️  预计检测时间: 15-25分钟")
    
    print("\n开始官方Qwen2.5-VL检测与评估...")
    success = official_qwen_detector_main()
    
    if success:
        print("\n🎉 官方Qwen2.5-VL检测与评估完成！")
        print("📁 检测结果已保存到 results/official_qwen_detection_results.json")
        print("📊 这是使用官方格式微调模型的最终检测结果")
        print("💡 结果包括：完整序列检测 + 线性插值 + 精确指标计算")
    else:
        print("\n❌ 官方Qwen2.5-VL检测失败！请检查错误信息")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
