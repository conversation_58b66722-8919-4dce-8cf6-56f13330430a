#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Qwen2.5-VL视频评估系统 - 使用全参数微调后的模型进行检测和评估
"""

import os
import sys
from pathlib import Path

sys.path.append('src')

from qwen_video_detector import main as qwen_video_detector_main

def main():
    print("=== Qwen2.5-VL视频检测与评估系统 ===")
    print("🎯 目标序列: data01, data02, data04, data06, data07, data19, data23, data26")
    print("📊 检测策略: 对完整长序列进行一次性检测")
    print("🔄 插值策略: 线性插值补全缺失帧")
    print("📈 指标计算: 使用真实labels计算指标")
    print("🤖 模型: 使用Qwen2.5-VL全参数微调后的模型")
    
    # 直接调用Qwen2.5-VL视频检测器
    return qwen_video_detector_main()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
