#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
超强Qwen2.5-VL检测评估脚本
使用超强微调后的模型进行检测和评估
"""

import sys
import os

# 添加src目录到路径
sys.path.append('src')

# 使用增强版本的检测器，但应用超强检测策略
import json
import torch
import logging
from pathlib import Path
from typing import List, Dict, Any
from PIL import Image
from transformers import Qwen2_5_VLForConditionalGeneration, AutoProcessor
from peft import PeftModel
import re
import numpy as np
from collections import defaultdict

# 导入超强检测器类
from supercharged_qwen_detector import SuperchargedQwenDetector

def main():
    print("=== 超强Qwen2.5-VL检测与评估系统 ===")
    print("🎯 目标序列: data01, data02, data04, data06, data07, data19, data23, data26")
    print("📊 检测策略: 多策略融合检测+高级轨迹插值")
    print("🔄 插值策略: 基于轨迹的高级插值算法")
    print("📈 指标计算: 使用真实labels计算指标")
    print("🤖 模型: 使用增强微调后的Qwen2.5-VL模型")
    print("🔧 技术: 超强JSON解析+多策略检测+轨迹插值")

    # 检查模型是否存在
    base_model_path = "qwen2.5-vl-7b"
    lora_model_path = "output/enhanced_qwen_finetuned"

    if not os.path.exists(lora_model_path):
        print(f"\n❌ 未找到增强微调后的模型: {lora_model_path}")
        print("请先运行增强训练")
        return False

    print(f"\n✅ 找到增强微调后的模型: {lora_model_path}")

    # 初始化超强检测器
    print("\n初始化超强Qwen2.5-VL检测器...")
    detector = SuperchargedQwenDetector(base_model_path, lora_model_path)

    # 开始检测
    print("\n开始超强Qwen2.5-VL检测与指标计算...")

    sequences = ['data01', 'data02', 'data04', 'data06', 'data07', 'data19', 'data23', 'data26']
    all_results = {}

    for sequence_id in sequences:
        print(f"\n{'='*60}")
        print(f"超强检测序列: {sequence_id}")
        print(f"{'='*60}")

        result = detector.detect_sequence_with_supercharged_method(sequence_id)
        all_results[sequence_id] = result

    # 保存完整结果
    os.makedirs("results", exist_ok=True)
    with open("results/supercharged_qwen_detection_results.json", 'w', encoding='utf-8') as f:
        json.dump(all_results, f, ensure_ascii=False, indent=2)

    print("\n🎉 超强Qwen2.5-VL检测完成！")
    print("📊 检测结果已保存到 results/supercharged_qwen_detection_results.json")

    # 统计总体结果
    print("\n📈 总体检测统计:")
    total_detections = 0
    total_direct = 0
    total_interpolated = 0
    overall_metrics = {"tp": 0, "fp": 0, "fn": 0}

    for seq_id, result in all_results.items():
        if result:
            total_detections += result['total_detections']
            total_direct += result['direct_detections']
            total_interpolated += result['interpolated_detections']

            metrics = result['metrics']
            overall_metrics['tp'] += metrics['tp']
            overall_metrics['fp'] += metrics['fp']
            overall_metrics['fn'] += metrics['fn']

            print(f"   {seq_id}: {result['total_detections']} 个检测 "
                  f"(直接: {result['direct_detections']}, 插值: {result['interpolated_detections']}) "
                  f"P: {metrics['precision']:.3f}, R: {metrics['recall']:.3f}, F1: {metrics['f1']:.3f}")

    # 计算总体指标
    overall_precision = overall_metrics['tp'] / (overall_metrics['tp'] + overall_metrics['fp']) if (overall_metrics['tp'] + overall_metrics['fp']) > 0 else 0
    overall_recall = overall_metrics['tp'] / (overall_metrics['tp'] + overall_metrics['fn']) if (overall_metrics['tp'] + overall_metrics['fn']) > 0 else 0
    overall_f1 = 2 * overall_precision * overall_recall / (overall_precision + overall_recall) if (overall_precision + overall_recall) > 0 else 0

    print(f"\n📊 总体统计:")
    print(f"   总检测数: {total_detections}")
    print(f"   直接检测: {total_direct}")
    print(f"   插值检测: {total_interpolated}")
    print(f"   总体精确度: {overall_precision:.3f}")
    print(f"   总体召回率: {overall_recall:.3f}")
    print(f"   总体F1分数: {overall_f1:.3f}")

    print("\n💡 这是使用增强微调模型+超强检测策略的结果:")
    print("包括：超强JSON解析 + 多策略融合检测 + 高级轨迹插值 + 真实指标计算")
    print("🎯 最大化优化了你的技术方案效果")

    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
