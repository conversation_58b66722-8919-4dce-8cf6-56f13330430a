#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时序序列Qwen2.5-VL检测评估脚本
使用时序序列微调后的模型进行检测和评估
"""

import sys
import os

# 添加src目录到路径
sys.path.append('src')

from temporal_sequence_detector import main as temporal_sequence_detector_main

def main():
    print("=== 时序序列Qwen2.5-VL检测与评估系统 ===")
    print("🎯 目标序列: data01, data02, data04, data06, data07, data19, data23, data26")
    print("📊 检测策略: 时序序列检测+时序插值")
    print("🔄 插值策略: 基于时序轨迹的插值")
    print("📈 指标计算: 使用真实labels计算指标")
    print("🤖 模型: 使用时序序列微调后的Qwen2.5-VL模型")
    print("🔧 技术: 时序感知检测+轨迹建模+时序插值")
    
    print("\n🚀 注意: 完全基于时序序列，无单帧训练")
    print("💾 严格按照'每隔10帧抽取'，学习时序关系")
    print("⏱️  预计检测时间: 20-30分钟")
    
    print("\n开始时序序列Qwen2.5-VL检测与评估...")
    success = temporal_sequence_detector_main()
    
    if success:
        print("\n🎉 时序序列Qwen2.5-VL检测与评估完成！")
        print("📁 检测结果已保存到 results/temporal_sequence_qwen_detection_results.json")
        print("📊 这是时序感知优化后的最终检测结果")
        print("💡 结果包括：时序感知检测 + 轨迹建模 + 时序插值 + 精确指标计算")
        print("🎯 完全基于时序序列，严格按照'每隔10帧抽取'要求")
    else:
        print("\n❌ 时序序列Qwen2.5-VL检测失败！请检查错误信息")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
