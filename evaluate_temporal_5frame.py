#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时序序列Qwen2.5-VL检测评估脚本 - 每隔5帧抽取测试
使用时序序列微调后的模型进行检测和评估
"""

import sys
import os

# 添加src目录到路径
sys.path.append('src')

from temporal_sequence_detector import main as temporal_sequence_detector_main

def main():
    print("=== 时序序列Qwen2.5-VL检测与评估系统 (每隔5帧抽取) ===")
    print("🎯 目标序列: data01, data02, data04, data06, data07, data19, data23, data26")
    print("📊 检测策略: 时序序列检测+时序插值 (每隔5帧抽取)")
    print("🔄 插值策略: 基于时序轨迹的插值")
    print("📈 指标计算: 使用真实labels计算指标")
    print("🤖 模型: 使用时序序列微调后的Qwen2.5-VL模型")
    print("🔧 技术: 时序感知检测+轨迹建模+时序插值")
    
    print("\n🚀 注意: 测试每隔5帧抽取的效果")
    print("💾 更密集的关键帧，可能有更好的检测效果")
    print("⏱️  预计检测时间: 25-35分钟")
    
    print("\n开始时序序列Qwen2.5-VL检测与评估 (每隔5帧)...")
    success = temporal_sequence_detector_main()
    
    if success:
        print("\n🎉 时序序列Qwen2.5-VL检测与评估完成 (每隔5帧)！")
        print("📁 检测结果已保存到 results/temporal_sequence_qwen_detection_results.json")
        print("📊 这是每隔5帧抽取的检测结果")
        print("💡 结果包括：时序感知检测 + 轨迹建模 + 时序插值 + 精确指标计算")
        print("🎯 测试更密集关键帧的效果")
    else:
        print("\n❌ 时序序列Qwen2.5-VL检测失败！请检查错误信息")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
