#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
弱小目标优化Qwen2.5-VL检测评估脚本
使用弱小目标优化微调后的模型进行检测和评估
按照用户思路优化，充分考虑时序性
"""

import sys
import os

# 添加src目录到路径
sys.path.append('src')

from weak_target_optimized_detector import main as weak_target_optimized_detector_main

def main():
    print("=== 弱小目标优化Qwen2.5-VL检测与评估系统 ===")
    print("🎯 目标序列: data01, data02, data04, data06, data07, data19, data23, data26")
    print("📊 检测策略: 弱小目标优化检测+时序感知插值")
    print("🔄 插值策略: 基于时序轨迹的智能插值")
    print("📈 指标计算: 使用真实labels计算指标")
    print("🤖 模型: 使用弱小目标优化微调后的Qwen2.5-VL模型")
    print("🔧 技术: 图像增强+弱小目标专用检测+时序轨迹建模")
    
    print("\n🚀 注意: 按照用户思路优化，充分考虑时序性")
    print("💾 图像增强 + 弱小目标专用 + 时序感知插值")
    print("⏱️  预计检测时间: 20-30分钟")
    
    print("\n开始弱小目标优化Qwen2.5-VL检测与评估...")
    success = weak_target_optimized_detector_main()
    
    if success:
        print("\n🎉 弱小目标优化Qwen2.5-VL检测与评估完成！")
        print("📁 检测结果已保存到 results/weak_target_optimized_detection_results.json")
        print("📊 这是弱小目标专用优化后的最终检测结果")
        print("💡 结果包括：图像增强 + 弱小目标专用检测 + 时序感知插值 + 精确指标计算")
        print("🎯 按照用户思路优化，充分考虑时序性")
    else:
        print("\n❌ 弱小目标优化Qwen2.5-VL检测失败！请检查错误信息")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
