[{"conversations": [{"from": "human", "value": "这是一个包含5帧的红外视频序列（从data01每隔10帧抽取1帧组成），图像尺寸为256x256，请仔细检测序列中所有帧的弱小目标。目标类别包括：drone, car, ship, bus, pedestrian, cyclist。请以JSON数组格式输出检测结果，每个目标必须包含精确的边界框坐标、类别标签和帧ID。格式要求：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]"}, {"from": "gpt", "value": "[{\"bbox\": [68.500096, 106.49996800000001, 73.50003199999999, 111.499904], \"label\": \"drone\", \"frame_id\": \"0\"}, {\"bbox\": [62.499968, 106.49996800000001, 67.499904, 111.499904], \"label\": \"drone\", \"frame_id\": \"10\"}, {\"bbox\": [67.50016000000001, 106.49996800000001, 72.500096, 111.499904], \"label\": \"drone\", \"frame_id\": \"20\"}, {\"bbox\": [68.500096, 106.49996800000001, 73.50003199999999, 111.499904], \"label\": \"drone\", \"frame_id\": \"30\"}, {\"bbox\": [64.500096, 106.49996800000001, 69.50003199999999, 111.499904], \"label\": \"drone\", \"frame_id\": \"40\"}]"}], "sequence_info": {"sequence_id": "data01_complete_video", "original_sequence": "data01", "video_frames": [{"frame_idx": 0, "frame_id": "0", "file_path": "dataset/images/data01/0.bmp", "sequence_position": 0}, {"frame_idx": 10, "frame_id": "10", "file_path": "dataset/images/data01/10.bmp", "sequence_position": 1}, {"frame_idx": 20, "frame_id": "20", "file_path": "dataset/images/data01/20.bmp", "sequence_position": 2}, {"frame_idx": 30, "frame_id": "30", "file_path": "dataset/images/data01/30.bmp", "sequence_position": 3}, {"frame_idx": 40, "frame_id": "40", "file_path": "dataset/images/data01/40.bmp", "sequence_position": 4}], "frame_count": 5, "frame_interval": 10, "image_size": [256, 256], "naming_system": "simple", "file_format": ".bmp", "annotations": [{"bbox": [68.500096, 106.49996800000001, 73.50003199999999, 111.499904], "label": "drone", "frame_id": "0", "frame_idx": 0, "sequence_position": 0}, {"bbox": [62.499968, 106.49996800000001, 67.499904, 111.499904], "label": "drone", "frame_id": "10", "frame_idx": 10, "sequence_position": 1}, {"bbox": [67.50016000000001, 106.49996800000001, 72.500096, 111.499904], "label": "drone", "frame_id": "20", "frame_idx": 20, "sequence_position": 2}, {"bbox": [68.500096, 106.49996800000001, 73.50003199999999, 111.499904], "label": "drone", "frame_id": "30", "frame_idx": 30, "sequence_position": 3}, {"bbox": [64.500096, 106.49996800000001, 69.50003199999999, 111.499904], "label": "drone", "frame_id": "40", "frame_idx": 40, "sequence_position": 4}], "total_annotations": 5}}, {"conversations": [{"from": "human", "value": "这是一个包含5帧的红外视频序列（从data02每隔10帧抽取1帧组成），图像尺寸为256x256，请仔细检测序列中所有帧的弱小目标。目标类别包括：drone, car, ship, bus, pedestrian, cyclist。请以JSON数组格式输出检测结果，每个目标必须包含精确的边界框坐标、类别标签和帧ID。格式要求：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]"}, {"from": "gpt", "value": "[{\"bbox\": [97.500032, 88.500096, 102.499968, 93.50003199999999], \"label\": \"drone\", \"frame_id\": \"0\"}, {\"bbox\": [96.500096, 90.49996800000001, 101.50003199999999, 95.499904], \"label\": \"drone\", \"frame_id\": \"10\"}, {\"bbox\": [96.500096, 89.500032, 101.50003199999999, 94.499968], \"label\": \"drone\", \"frame_id\": \"20\"}, {\"bbox\": [97.500032, 89.500032, 102.499968, 94.499968], \"label\": \"drone\", \"frame_id\": \"30\"}, {\"bbox\": [96.500096, 88.500096, 101.50003199999999, 93.50003199999999], \"label\": \"drone\", \"frame_id\": \"40\"}]"}], "sequence_info": {"sequence_id": "data02_complete_video", "original_sequence": "data02", "video_frames": [{"frame_idx": 0, "frame_id": "0", "file_path": "dataset/images/data02/0.bmp", "sequence_position": 0}, {"frame_idx": 10, "frame_id": "10", "file_path": "dataset/images/data02/10.bmp", "sequence_position": 1}, {"frame_idx": 20, "frame_id": "20", "file_path": "dataset/images/data02/20.bmp", "sequence_position": 2}, {"frame_idx": 30, "frame_id": "30", "file_path": "dataset/images/data02/30.bmp", "sequence_position": 3}, {"frame_idx": 40, "frame_id": "40", "file_path": "dataset/images/data02/40.bmp", "sequence_position": 4}], "frame_count": 5, "frame_interval": 10, "image_size": [256, 256], "naming_system": "simple", "file_format": ".bmp", "annotations": [{"bbox": [97.500032, 88.500096, 102.499968, 93.50003199999999], "label": "drone", "frame_id": "0", "frame_idx": 0, "sequence_position": 0}, {"bbox": [96.500096, 90.49996800000001, 101.50003199999999, 95.499904], "label": "drone", "frame_id": "10", "frame_idx": 10, "sequence_position": 1}, {"bbox": [96.500096, 89.500032, 101.50003199999999, 94.499968], "label": "drone", "frame_id": "20", "frame_idx": 20, "sequence_position": 2}, {"bbox": [97.500032, 89.500032, 102.499968, 94.499968], "label": "drone", "frame_id": "30", "frame_idx": 30, "sequence_position": 3}, {"bbox": [96.500096, 88.500096, 101.50003199999999, 93.50003199999999], "label": "drone", "frame_id": "40", "frame_idx": 40, "sequence_position": 4}], "total_annotations": 5}}, {"conversations": [{"from": "human", "value": "这是一个包含5帧的红外视频序列（从data04每隔10帧抽取1帧组成），图像尺寸为256x256，请仔细检测序列中所有帧的弱小目标。目标类别包括：drone, car, ship, bus, pedestrian, cyclist。请以JSON数组格式输出检测结果，每个目标必须包含精确的边界框坐标、类别标签和帧ID。格式要求：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]"}, {"from": "gpt", "value": "[{\"bbox\": [97.500032, 78.49996800000001, 102.499968, 83.499904], \"label\": \"drone\", \"frame_id\": \"0\"}, {\"bbox\": [104.500096, 79.499904, 109.50003199999999, 84.49983999999999], \"label\": \"drone\", \"frame_id\": \"10\"}, {\"bbox\": [101.500032, 85.500032, 106.499968, 90.499968], \"label\": \"drone\", \"frame_id\": \"20\"}, {\"bbox\": [100.500096, 80.500096, 105.50003199999999, 85.50003199999999], \"label\": \"drone\", \"frame_id\": \"30\"}, {\"bbox\": [104.500096, 83.50016000000001, 109.50003199999999, 88.500096], \"label\": \"drone\", \"frame_id\": \"40\"}]"}], "sequence_info": {"sequence_id": "data04_complete_video", "original_sequence": "data04", "video_frames": [{"frame_idx": 0, "frame_id": "0", "file_path": "dataset/images/data04/0.bmp", "sequence_position": 0}, {"frame_idx": 10, "frame_id": "10", "file_path": "dataset/images/data04/10.bmp", "sequence_position": 1}, {"frame_idx": 20, "frame_id": "20", "file_path": "dataset/images/data04/20.bmp", "sequence_position": 2}, {"frame_idx": 30, "frame_id": "30", "file_path": "dataset/images/data04/30.bmp", "sequence_position": 3}, {"frame_idx": 40, "frame_id": "40", "file_path": "dataset/images/data04/40.bmp", "sequence_position": 4}], "frame_count": 5, "frame_interval": 10, "image_size": [256, 256], "naming_system": "simple", "file_format": ".bmp", "annotations": [{"bbox": [97.500032, 78.49996800000001, 102.499968, 83.499904], "label": "drone", "frame_id": "0", "frame_idx": 0, "sequence_position": 0}, {"bbox": [104.500096, 79.499904, 109.50003199999999, 84.49983999999999], "label": "drone", "frame_id": "10", "frame_idx": 10, "sequence_position": 1}, {"bbox": [101.500032, 85.500032, 106.499968, 90.499968], "label": "drone", "frame_id": "20", "frame_idx": 20, "sequence_position": 2}, {"bbox": [100.500096, 80.500096, 105.50003199999999, 85.50003199999999], "label": "drone", "frame_id": "30", "frame_idx": 30, "sequence_position": 3}, {"bbox": [104.500096, 83.50016000000001, 109.50003199999999, 88.500096], "label": "drone", "frame_id": "40", "frame_idx": 40, "sequence_position": 4}], "total_annotations": 5}}, {"conversations": [{"from": "human", "value": "这是一个包含5帧的红外视频序列（从data06每隔10帧抽取1帧组成），图像尺寸为256x256，请仔细检测序列中所有帧的弱小目标。目标类别包括：drone, car, ship, bus, pedestrian, cyclist。请以JSON数组格式输出检测结果，每个目标必须包含精确的边界框坐标、类别标签和帧ID。格式要求：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]"}, {"from": "gpt", "value": "[{\"bbox\": [92.500096, 123.50016000000001, 97.50003199999999, 128.500096], \"label\": \"drone\", \"frame_id\": \"0\"}, {\"bbox\": [90.49996800000001, 122.49996800000001, 95.499904, 127.499904], \"label\": \"drone\", \"frame_id\": \"10\"}, {\"bbox\": [90.49996800000001, 124.500096, 95.499904, 129.500032], \"label\": \"drone\", \"frame_id\": \"20\"}, {\"bbox\": [91.50016000000001, 123.50016000000001, 96.500096, 128.500096], \"label\": \"drone\", \"frame_id\": \"30\"}, {\"bbox\": [90.49996800000001, 121.500032, 95.499904, 126.499968], \"label\": \"drone\", \"frame_id\": \"40\"}]"}], "sequence_info": {"sequence_id": "data06_complete_video", "original_sequence": "data06", "video_frames": [{"frame_idx": 0, "frame_id": "0", "file_path": "dataset/images/data06/0.bmp", "sequence_position": 0}, {"frame_idx": 10, "frame_id": "10", "file_path": "dataset/images/data06/10.bmp", "sequence_position": 1}, {"frame_idx": 20, "frame_id": "20", "file_path": "dataset/images/data06/20.bmp", "sequence_position": 2}, {"frame_idx": 30, "frame_id": "30", "file_path": "dataset/images/data06/30.bmp", "sequence_position": 3}, {"frame_idx": 40, "frame_id": "40", "file_path": "dataset/images/data06/40.bmp", "sequence_position": 4}], "frame_count": 5, "frame_interval": 10, "image_size": [256, 256], "naming_system": "simple", "file_format": ".bmp", "annotations": [{"bbox": [92.500096, 123.50016000000001, 97.50003199999999, 128.500096], "label": "drone", "frame_id": "0", "frame_idx": 0, "sequence_position": 0}, {"bbox": [90.49996800000001, 122.49996800000001, 95.499904, 127.499904], "label": "drone", "frame_id": "10", "frame_idx": 10, "sequence_position": 1}, {"bbox": [90.49996800000001, 124.500096, 95.499904, 129.500032], "label": "drone", "frame_id": "20", "frame_idx": 20, "sequence_position": 2}, {"bbox": [91.50016000000001, 123.50016000000001, 96.500096, 128.500096], "label": "drone", "frame_id": "30", "frame_idx": 30, "sequence_position": 3}, {"bbox": [90.49996800000001, 121.500032, 95.499904, 126.499968], "label": "drone", "frame_id": "40", "frame_idx": 40, "sequence_position": 4}], "total_annotations": 5}}, {"conversations": [{"from": "human", "value": "这是一个包含5帧的红外视频序列（从data07每隔10帧抽取1帧组成），图像尺寸为256x256，请仔细检测序列中所有帧的弱小目标。目标类别包括：drone, car, ship, bus, pedestrian, cyclist。请以JSON数组格式输出检测结果，每个目标必须包含精确的边界框坐标、类别标签和帧ID。格式要求：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]"}, {"from": "gpt", "value": "[{\"bbox\": [164.0, 137.99987199999998, 194.00012800000002, 168.0], \"label\": \"drone\", \"frame_id\": \"0\"}, {\"bbox\": [148.999936, 117.999872, 179.000064, 148.0], \"label\": \"drone\", \"frame_id\": \"10\"}, {\"bbox\": [143.00006399999998, 98.999808, 173.000192, 128.999936], \"label\": \"drone\", \"frame_id\": \"20\"}, {\"bbox\": [136.999936, 80.0, 167.000064, 110.00012799999999], \"label\": \"drone\", \"frame_id\": \"30\"}, {\"bbox\": [128.999936, 60.999936, 159.000064, 91.000064], \"label\": \"drone\", \"frame_id\": \"40\"}]"}], "sequence_info": {"sequence_id": "data07_complete_video", "original_sequence": "data07", "video_frames": [{"frame_idx": 0, "frame_id": "0", "file_path": "dataset/images/data07/0.bmp", "sequence_position": 0}, {"frame_idx": 10, "frame_id": "10", "file_path": "dataset/images/data07/10.bmp", "sequence_position": 1}, {"frame_idx": 20, "frame_id": "20", "file_path": "dataset/images/data07/20.bmp", "sequence_position": 2}, {"frame_idx": 30, "frame_id": "30", "file_path": "dataset/images/data07/30.bmp", "sequence_position": 3}, {"frame_idx": 40, "frame_id": "40", "file_path": "dataset/images/data07/40.bmp", "sequence_position": 4}], "frame_count": 5, "frame_interval": 10, "image_size": [256, 256], "naming_system": "simple", "file_format": ".bmp", "annotations": [{"bbox": [164.0, 137.99987199999998, 194.00012800000002, 168.0], "label": "drone", "frame_id": "0", "frame_idx": 0, "sequence_position": 0}, {"bbox": [148.999936, 117.999872, 179.000064, 148.0], "label": "drone", "frame_id": "10", "frame_idx": 10, "sequence_position": 1}, {"bbox": [143.00006399999998, 98.999808, 173.000192, 128.999936], "label": "drone", "frame_id": "20", "frame_idx": 20, "sequence_position": 2}, {"bbox": [136.999936, 80.0, 167.000064, 110.00012799999999], "label": "drone", "frame_id": "30", "frame_idx": 30, "sequence_position": 3}, {"bbox": [128.999936, 60.999936, 159.000064, 91.000064], "label": "drone", "frame_id": "40", "frame_idx": 40, "sequence_position": 4}], "total_annotations": 5}}, {"conversations": [{"from": "human", "value": "这是一个包含5帧的红外视频序列（从data19每隔10帧抽取1帧组成），图像尺寸为640x512，请仔细检测序列中所有帧的弱小目标。目标类别包括：drone, car, ship, bus, pedestrian, cyclist。请以JSON数组格式输出检测结果，每个目标必须包含精确的边界框坐标、类别标签和帧ID。格式要求：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]"}, {"from": "gpt", "value": "[{\"bbox\": [306.00032000000004, 270.00012799999996, 320.00032000000004, 280.999936], \"label\": \"drone\", \"frame_id\": \"merged_dataset_data_transform_1_01_1751_0250-1750_000001\"}, {\"bbox\": [343.0, 269.00019199999997, 356.00032, 280.0], \"label\": \"drone\", \"frame_id\": \"merged_dataset_data_transform_1_01_1751_0250-1750_000011\"}, {\"bbox\": [321.99968, 270.00012799999996, 335.0, 280.999936], \"label\": \"drone\", \"frame_id\": \"merged_dataset_data_transform_1_01_1751_0250-1750_000021\"}, {\"bbox\": [316.99999999999994, 268.999936, 330.00032, 282.00012799999996], \"label\": \"drone\", \"frame_id\": \"merged_dataset_data_transform_1_01_1751_0250-1750_000031\"}, {\"bbox\": [302.99968, 269.999616, 314.99968, 281.99987200000004], \"label\": \"drone\", \"frame_id\": \"merged_dataset_data_transform_1_01_1751_0250-1750_000041\"}]"}], "sequence_info": {"sequence_id": "data19_complete_video", "original_sequence": "data19", "video_frames": [{"frame_idx": 0, "frame_id": "merged_dataset_data_transform_1_01_1751_0250-1750_000001", "file_path": "dataset/images/data19/merged_dataset_data_transform_1_01_1751_0250-1750_000001.jpg", "sequence_position": 0}, {"frame_idx": 10, "frame_id": "merged_dataset_data_transform_1_01_1751_0250-1750_000011", "file_path": "dataset/images/data19/merged_dataset_data_transform_1_01_1751_0250-1750_000011.jpg", "sequence_position": 1}, {"frame_idx": 20, "frame_id": "merged_dataset_data_transform_1_01_1751_0250-1750_000021", "file_path": "dataset/images/data19/merged_dataset_data_transform_1_01_1751_0250-1750_000021.jpg", "sequence_position": 2}, {"frame_idx": 30, "frame_id": "merged_dataset_data_transform_1_01_1751_0250-1750_000031", "file_path": "dataset/images/data19/merged_dataset_data_transform_1_01_1751_0250-1750_000031.jpg", "sequence_position": 3}, {"frame_idx": 40, "frame_id": "merged_dataset_data_transform_1_01_1751_0250-1750_000041", "file_path": "dataset/images/data19/merged_dataset_data_transform_1_01_1751_0250-1750_000041.jpg", "sequence_position": 4}], "frame_count": 5, "frame_interval": 10, "image_size": [640, 512], "naming_system": "complex", "file_format": ".jpg", "annotations": [{"bbox": [306.00032000000004, 270.00012799999996, 320.00032000000004, 280.999936], "label": "drone", "frame_id": "merged_dataset_data_transform_1_01_1751_0250-1750_000001", "frame_idx": 0, "sequence_position": 0}, {"bbox": [343.0, 269.00019199999997, 356.00032, 280.0], "label": "drone", "frame_id": "merged_dataset_data_transform_1_01_1751_0250-1750_000011", "frame_idx": 10, "sequence_position": 1}, {"bbox": [321.99968, 270.00012799999996, 335.0, 280.999936], "label": "drone", "frame_id": "merged_dataset_data_transform_1_01_1751_0250-1750_000021", "frame_idx": 20, "sequence_position": 2}, {"bbox": [316.99999999999994, 268.999936, 330.00032, 282.00012799999996], "label": "drone", "frame_id": "merged_dataset_data_transform_1_01_1751_0250-1750_000031", "frame_idx": 30, "sequence_position": 3}, {"bbox": [302.99968, 269.999616, 314.99968, 281.99987200000004], "label": "drone", "frame_id": "merged_dataset_data_transform_1_01_1751_0250-1750_000041", "frame_idx": 40, "sequence_position": 4}], "total_annotations": 5}}, {"conversations": [{"from": "human", "value": "这是一个包含5帧的红外视频序列（从data23每隔10帧抽取1帧组成），图像尺寸为640x512，请仔细检测序列中所有帧的弱小目标。目标类别包括：drone, car, ship, bus, pedestrian, cyclist。请以JSON数组格式输出检测结果，每个目标必须包含精确的边界框坐标、类别标签和帧ID。格式要求：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]"}, {"from": "gpt", "value": "[{\"bbox\": [349.0, 243.999744, 355.99967999999996, 250.999808], \"label\": \"drone\", \"frame_id\": \"merged_dataset_data_transform_1_new2_train_000001\"}, {\"bbox\": [349.0, 244.999936, 355.99967999999996, 251.000064], \"label\": \"drone\", \"frame_id\": \"merged_dataset_data_transform_1_new2_train_000011\"}, {\"bbox\": [351.0, 244.0, 356.99999999999994, 250.00012800000002], \"label\": \"drone\", \"frame_id\": \"merged_dataset_data_transform_1_new2_train_000021\"}, {\"bbox\": [348.00000000000006, 246.000128, 356.0, 253.000192], \"label\": \"drone\", \"frame_id\": \"merged_dataset_data_transform_1_new2_train_000031\"}, {\"bbox\": [349.0, 244.999936, 355.99967999999996, 251.000064], \"label\": \"drone\", \"frame_id\": \"merged_dataset_data_transform_1_new2_train_000041\"}]"}], "sequence_info": {"sequence_id": "data23_complete_video", "original_sequence": "data23", "video_frames": [{"frame_idx": 0, "frame_id": "merged_dataset_data_transform_1_new2_train_000001", "file_path": "dataset/images/data23/merged_dataset_data_transform_1_new2_train_000001.jpg", "sequence_position": 0}, {"frame_idx": 10, "frame_id": "merged_dataset_data_transform_1_new2_train_000011", "file_path": "dataset/images/data23/merged_dataset_data_transform_1_new2_train_000011.jpg", "sequence_position": 1}, {"frame_idx": 20, "frame_id": "merged_dataset_data_transform_1_new2_train_000021", "file_path": "dataset/images/data23/merged_dataset_data_transform_1_new2_train_000021.jpg", "sequence_position": 2}, {"frame_idx": 30, "frame_id": "merged_dataset_data_transform_1_new2_train_000031", "file_path": "dataset/images/data23/merged_dataset_data_transform_1_new2_train_000031.jpg", "sequence_position": 3}, {"frame_idx": 40, "frame_id": "merged_dataset_data_transform_1_new2_train_000041", "file_path": "dataset/images/data23/merged_dataset_data_transform_1_new2_train_000041.jpg", "sequence_position": 4}], "frame_count": 5, "frame_interval": 10, "image_size": [640, 512], "naming_system": "complex", "file_format": ".jpg", "annotations": [{"bbox": [349.0, 243.999744, 355.99967999999996, 250.999808], "label": "drone", "frame_id": "merged_dataset_data_transform_1_new2_train_000001", "frame_idx": 0, "sequence_position": 0}, {"bbox": [349.0, 244.999936, 355.99967999999996, 251.000064], "label": "drone", "frame_id": "merged_dataset_data_transform_1_new2_train_000011", "frame_idx": 10, "sequence_position": 1}, {"bbox": [351.0, 244.0, 356.99999999999994, 250.00012800000002], "label": "drone", "frame_id": "merged_dataset_data_transform_1_new2_train_000021", "frame_idx": 20, "sequence_position": 2}, {"bbox": [348.00000000000006, 246.000128, 356.0, 253.000192], "label": "drone", "frame_id": "merged_dataset_data_transform_1_new2_train_000031", "frame_idx": 30, "sequence_position": 3}, {"bbox": [349.0, 244.999936, 355.99967999999996, 251.000064], "label": "drone", "frame_id": "merged_dataset_data_transform_1_new2_train_000041", "frame_idx": 40, "sequence_position": 4}], "total_annotations": 5}}, {"conversations": [{"from": "human", "value": "这是一个包含5帧的红外视频序列（从data26每隔10帧抽取1帧组成），图像尺寸为640x512，请仔细检测序列中所有帧的弱小目标。目标类别包括：drone, car, ship, bus, pedestrian, cyclist。请以JSON数组格式输出检测结果，每个目标必须包含精确的边界框坐标、类别标签和帧ID。格式要求：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}]"}, {"from": "gpt", "value": "[{\"bbox\": [535.00032, 83.00032, 539.00032, 87.000064], \"label\": \"drone\", \"frame_id\": \"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000001\"}, {\"bbox\": [532.0003199999999, 82.000128, 535.0, 84.999936], \"label\": \"drone\", \"frame_id\": \"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000011\"}, {\"bbox\": [531.0, 82.000128, 533.99968, 84.999936], \"label\": \"drone\", \"frame_id\": \"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000021\"}, {\"bbox\": [531.0, 81.000192, 533.99968, 84.0], \"label\": \"drone\", \"frame_id\": \"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000031\"}, {\"bbox\": [532.0003199999999, 81.000192, 535.0, 84.0], \"label\": \"drone\", \"frame_id\": \"merged_dataset_data_transform_1_wg2022_ir_053_split_06_000041\"}]"}], "sequence_info": {"sequence_id": "data26_complete_video", "original_sequence": "data26", "video_frames": [{"frame_idx": 0, "frame_id": "merged_dataset_data_transform_1_wg2022_ir_053_split_06_000001", "file_path": "dataset/images/data26/merged_dataset_data_transform_1_wg2022_ir_053_split_06_000001.jpg", "sequence_position": 0}, {"frame_idx": 10, "frame_id": "merged_dataset_data_transform_1_wg2022_ir_053_split_06_000011", "file_path": "dataset/images/data26/merged_dataset_data_transform_1_wg2022_ir_053_split_06_000011.jpg", "sequence_position": 1}, {"frame_idx": 20, "frame_id": "merged_dataset_data_transform_1_wg2022_ir_053_split_06_000021", "file_path": "dataset/images/data26/merged_dataset_data_transform_1_wg2022_ir_053_split_06_000021.jpg", "sequence_position": 2}, {"frame_idx": 30, "frame_id": "merged_dataset_data_transform_1_wg2022_ir_053_split_06_000031", "file_path": "dataset/images/data26/merged_dataset_data_transform_1_wg2022_ir_053_split_06_000031.jpg", "sequence_position": 3}, {"frame_idx": 40, "frame_id": "merged_dataset_data_transform_1_wg2022_ir_053_split_06_000041", "file_path": "dataset/images/data26/merged_dataset_data_transform_1_wg2022_ir_053_split_06_000041.jpg", "sequence_position": 4}], "frame_count": 5, "frame_interval": 10, "image_size": [640, 512], "naming_system": "complex", "file_format": ".jpg", "annotations": [{"bbox": [535.00032, 83.00032, 539.00032, 87.000064], "label": "drone", "frame_id": "merged_dataset_data_transform_1_wg2022_ir_053_split_06_000001", "frame_idx": 0, "sequence_position": 0}, {"bbox": [532.0003199999999, 82.000128, 535.0, 84.999936], "label": "drone", "frame_id": "merged_dataset_data_transform_1_wg2022_ir_053_split_06_000011", "frame_idx": 10, "sequence_position": 1}, {"bbox": [531.0, 82.000128, 533.99968, 84.999936], "label": "drone", "frame_id": "merged_dataset_data_transform_1_wg2022_ir_053_split_06_000021", "frame_idx": 20, "sequence_position": 2}, {"bbox": [531.0, 81.000192, 533.99968, 84.0], "label": "drone", "frame_id": "merged_dataset_data_transform_1_wg2022_ir_053_split_06_000031", "frame_idx": 30, "sequence_position": 3}, {"bbox": [532.0003199999999, 81.000192, 535.0, 84.0], "label": "drone", "frame_id": "merged_dataset_data_transform_1_wg2022_ir_053_split_06_000041", "frame_idx": 40, "sequence_position": 4}], "total_annotations": 5}}]