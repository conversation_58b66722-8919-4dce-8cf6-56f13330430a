{"best_global_step": null, "best_metric": null, "best_model_checkpoint": null, "epoch": 1.56, "eval_steps": 500, "global_step": 20, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.4, "grad_norm": 5.644313335418701, "learning_rate": 4.000000000000001e-06, "loss": 25.905, "step": 5}, {"epoch": 0.8, "grad_norm": 6.430604457855225, "learning_rate": 1.4e-05, "loss": 22.6474, "step": 10}, {"epoch": 1.16, "grad_norm": 6.3547868728637695, "learning_rate": 1.9272727272727275e-05, "loss": 22.7029, "step": 15}, {"epoch": 1.56, "grad_norm": 7.501271724700928, "learning_rate": 1.7454545454545456e-05, "loss": 23.3908, "step": 20}], "logging_steps": 5, "max_steps": 65, "num_input_tokens_seen": 0, "num_train_epochs": 5, "save_steps": 20, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 7419754035836928.0, "train_batch_size": 1, "trial_name": null, "trial_params": null}