{"best_global_step": null, "best_metric": null, "best_model_checkpoint": null, "epoch": 9.092485549132949, "eval_steps": 500, "global_step": 200, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.4624277456647399, "grad_norm": 16.23053741455078, "learning_rate": 1.4000000000000001e-06, "loss": 68.9177, "step": 10}, {"epoch": 0.9248554913294798, "grad_norm": 18.52475357055664, "learning_rate": 3.4000000000000005e-06, "loss": 68.0073, "step": 20}, {"epoch": 1.369942196531792, "grad_norm": 17.763187408447266, "learning_rate": 5.400000000000001e-06, "loss": 65.8325, "step": 30}, {"epoch": 1.8323699421965318, "grad_norm": NaN, "learning_rate": 7.2000000000000005e-06, "loss": 67.806, "step": 40}, {"epoch": 2.277456647398844, "grad_norm": 20.555334091186523, "learning_rate": 9e-06, "loss": 64.341, "step": 50}, {"epoch": 2.739884393063584, "grad_norm": 22.47884178161621, "learning_rate": 9.978670881475173e-06, "loss": 66.9605, "step": 60}, {"epoch": 3.184971098265896, "grad_norm": 23.919353485107422, "learning_rate": 9.809128215864096e-06, "loss": 62.8618, "step": 70}, {"epoch": 3.647398843930636, "grad_norm": 28.59227752685547, "learning_rate": 9.475816456775313e-06, "loss": 65.0195, "step": 80}, {"epoch": 4.092485549132948, "grad_norm": 29.46798324584961, "learning_rate": 8.990086136401199e-06, "loss": 61.0035, "step": 90}, {"epoch": 4.554913294797688, "grad_norm": 36.03175735473633, "learning_rate": 8.368478218232787e-06, "loss": 62.8837, "step": 100}, {"epoch": 5.0, "grad_norm": 23.931968688964844, "learning_rate": 7.63216081438678e-06, "loss": 57.1652, "step": 110}, {"epoch": 5.46242774566474, "grad_norm": 38.889713287353516, "learning_rate": 6.806208330935766e-06, "loss": 59.3373, "step": 120}, {"epoch": 5.924855491329479, "grad_norm": 42.83644104003906, "learning_rate": 5.918747589082853e-06, "loss": 56.0971, "step": 130}, {"epoch": 6.369942196531792, "grad_norm": 47.46303176879883, "learning_rate": 5e-06, "loss": 53.327, "step": 140}, {"epoch": 6.832369942196532, "grad_norm": 45.330326080322266, "learning_rate": 4.081252410917148e-06, "loss": 54.0214, "step": 150}, {"epoch": 7.277456647398844, "grad_norm": 45.48734664916992, "learning_rate": 3.1937916690642356e-06, "loss": 50.5732, "step": 160}, {"epoch": 7.7398843930635834, "grad_norm": 45.238990783691406, "learning_rate": 2.3678391856132203e-06, "loss": 52.1344, "step": 170}, {"epoch": 8.184971098265896, "grad_norm": 41.16489028930664, "learning_rate": 1.6315217817672142e-06, "loss": 49.4014, "step": 180}, {"epoch": 8.647398843930636, "grad_norm": 43.940338134765625, "learning_rate": 1.0099138635988026e-06, "loss": 50.7034, "step": 190}, {"epoch": 9.092485549132949, "grad_norm": 43.88916778564453, "learning_rate": 5.241835432246888e-07, "loss": 48.0145, "step": 200}], "logging_steps": 10, "max_steps": 220, "num_input_tokens_seen": 0, "num_train_epochs": 10, "save_steps": 50, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 1.053708425626583e+17, "train_batch_size": 1, "trial_name": null, "trial_params": null}