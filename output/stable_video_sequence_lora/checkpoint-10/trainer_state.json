{"best_global_step": null, "best_metric": null, "best_model_checkpoint": null, "epoch": 5.0, "eval_steps": 500, "global_step": 10, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 1.0, "grad_norm": NaN, "learning_rate": 1e-05, "loss": 37.6622, "step": 2}, {"epoch": 2.0, "grad_norm": 9.11827278137207, "learning_rate": 2e-05, "loss": 37.6534, "step": 4}, {"epoch": 3.0, "grad_norm": 9.043699264526367, "learning_rate": 4e-05, "loss": 37.541, "step": 6}, {"epoch": 4.0, "grad_norm": 9.885262489318848, "learning_rate": 4.666666666666667e-05, "loss": 37.2552, "step": 8}, {"epoch": 5.0, "grad_norm": 10.037759780883789, "learning_rate": 4e-05, "loss": 36.8982, "step": 10}], "logging_steps": 2, "max_steps": 20, "num_input_tokens_seen": 0, "num_train_epochs": 10, "save_steps": 10, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 616068569917440.0, "train_batch_size": 1, "trial_name": null, "trial_params": null}