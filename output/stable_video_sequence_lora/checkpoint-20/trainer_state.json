{"best_global_step": null, "best_metric": null, "best_model_checkpoint": null, "epoch": 10.0, "eval_steps": 500, "global_step": 20, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 1.0, "grad_norm": NaN, "learning_rate": 1e-05, "loss": 37.6622, "step": 2}, {"epoch": 2.0, "grad_norm": 9.11827278137207, "learning_rate": 2e-05, "loss": 37.6534, "step": 4}, {"epoch": 3.0, "grad_norm": 9.043699264526367, "learning_rate": 4e-05, "loss": 37.541, "step": 6}, {"epoch": 4.0, "grad_norm": 9.885262489318848, "learning_rate": 4.666666666666667e-05, "loss": 37.2552, "step": 8}, {"epoch": 5.0, "grad_norm": 10.037759780883789, "learning_rate": 4e-05, "loss": 36.8982, "step": 10}, {"epoch": 6.0, "grad_norm": 10.500056266784668, "learning_rate": 3.3333333333333335e-05, "loss": 36.559, "step": 12}, {"epoch": 7.0, "grad_norm": 11.423871040344238, "learning_rate": 2.6666666666666667e-05, "loss": 36.2279, "step": 14}, {"epoch": 8.0, "grad_norm": 12.300406455993652, "learning_rate": 2e-05, "loss": 35.9579, "step": 16}, {"epoch": 9.0, "grad_norm": 12.28969669342041, "learning_rate": 1.3333333333333333e-05, "loss": 35.7415, "step": 18}, {"epoch": 10.0, "grad_norm": 12.501113891601562, "learning_rate": 6.666666666666667e-06, "loss": 35.5845, "step": 20}], "logging_steps": 2, "max_steps": 20, "num_input_tokens_seen": 0, "num_train_epochs": 10, "save_steps": 10, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": true}, "attributes": {}}}, "total_flos": 1232137139834880.0, "train_batch_size": 1, "trial_name": null, "trial_params": null}