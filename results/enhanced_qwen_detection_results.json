{"data01": {"sequence_id": "data01", "total_frames": 75, "key_frames": 8, "total_detections": 80, "direct_detections": 13, "interpolated_detections": 67, "metrics": {"precision": 0.0, "recall": 0.0, "f1": 0, "tp": 0, "fp": 80, "fn": 75}, "detections": {"0": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "0", "frame_idx": 0}, {"bbox": [97, 48, 102, 53], "label": "drone", "frame_id": "0", "frame_idx": 0}, {"bbox": [102, 48, 107, 53], "label": "car", "frame_id": "0", "frame_idx": 0}, {"bbox": [107, 48, 112, 53], "label": "ship", "frame_id": "0", "frame_idx": 0}, {"bbox": [112, 48, 117, 53], "label": "bus", "frame_id": "0", "frame_idx": 0}, {"bbox": [1, 48, 6, 53], "label": "pedestrian", "frame_id": "0", "frame_idx": 0}], "10": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "10", "frame_idx": 10}], "20": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_idx": 20, "frame_id": "20"}], "30": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "30", "frame_idx": 30}], "40": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "40", "frame_idx": 40}], "50": [{"bbox": [98, 71, 103, 76], "label": "drone", "frame_id": "50", "frame_idx": 50}], "60": [{"bbox": [97, 80, 102, 85], "label": "drone", "frame_id": "60", "frame_idx": 60}], "70": [{"bbox": [98, 100, 105, 107], "label": "drone", "frame_id": "70", "frame_idx": 70}], "1": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "1", "frame_idx": 1, "interpolated": true, "interpolated_from": [0, 10]}], "2": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "2", "frame_idx": 2, "interpolated": true, "interpolated_from": [0, 10]}], "3": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "3", "frame_idx": 3, "interpolated": true, "interpolated_from": [0, 10]}], "4": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "4", "frame_idx": 4, "interpolated": true, "interpolated_from": [0, 10]}], "5": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "5", "frame_idx": 5, "interpolated": true, "interpolated_from": [0, 10]}], "6": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "6", "frame_idx": 6, "interpolated": true, "interpolated_from": [0, 10]}], "7": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "7", "frame_idx": 7, "interpolated": true, "interpolated_from": [0, 10]}], "8": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "8", "frame_idx": 8, "interpolated": true, "interpolated_from": [0, 10]}], "9": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "9", "frame_idx": 9, "interpolated": true, "interpolated_from": [0, 10]}], "11": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "11", "frame_idx": 11, "interpolated": true, "interpolated_from": [10, 20]}], "12": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "12", "frame_idx": 12, "interpolated": true, "interpolated_from": [10, 20]}], "13": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "13", "frame_idx": 13, "interpolated": true, "interpolated_from": [10, 20]}], "14": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "14", "frame_idx": 14, "interpolated": true, "interpolated_from": [10, 20]}], "15": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "15", "frame_idx": 15, "interpolated": true, "interpolated_from": [10, 20]}], "16": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "16", "frame_idx": 16, "interpolated": true, "interpolated_from": [10, 20]}], "17": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "17", "frame_idx": 17, "interpolated": true, "interpolated_from": [10, 20]}], "18": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "18", "frame_idx": 18, "interpolated": true, "interpolated_from": [10, 20]}], "19": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "19", "frame_idx": 19, "interpolated": true, "interpolated_from": [10, 20]}], "21": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "21", "frame_idx": 21, "interpolated": true, "interpolated_from": [20, 30]}], "22": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "22", "frame_idx": 22, "interpolated": true, "interpolated_from": [20, 30]}], "23": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "23", "frame_idx": 23, "interpolated": true, "interpolated_from": [20, 30]}], "24": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "24", "frame_idx": 24, "interpolated": true, "interpolated_from": [20, 30]}], "25": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "25", "frame_idx": 25, "interpolated": true, "interpolated_from": [20, 30]}], "26": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "26", "frame_idx": 26, "interpolated": true, "interpolated_from": [20, 30]}], "27": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "27", "frame_idx": 27, "interpolated": true, "interpolated_from": [20, 30]}], "28": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "28", "frame_idx": 28, "interpolated": true, "interpolated_from": [20, 30]}], "29": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "29", "frame_idx": 29, "interpolated": true, "interpolated_from": [20, 30]}], "31": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "31", "frame_idx": 31, "interpolated": true, "interpolated_from": [30, 40]}], "32": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "32", "frame_idx": 32, "interpolated": true, "interpolated_from": [30, 40]}], "33": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "33", "frame_idx": 33, "interpolated": true, "interpolated_from": [30, 40]}], "34": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "34", "frame_idx": 34, "interpolated": true, "interpolated_from": [30, 40]}], "35": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "35", "frame_idx": 35, "interpolated": true, "interpolated_from": [30, 40]}], "36": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "36", "frame_idx": 36, "interpolated": true, "interpolated_from": [30, 40]}], "37": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "37", "frame_idx": 37, "interpolated": true, "interpolated_from": [30, 40]}], "38": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "38", "frame_idx": 38, "interpolated": true, "interpolated_from": [30, 40]}], "39": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "39", "frame_idx": 39, "interpolated": true, "interpolated_from": [30, 40]}], "41": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "41", "frame_idx": 41, "interpolated": true, "interpolated_from": [40, 50]}], "42": [{"bbox": [98, 77, 103, 82], "label": "drone", "frame_id": "42", "frame_idx": 42, "interpolated": true, "interpolated_from": [40, 50]}], "43": [{"bbox": [98, 76, 103, 81], "label": "drone", "frame_id": "43", "frame_idx": 43, "interpolated": true, "interpolated_from": [40, 50]}], "44": [{"bbox": [98, 75, 103, 80], "label": "drone", "frame_id": "44", "frame_idx": 44, "interpolated": true, "interpolated_from": [40, 50]}], "45": [{"bbox": [98, 75, 103, 80], "label": "drone", "frame_id": "45", "frame_idx": 45, "interpolated": true, "interpolated_from": [40, 50]}], "46": [{"bbox": [98, 74, 103, 79], "label": "drone", "frame_id": "46", "frame_idx": 46, "interpolated": true, "interpolated_from": [40, 50]}], "47": [{"bbox": [98, 73, 103, 78], "label": "drone", "frame_id": "47", "frame_idx": 47, "interpolated": true, "interpolated_from": [40, 50]}], "48": [{"bbox": [98, 72, 103, 77], "label": "drone", "frame_id": "48", "frame_idx": 48, "interpolated": true, "interpolated_from": [40, 50]}], "49": [{"bbox": [98, 71, 103, 76], "label": "drone", "frame_id": "49", "frame_idx": 49, "interpolated": true, "interpolated_from": [40, 50]}], "51": [{"bbox": [97, 71, 102, 76], "label": "drone", "frame_id": "51", "frame_idx": 51, "interpolated": true, "interpolated_from": [50, 60]}], "52": [{"bbox": [97, 72, 102, 77], "label": "drone", "frame_id": "52", "frame_idx": 52, "interpolated": true, "interpolated_from": [50, 60]}], "53": [{"bbox": [97, 73, 102, 78], "label": "drone", "frame_id": "53", "frame_idx": 53, "interpolated": true, "interpolated_from": [50, 60]}], "54": [{"bbox": [97, 74, 102, 79], "label": "drone", "frame_id": "54", "frame_idx": 54, "interpolated": true, "interpolated_from": [50, 60]}], "55": [{"bbox": [97, 75, 102, 80], "label": "drone", "frame_id": "55", "frame_idx": 55, "interpolated": true, "interpolated_from": [50, 60]}], "56": [{"bbox": [97, 76, 102, 81], "label": "drone", "frame_id": "56", "frame_idx": 56, "interpolated": true, "interpolated_from": [50, 60]}], "57": [{"bbox": [97, 77, 102, 82], "label": "drone", "frame_id": "57", "frame_idx": 57, "interpolated": true, "interpolated_from": [50, 60]}], "58": [{"bbox": [97, 78, 102, 83], "label": "drone", "frame_id": "58", "frame_idx": 58, "interpolated": true, "interpolated_from": [50, 60]}], "59": [{"bbox": [97, 79, 102, 84], "label": "drone", "frame_id": "59", "frame_idx": 59, "interpolated": true, "interpolated_from": [50, 60]}], "61": [{"bbox": [97, 82, 102, 87], "label": "drone", "frame_id": "61", "frame_idx": 61, "interpolated": true, "interpolated_from": [60, 70]}], "62": [{"bbox": [97, 84, 102, 89], "label": "drone", "frame_id": "62", "frame_idx": 62, "interpolated": true, "interpolated_from": [60, 70]}], "63": [{"bbox": [97, 86, 102, 91], "label": "drone", "frame_id": "63", "frame_idx": 63, "interpolated": true, "interpolated_from": [60, 70]}], "64": [{"bbox": [97, 88, 103, 93], "label": "drone", "frame_id": "64", "frame_idx": 64, "interpolated": true, "interpolated_from": [60, 70]}], "65": [{"bbox": [97, 90, 103, 96], "label": "drone", "frame_id": "65", "frame_idx": 65, "interpolated": true, "interpolated_from": [60, 70]}], "66": [{"bbox": [97, 92, 103, 98], "label": "drone", "frame_id": "66", "frame_idx": 66, "interpolated": true, "interpolated_from": [60, 70]}], "67": [{"bbox": [97, 94, 104, 100], "label": "drone", "frame_id": "67", "frame_idx": 67, "interpolated": true, "interpolated_from": [60, 70]}], "68": [{"bbox": [97, 96, 104, 102], "label": "drone", "frame_id": "68", "frame_idx": 68, "interpolated": true, "interpolated_from": [60, 70]}], "69": [{"bbox": [97, 98, 104, 104], "label": "drone", "frame_id": "69", "frame_idx": 69, "interpolated": true, "interpolated_from": [60, 70]}], "71": [{"bbox": [98, 100, 105, 107], "label": "drone", "frame_id": "71", "frame_idx": 71, "interpolated": true, "interpolated_from": [70]}], "72": [{"bbox": [98, 100, 105, 107], "label": "drone", "frame_id": "72", "frame_idx": 72, "interpolated": true, "interpolated_from": [70]}], "73": [{"bbox": [98, 100, 105, 107], "label": "drone", "frame_id": "73", "frame_idx": 73, "interpolated": true, "interpolated_from": [70]}], "74": [{"bbox": [98, 100, 105, 107], "label": "drone", "frame_id": "74", "frame_idx": 74, "interpolated": true, "interpolated_from": [70]}]}}, "data02": {"sequence_id": "data02", "total_frames": 50, "key_frames": 5, "total_detections": 52, "direct_detections": 7, "interpolated_detections": 45, "metrics": {"precision": 0.0, "recall": 0.0, "f1": 0, "tp": 0, "fp": 52, "fn": 50}, "detections": {"0": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "0", "frame_idx": 0}, {"bbox": [98, 74, 108, 84], "label": "drone", "frame_id": "0", "frame_idx": 0}, {"bbox": [100, 74, 109, 84], "label": "drone", "frame_id": "0", "frame_idx": 0}], "10": [{"bbox": [97, 85, 102, 90], "label": "drone", "frame_id": "10", "frame_idx": 10}], "20": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "20", "frame_idx": 20}], "30": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "30", "frame_idx": 30}], "40": [{"bbox": [97, 86, 102, 91], "label": "drone", "frame_id": "40", "frame_idx": 40}], "1": [{"bbox": [97, 78, 102, 83], "label": "drone", "frame_id": "1", "frame_idx": 1, "interpolated": true, "interpolated_from": [0, 10]}], "2": [{"bbox": [97, 79, 102, 84], "label": "drone", "frame_id": "2", "frame_idx": 2, "interpolated": true, "interpolated_from": [0, 10]}], "3": [{"bbox": [97, 80, 102, 85], "label": "drone", "frame_id": "3", "frame_idx": 3, "interpolated": true, "interpolated_from": [0, 10]}], "4": [{"bbox": [97, 80, 102, 85], "label": "drone", "frame_id": "4", "frame_idx": 4, "interpolated": true, "interpolated_from": [0, 10]}], "5": [{"bbox": [97, 81, 102, 86], "label": "drone", "frame_id": "5", "frame_idx": 5, "interpolated": true, "interpolated_from": [0, 10]}], "6": [{"bbox": [97, 82, 102, 87], "label": "drone", "frame_id": "6", "frame_idx": 6, "interpolated": true, "interpolated_from": [0, 10]}], "7": [{"bbox": [97, 82, 102, 87], "label": "drone", "frame_id": "7", "frame_idx": 7, "interpolated": true, "interpolated_from": [0, 10]}], "8": [{"bbox": [97, 83, 102, 88], "label": "drone", "frame_id": "8", "frame_idx": 8, "interpolated": true, "interpolated_from": [0, 10]}], "9": [{"bbox": [97, 84, 102, 89], "label": "drone", "frame_id": "9", "frame_idx": 9, "interpolated": true, "interpolated_from": [0, 10]}], "11": [{"bbox": [97, 84, 102, 89], "label": "drone", "frame_id": "11", "frame_idx": 11, "interpolated": true, "interpolated_from": [10, 20]}], "12": [{"bbox": [97, 83, 102, 88], "label": "drone", "frame_id": "12", "frame_idx": 12, "interpolated": true, "interpolated_from": [10, 20]}], "13": [{"bbox": [97, 82, 102, 87], "label": "drone", "frame_id": "13", "frame_idx": 13, "interpolated": true, "interpolated_from": [10, 20]}], "14": [{"bbox": [97, 82, 102, 87], "label": "drone", "frame_id": "14", "frame_idx": 14, "interpolated": true, "interpolated_from": [10, 20]}], "15": [{"bbox": [97, 81, 102, 86], "label": "drone", "frame_id": "15", "frame_idx": 15, "interpolated": true, "interpolated_from": [10, 20]}], "16": [{"bbox": [97, 80, 102, 85], "label": "drone", "frame_id": "16", "frame_idx": 16, "interpolated": true, "interpolated_from": [10, 20]}], "17": [{"bbox": [97, 80, 102, 85], "label": "drone", "frame_id": "17", "frame_idx": 17, "interpolated": true, "interpolated_from": [10, 20]}], "18": [{"bbox": [97, 79, 102, 84], "label": "drone", "frame_id": "18", "frame_idx": 18, "interpolated": true, "interpolated_from": [10, 20]}], "19": [{"bbox": [97, 78, 102, 83], "label": "drone", "frame_id": "19", "frame_idx": 19, "interpolated": true, "interpolated_from": [10, 20]}], "21": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "21", "frame_idx": 21, "interpolated": true, "interpolated_from": [20, 30]}], "22": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "22", "frame_idx": 22, "interpolated": true, "interpolated_from": [20, 30]}], "23": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "23", "frame_idx": 23, "interpolated": true, "interpolated_from": [20, 30]}], "24": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "24", "frame_idx": 24, "interpolated": true, "interpolated_from": [20, 30]}], "25": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "25", "frame_idx": 25, "interpolated": true, "interpolated_from": [20, 30]}], "26": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "26", "frame_idx": 26, "interpolated": true, "interpolated_from": [20, 30]}], "27": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "27", "frame_idx": 27, "interpolated": true, "interpolated_from": [20, 30]}], "28": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "28", "frame_idx": 28, "interpolated": true, "interpolated_from": [20, 30]}], "29": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "29", "frame_idx": 29, "interpolated": true, "interpolated_from": [20, 30]}], "31": [{"bbox": [97, 79, 102, 84], "label": "drone", "frame_id": "31", "frame_idx": 31, "interpolated": true, "interpolated_from": [30, 40]}], "32": [{"bbox": [97, 80, 102, 85], "label": "drone", "frame_id": "32", "frame_idx": 32, "interpolated": true, "interpolated_from": [30, 40]}], "33": [{"bbox": [97, 81, 102, 86], "label": "drone", "frame_id": "33", "frame_idx": 33, "interpolated": true, "interpolated_from": [30, 40]}], "34": [{"bbox": [97, 81, 102, 86], "label": "drone", "frame_id": "34", "frame_idx": 34, "interpolated": true, "interpolated_from": [30, 40]}], "35": [{"bbox": [97, 82, 102, 87], "label": "drone", "frame_id": "35", "frame_idx": 35, "interpolated": true, "interpolated_from": [30, 40]}], "36": [{"bbox": [97, 83, 102, 88], "label": "drone", "frame_id": "36", "frame_idx": 36, "interpolated": true, "interpolated_from": [30, 40]}], "37": [{"bbox": [97, 83, 102, 88], "label": "drone", "frame_id": "37", "frame_idx": 37, "interpolated": true, "interpolated_from": [30, 40]}], "38": [{"bbox": [97, 84, 102, 89], "label": "drone", "frame_id": "38", "frame_idx": 38, "interpolated": true, "interpolated_from": [30, 40]}], "39": [{"bbox": [97, 85, 102, 90], "label": "drone", "frame_id": "39", "frame_idx": 39, "interpolated": true, "interpolated_from": [30, 40]}], "41": [{"bbox": [97, 86, 102, 91], "label": "drone", "frame_id": "41", "frame_idx": 41, "interpolated": true, "interpolated_from": [40]}], "42": [{"bbox": [97, 86, 102, 91], "label": "drone", "frame_id": "42", "frame_idx": 42, "interpolated": true, "interpolated_from": [40]}], "43": [{"bbox": [97, 86, 102, 91], "label": "drone", "frame_id": "43", "frame_idx": 43, "interpolated": true, "interpolated_from": [40]}], "44": [{"bbox": [97, 86, 102, 91], "label": "drone", "frame_id": "44", "frame_idx": 44, "interpolated": true, "interpolated_from": [40]}], "45": [{"bbox": [97, 86, 102, 91], "label": "drone", "frame_id": "45", "frame_idx": 45, "interpolated": true, "interpolated_from": [40]}], "46": [{"bbox": [97, 86, 102, 91], "label": "drone", "frame_id": "46", "frame_idx": 46, "interpolated": true, "interpolated_from": [40]}], "47": [{"bbox": [97, 86, 102, 91], "label": "drone", "frame_id": "47", "frame_idx": 47, "interpolated": true, "interpolated_from": [40]}], "48": [{"bbox": [97, 86, 102, 91], "label": "drone", "frame_id": "48", "frame_idx": 48, "interpolated": true, "interpolated_from": [40]}], "49": [{"bbox": [97, 86, 102, 91], "label": "drone", "frame_id": "49", "frame_idx": 49, "interpolated": true, "interpolated_from": [40]}]}}, "data04": {"sequence_id": "data04", "total_frames": 50, "key_frames": 5, "total_detections": 82, "direct_detections": 10, "interpolated_detections": 72, "metrics": {"precision": 0.012195121951219513, "recall": 0.02, "f1": 0.015151515151515152, "tp": 1, "fp": 81, "fn": 49}, "detections": {"0": [{"bbox": [98, 77, 103, 82], "label": "drone", "frame_id": "0", "frame_idx": 0}, {"bbox": [98, 78, 108, 88], "label": "drone", "frame_id": "0", "frame_idx": 0}, {"bbox": [100, 80, 110, 90], "label": "car", "frame": "10", "id": "1", "frame_id": "0", "frame_idx": 0}], "10": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "10", "frame_idx": 10}], "20": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "20", "frame_idx": 20}, {"bbox": [100, 80, 110, 90], "label": "ship", "frame_id": "20", "id": "2", "frame_idx": 20}], "30": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "30", "frame_idx": 30}, {"bbox": [100, 80, 110, 90], "label": "bus", "frame_id": "30", "id": "3", "frame_idx": 30}], "40": [{"bbox": [97, 88, 102, 93], "label": "drone", "frame_id": "40", "frame_idx": 40}, {"bbox": [100, 80, 110, 90], "label": "pedestrian", "frame_id": "40", "id": "4", "frame_idx": 40}], "1": [{"bbox": [98, 77, 103, 82], "label": "drone", "frame_id": "1", "frame_idx": 1, "interpolated": true, "interpolated_from": [0, 10]}], "2": [{"bbox": [98, 77, 103, 82], "label": "drone", "frame_id": "2", "frame_idx": 2, "interpolated": true, "interpolated_from": [0, 10]}], "3": [{"bbox": [98, 77, 103, 82], "label": "drone", "frame_id": "3", "frame_idx": 3, "interpolated": true, "interpolated_from": [0, 10]}], "4": [{"bbox": [98, 77, 103, 82], "label": "drone", "frame_id": "4", "frame_idx": 4, "interpolated": true, "interpolated_from": [0, 10]}], "5": [{"bbox": [98, 77, 103, 82], "label": "drone", "frame_id": "5", "frame_idx": 5, "interpolated": true, "interpolated_from": [0, 10]}], "6": [{"bbox": [98, 77, 103, 82], "label": "drone", "frame_id": "6", "frame_idx": 6, "interpolated": true, "interpolated_from": [0, 10]}], "7": [{"bbox": [98, 77, 103, 82], "label": "drone", "frame_id": "7", "frame_idx": 7, "interpolated": true, "interpolated_from": [0, 10]}], "8": [{"bbox": [98, 77, 103, 82], "label": "drone", "frame_id": "8", "frame_idx": 8, "interpolated": true, "interpolated_from": [0, 10]}], "9": [{"bbox": [98, 77, 103, 82], "label": "drone", "frame_id": "9", "frame_idx": 9, "interpolated": true, "interpolated_from": [0, 10]}], "11": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "11", "frame_idx": 11, "interpolated": true, "interpolated_from": [10, 20]}], "12": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "12", "frame_idx": 12, "interpolated": true, "interpolated_from": [10, 20]}], "13": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "13", "frame_idx": 13, "interpolated": true, "interpolated_from": [10, 20]}], "14": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "14", "frame_idx": 14, "interpolated": true, "interpolated_from": [10, 20]}], "15": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "15", "frame_idx": 15, "interpolated": true, "interpolated_from": [10, 20]}], "16": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "16", "frame_idx": 16, "interpolated": true, "interpolated_from": [10, 20]}], "17": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "17", "frame_idx": 17, "interpolated": true, "interpolated_from": [10, 20]}], "18": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "18", "frame_idx": 18, "interpolated": true, "interpolated_from": [10, 20]}], "19": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "19", "frame_idx": 19, "interpolated": true, "interpolated_from": [10, 20]}], "21": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "21", "frame_idx": 21, "interpolated": true, "interpolated_from": [20, 30]}, {"bbox": [100, 80, 110, 90], "label": "ship", "frame_id": "21", "frame_idx": 21, "interpolated": true, "interpolated_from": [20, 30]}], "22": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "22", "frame_idx": 22, "interpolated": true, "interpolated_from": [20, 30]}, {"bbox": [100, 80, 110, 90], "label": "ship", "frame_id": "22", "frame_idx": 22, "interpolated": true, "interpolated_from": [20, 30]}], "23": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "23", "frame_idx": 23, "interpolated": true, "interpolated_from": [20, 30]}, {"bbox": [100, 80, 110, 90], "label": "ship", "frame_id": "23", "frame_idx": 23, "interpolated": true, "interpolated_from": [20, 30]}], "24": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "24", "frame_idx": 24, "interpolated": true, "interpolated_from": [20, 30]}, {"bbox": [100, 80, 110, 90], "label": "ship", "frame_id": "24", "frame_idx": 24, "interpolated": true, "interpolated_from": [20, 30]}], "25": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "25", "frame_idx": 25, "interpolated": true, "interpolated_from": [20, 30]}, {"bbox": [100, 80, 110, 90], "label": "ship", "frame_id": "25", "frame_idx": 25, "interpolated": true, "interpolated_from": [20, 30]}], "26": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "26", "frame_idx": 26, "interpolated": true, "interpolated_from": [20, 30]}, {"bbox": [100, 80, 110, 90], "label": "ship", "frame_id": "26", "frame_idx": 26, "interpolated": true, "interpolated_from": [20, 30]}], "27": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "27", "frame_idx": 27, "interpolated": true, "interpolated_from": [20, 30]}, {"bbox": [100, 80, 110, 90], "label": "ship", "frame_id": "27", "frame_idx": 27, "interpolated": true, "interpolated_from": [20, 30]}], "28": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "28", "frame_idx": 28, "interpolated": true, "interpolated_from": [20, 30]}, {"bbox": [100, 80, 110, 90], "label": "ship", "frame_id": "28", "frame_idx": 28, "interpolated": true, "interpolated_from": [20, 30]}], "29": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "29", "frame_idx": 29, "interpolated": true, "interpolated_from": [20, 30]}, {"bbox": [100, 80, 110, 90], "label": "ship", "frame_id": "29", "frame_idx": 29, "interpolated": true, "interpolated_from": [20, 30]}], "31": [{"bbox": [97, 79, 102, 84], "label": "drone", "frame_id": "31", "frame_idx": 31, "interpolated": true, "interpolated_from": [30, 40]}, {"bbox": [100, 80, 110, 90], "label": "bus", "frame_id": "31", "frame_idx": 31, "interpolated": true, "interpolated_from": [30, 40]}], "32": [{"bbox": [97, 80, 102, 85], "label": "drone", "frame_id": "32", "frame_idx": 32, "interpolated": true, "interpolated_from": [30, 40]}, {"bbox": [100, 80, 110, 90], "label": "bus", "frame_id": "32", "frame_idx": 32, "interpolated": true, "interpolated_from": [30, 40]}], "33": [{"bbox": [97, 81, 102, 86], "label": "drone", "frame_id": "33", "frame_idx": 33, "interpolated": true, "interpolated_from": [30, 40]}, {"bbox": [100, 80, 110, 90], "label": "bus", "frame_id": "33", "frame_idx": 33, "interpolated": true, "interpolated_from": [30, 40]}], "34": [{"bbox": [97, 82, 102, 87], "label": "drone", "frame_id": "34", "frame_idx": 34, "interpolated": true, "interpolated_from": [30, 40]}, {"bbox": [100, 80, 110, 90], "label": "bus", "frame_id": "34", "frame_idx": 34, "interpolated": true, "interpolated_from": [30, 40]}], "35": [{"bbox": [97, 83, 102, 88], "label": "drone", "frame_id": "35", "frame_idx": 35, "interpolated": true, "interpolated_from": [30, 40]}, {"bbox": [100, 80, 110, 90], "label": "bus", "frame_id": "35", "frame_idx": 35, "interpolated": true, "interpolated_from": [30, 40]}], "36": [{"bbox": [97, 84, 102, 89], "label": "drone", "frame_id": "36", "frame_idx": 36, "interpolated": true, "interpolated_from": [30, 40]}, {"bbox": [100, 80, 110, 90], "label": "bus", "frame_id": "36", "frame_idx": 36, "interpolated": true, "interpolated_from": [30, 40]}], "37": [{"bbox": [97, 85, 102, 90], "label": "drone", "frame_id": "37", "frame_idx": 37, "interpolated": true, "interpolated_from": [30, 40]}, {"bbox": [100, 80, 110, 90], "label": "bus", "frame_id": "37", "frame_idx": 37, "interpolated": true, "interpolated_from": [30, 40]}], "38": [{"bbox": [97, 86, 102, 91], "label": "drone", "frame_id": "38", "frame_idx": 38, "interpolated": true, "interpolated_from": [30, 40]}, {"bbox": [100, 80, 110, 90], "label": "bus", "frame_id": "38", "frame_idx": 38, "interpolated": true, "interpolated_from": [30, 40]}], "39": [{"bbox": [97, 87, 102, 92], "label": "drone", "frame_id": "39", "frame_idx": 39, "interpolated": true, "interpolated_from": [30, 40]}, {"bbox": [100, 80, 110, 90], "label": "bus", "frame_id": "39", "frame_idx": 39, "interpolated": true, "interpolated_from": [30, 40]}], "41": [{"bbox": [97, 88, 102, 93], "label": "drone", "frame_id": "41", "frame_idx": 41, "interpolated": true, "interpolated_from": [40]}, {"bbox": [100, 80, 110, 90], "label": "pedestrian", "frame_id": "41", "frame_idx": 41, "interpolated": true, "interpolated_from": [40]}], "42": [{"bbox": [97, 88, 102, 93], "label": "drone", "frame_id": "42", "frame_idx": 42, "interpolated": true, "interpolated_from": [40]}, {"bbox": [100, 80, 110, 90], "label": "pedestrian", "frame_id": "42", "frame_idx": 42, "interpolated": true, "interpolated_from": [40]}], "43": [{"bbox": [97, 88, 102, 93], "label": "drone", "frame_id": "43", "frame_idx": 43, "interpolated": true, "interpolated_from": [40]}, {"bbox": [100, 80, 110, 90], "label": "pedestrian", "frame_id": "43", "frame_idx": 43, "interpolated": true, "interpolated_from": [40]}], "44": [{"bbox": [97, 88, 102, 93], "label": "drone", "frame_id": "44", "frame_idx": 44, "interpolated": true, "interpolated_from": [40]}, {"bbox": [100, 80, 110, 90], "label": "pedestrian", "frame_id": "44", "frame_idx": 44, "interpolated": true, "interpolated_from": [40]}], "45": [{"bbox": [97, 88, 102, 93], "label": "drone", "frame_id": "45", "frame_idx": 45, "interpolated": true, "interpolated_from": [40]}, {"bbox": [100, 80, 110, 90], "label": "pedestrian", "frame_id": "45", "frame_idx": 45, "interpolated": true, "interpolated_from": [40]}], "46": [{"bbox": [97, 88, 102, 93], "label": "drone", "frame_id": "46", "frame_idx": 46, "interpolated": true, "interpolated_from": [40]}, {"bbox": [100, 80, 110, 90], "label": "pedestrian", "frame_id": "46", "frame_idx": 46, "interpolated": true, "interpolated_from": [40]}], "47": [{"bbox": [97, 88, 102, 93], "label": "drone", "frame_id": "47", "frame_idx": 47, "interpolated": true, "interpolated_from": [40]}, {"bbox": [100, 80, 110, 90], "label": "pedestrian", "frame_id": "47", "frame_idx": 47, "interpolated": true, "interpolated_from": [40]}], "48": [{"bbox": [97, 88, 102, 93], "label": "drone", "frame_id": "48", "frame_idx": 48, "interpolated": true, "interpolated_from": [40]}, {"bbox": [100, 80, 110, 90], "label": "pedestrian", "frame_id": "48", "frame_idx": 48, "interpolated": true, "interpolated_from": [40]}], "49": [{"bbox": [97, 88, 102, 93], "label": "drone", "frame_id": "49", "frame_idx": 49, "interpolated": true, "interpolated_from": [40]}, {"bbox": [100, 80, 110, 90], "label": "pedestrian", "frame_id": "49", "frame_idx": 49, "interpolated": true, "interpolated_from": [40]}]}}, "data06": {"sequence_id": "data06", "total_frames": 50, "key_frames": 5, "total_detections": 55, "direct_detections": 10, "interpolated_detections": 45, "metrics": {"precision": 0.0, "recall": 0.0, "f1": 0, "tp": 0, "fp": 55, "fn": 50}, "detections": {"0": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "0", "frame_idx": 0}, {"bbox": [98, 74, 103, 79], "label": "drone", "frame_id": "0", "frame_idx": 0}, {"bbox": [128, 74, 133, 79], "label": "car", "frame_id": "0", "frame_idx": 0}, {"bbox": [168, 74, 173, 79], "label": "ship", "frame_id": "0", "frame_idx": 0}, {"bbox": [203, 74, 208, 79], "label": "bus", "frame_id": "0", "frame_idx": 0}, {"bbox": [22, 74, 27, 79], "label": "pedestrian", "frame_id": "0", "frame_idx": 0}], "10": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "10", "frame_idx": 10}], "20": [{"bbox": [97, 89, 102, 94], "label": "drone", "frame_id": "20", "frame_idx": 20}], "30": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "30", "frame_idx": 30}], "40": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "40", "frame_idx": 40}], "1": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "1", "frame_idx": 1, "interpolated": true, "interpolated_from": [0, 10]}], "2": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "2", "frame_idx": 2, "interpolated": true, "interpolated_from": [0, 10]}], "3": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "3", "frame_idx": 3, "interpolated": true, "interpolated_from": [0, 10]}], "4": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "4", "frame_idx": 4, "interpolated": true, "interpolated_from": [0, 10]}], "5": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "5", "frame_idx": 5, "interpolated": true, "interpolated_from": [0, 10]}], "6": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "6", "frame_idx": 6, "interpolated": true, "interpolated_from": [0, 10]}], "7": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "7", "frame_idx": 7, "interpolated": true, "interpolated_from": [0, 10]}], "8": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "8", "frame_idx": 8, "interpolated": true, "interpolated_from": [0, 10]}], "9": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "9", "frame_idx": 9, "interpolated": true, "interpolated_from": [0, 10]}], "11": [{"bbox": [97, 79, 102, 84], "label": "drone", "frame_id": "11", "frame_idx": 11, "interpolated": true, "interpolated_from": [10, 20]}], "12": [{"bbox": [97, 80, 102, 85], "label": "drone", "frame_id": "12", "frame_idx": 12, "interpolated": true, "interpolated_from": [10, 20]}], "13": [{"bbox": [97, 81, 102, 86], "label": "drone", "frame_id": "13", "frame_idx": 13, "interpolated": true, "interpolated_from": [10, 20]}], "14": [{"bbox": [97, 82, 102, 87], "label": "drone", "frame_id": "14", "frame_idx": 14, "interpolated": true, "interpolated_from": [10, 20]}], "15": [{"bbox": [97, 83, 102, 88], "label": "drone", "frame_id": "15", "frame_idx": 15, "interpolated": true, "interpolated_from": [10, 20]}], "16": [{"bbox": [97, 84, 102, 89], "label": "drone", "frame_id": "16", "frame_idx": 16, "interpolated": true, "interpolated_from": [10, 20]}], "17": [{"bbox": [97, 85, 102, 90], "label": "drone", "frame_id": "17", "frame_idx": 17, "interpolated": true, "interpolated_from": [10, 20]}], "18": [{"bbox": [97, 86, 102, 91], "label": "drone", "frame_id": "18", "frame_idx": 18, "interpolated": true, "interpolated_from": [10, 20]}], "19": [{"bbox": [97, 87, 102, 92], "label": "drone", "frame_id": "19", "frame_idx": 19, "interpolated": true, "interpolated_from": [10, 20]}], "21": [{"bbox": [97, 88, 102, 93], "label": "drone", "frame_id": "21", "frame_idx": 21, "interpolated": true, "interpolated_from": [20, 30]}], "22": [{"bbox": [97, 87, 102, 92], "label": "drone", "frame_id": "22", "frame_idx": 22, "interpolated": true, "interpolated_from": [20, 30]}], "23": [{"bbox": [97, 86, 102, 91], "label": "drone", "frame_id": "23", "frame_idx": 23, "interpolated": true, "interpolated_from": [20, 30]}], "24": [{"bbox": [97, 85, 102, 90], "label": "drone", "frame_id": "24", "frame_idx": 24, "interpolated": true, "interpolated_from": [20, 30]}], "25": [{"bbox": [97, 84, 102, 89], "label": "drone", "frame_id": "25", "frame_idx": 25, "interpolated": true, "interpolated_from": [20, 30]}], "26": [{"bbox": [97, 83, 102, 88], "label": "drone", "frame_id": "26", "frame_idx": 26, "interpolated": true, "interpolated_from": [20, 30]}], "27": [{"bbox": [97, 82, 102, 87], "label": "drone", "frame_id": "27", "frame_idx": 27, "interpolated": true, "interpolated_from": [20, 30]}], "28": [{"bbox": [97, 81, 102, 86], "label": "drone", "frame_id": "28", "frame_idx": 28, "interpolated": true, "interpolated_from": [20, 30]}], "29": [{"bbox": [97, 80, 102, 85], "label": "drone", "frame_id": "29", "frame_idx": 29, "interpolated": true, "interpolated_from": [20, 30]}], "31": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "31", "frame_idx": 31, "interpolated": true, "interpolated_from": [30, 40]}], "32": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "32", "frame_idx": 32, "interpolated": true, "interpolated_from": [30, 40]}], "33": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "33", "frame_idx": 33, "interpolated": true, "interpolated_from": [30, 40]}], "34": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "34", "frame_idx": 34, "interpolated": true, "interpolated_from": [30, 40]}], "35": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "35", "frame_idx": 35, "interpolated": true, "interpolated_from": [30, 40]}], "36": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "36", "frame_idx": 36, "interpolated": true, "interpolated_from": [30, 40]}], "37": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "37", "frame_idx": 37, "interpolated": true, "interpolated_from": [30, 40]}], "38": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "38", "frame_idx": 38, "interpolated": true, "interpolated_from": [30, 40]}], "39": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "39", "frame_idx": 39, "interpolated": true, "interpolated_from": [30, 40]}], "41": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "41", "frame_idx": 41, "interpolated": true, "interpolated_from": [40]}], "42": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "42", "frame_idx": 42, "interpolated": true, "interpolated_from": [40]}], "43": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "43", "frame_idx": 43, "interpolated": true, "interpolated_from": [40]}], "44": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "44", "frame_idx": 44, "interpolated": true, "interpolated_from": [40]}], "45": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "45", "frame_idx": 45, "interpolated": true, "interpolated_from": [40]}], "46": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "46", "frame_idx": 46, "interpolated": true, "interpolated_from": [40]}], "47": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "47", "frame_idx": 47, "interpolated": true, "interpolated_from": [40]}], "48": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "48", "frame_idx": 48, "interpolated": true, "interpolated_from": [40]}], "49": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "49", "frame_idx": 49, "interpolated": true, "interpolated_from": [40]}]}}, "data07": {"sequence_id": "data07", "total_frames": 50, "key_frames": 5, "total_detections": 52, "direct_detections": 7, "interpolated_detections": 45, "metrics": {"precision": 0.0, "recall": 0.0, "f1": 0, "tp": 0, "fp": 52, "fn": 50}, "detections": {"0": [{"bbox": [98, 138, 103, 143], "label": "drone", "frame_id": "0", "frame_idx": 0}, {"bbox": [98, 140, 108, 150], "label": "drone", "frame_id": "0", "frame_idx": 0}, {"bbox": [126, 140, 136, 150], "label": "car", "frame_id": "0", "frame_idx": 0}], "10": [{"bbox": [98, 124, 103, 129], "label": "drone", "frame_id": "10", "frame_idx": 10}], "20": [{"bbox": [98, 121, 103, 126], "label": "drone", "frame_id": "20", "frame_idx": 20}], "30": [{"bbox": [98, 120, 103, 125], "label": "drone", "frame_id": "30", "frame_idx": 30}], "40": [{"bbox": [98, 120, 103, 125], "label": "drone", "frame_id": "40", "frame_idx": 40}], "1": [{"bbox": [98, 136, 103, 141], "label": "drone", "frame_id": "1", "frame_idx": 1, "interpolated": true, "interpolated_from": [0, 10]}], "2": [{"bbox": [98, 135, 103, 140], "label": "drone", "frame_id": "2", "frame_idx": 2, "interpolated": true, "interpolated_from": [0, 10]}], "3": [{"bbox": [98, 133, 103, 138], "label": "drone", "frame_id": "3", "frame_idx": 3, "interpolated": true, "interpolated_from": [0, 10]}], "4": [{"bbox": [98, 132, 103, 137], "label": "drone", "frame_id": "4", "frame_idx": 4, "interpolated": true, "interpolated_from": [0, 10]}], "5": [{"bbox": [98, 131, 103, 136], "label": "drone", "frame_id": "5", "frame_idx": 5, "interpolated": true, "interpolated_from": [0, 10]}], "6": [{"bbox": [98, 129, 103, 134], "label": "drone", "frame_id": "6", "frame_idx": 6, "interpolated": true, "interpolated_from": [0, 10]}], "7": [{"bbox": [98, 128, 103, 133], "label": "drone", "frame_id": "7", "frame_idx": 7, "interpolated": true, "interpolated_from": [0, 10]}], "8": [{"bbox": [98, 126, 103, 131], "label": "drone", "frame_id": "8", "frame_idx": 8, "interpolated": true, "interpolated_from": [0, 10]}], "9": [{"bbox": [98, 125, 103, 130], "label": "drone", "frame_id": "9", "frame_idx": 9, "interpolated": true, "interpolated_from": [0, 10]}], "11": [{"bbox": [98, 123, 103, 128], "label": "drone", "frame_id": "11", "frame_idx": 11, "interpolated": true, "interpolated_from": [10, 20]}], "12": [{"bbox": [98, 123, 103, 128], "label": "drone", "frame_id": "12", "frame_idx": 12, "interpolated": true, "interpolated_from": [10, 20]}], "13": [{"bbox": [98, 123, 103, 128], "label": "drone", "frame_id": "13", "frame_idx": 13, "interpolated": true, "interpolated_from": [10, 20]}], "14": [{"bbox": [98, 122, 103, 127], "label": "drone", "frame_id": "14", "frame_idx": 14, "interpolated": true, "interpolated_from": [10, 20]}], "15": [{"bbox": [98, 122, 103, 127], "label": "drone", "frame_id": "15", "frame_idx": 15, "interpolated": true, "interpolated_from": [10, 20]}], "16": [{"bbox": [98, 122, 103, 127], "label": "drone", "frame_id": "16", "frame_idx": 16, "interpolated": true, "interpolated_from": [10, 20]}], "17": [{"bbox": [98, 121, 103, 126], "label": "drone", "frame_id": "17", "frame_idx": 17, "interpolated": true, "interpolated_from": [10, 20]}], "18": [{"bbox": [98, 121, 103, 126], "label": "drone", "frame_id": "18", "frame_idx": 18, "interpolated": true, "interpolated_from": [10, 20]}], "19": [{"bbox": [98, 121, 103, 126], "label": "drone", "frame_id": "19", "frame_idx": 19, "interpolated": true, "interpolated_from": [10, 20]}], "21": [{"bbox": [98, 120, 103, 125], "label": "drone", "frame_id": "21", "frame_idx": 21, "interpolated": true, "interpolated_from": [20, 30]}], "22": [{"bbox": [98, 120, 103, 125], "label": "drone", "frame_id": "22", "frame_idx": 22, "interpolated": true, "interpolated_from": [20, 30]}], "23": [{"bbox": [98, 120, 103, 125], "label": "drone", "frame_id": "23", "frame_idx": 23, "interpolated": true, "interpolated_from": [20, 30]}], "24": [{"bbox": [98, 120, 103, 125], "label": "drone", "frame_id": "24", "frame_idx": 24, "interpolated": true, "interpolated_from": [20, 30]}], "25": [{"bbox": [98, 120, 103, 125], "label": "drone", "frame_id": "25", "frame_idx": 25, "interpolated": true, "interpolated_from": [20, 30]}], "26": [{"bbox": [98, 120, 103, 125], "label": "drone", "frame_id": "26", "frame_idx": 26, "interpolated": true, "interpolated_from": [20, 30]}], "27": [{"bbox": [98, 120, 103, 125], "label": "drone", "frame_id": "27", "frame_idx": 27, "interpolated": true, "interpolated_from": [20, 30]}], "28": [{"bbox": [98, 120, 103, 125], "label": "drone", "frame_id": "28", "frame_idx": 28, "interpolated": true, "interpolated_from": [20, 30]}], "29": [{"bbox": [98, 120, 103, 125], "label": "drone", "frame_id": "29", "frame_idx": 29, "interpolated": true, "interpolated_from": [20, 30]}], "31": [{"bbox": [98, 120, 103, 125], "label": "drone", "frame_id": "31", "frame_idx": 31, "interpolated": true, "interpolated_from": [30, 40]}], "32": [{"bbox": [98, 120, 103, 125], "label": "drone", "frame_id": "32", "frame_idx": 32, "interpolated": true, "interpolated_from": [30, 40]}], "33": [{"bbox": [98, 120, 103, 125], "label": "drone", "frame_id": "33", "frame_idx": 33, "interpolated": true, "interpolated_from": [30, 40]}], "34": [{"bbox": [98, 120, 103, 125], "label": "drone", "frame_id": "34", "frame_idx": 34, "interpolated": true, "interpolated_from": [30, 40]}], "35": [{"bbox": [98, 120, 103, 125], "label": "drone", "frame_id": "35", "frame_idx": 35, "interpolated": true, "interpolated_from": [30, 40]}], "36": [{"bbox": [98, 120, 103, 125], "label": "drone", "frame_id": "36", "frame_idx": 36, "interpolated": true, "interpolated_from": [30, 40]}], "37": [{"bbox": [98, 120, 103, 125], "label": "drone", "frame_id": "37", "frame_idx": 37, "interpolated": true, "interpolated_from": [30, 40]}], "38": [{"bbox": [98, 120, 103, 125], "label": "drone", "frame_id": "38", "frame_idx": 38, "interpolated": true, "interpolated_from": [30, 40]}], "39": [{"bbox": [98, 120, 103, 125], "label": "drone", "frame_id": "39", "frame_idx": 39, "interpolated": true, "interpolated_from": [30, 40]}], "41": [{"bbox": [98, 120, 103, 125], "label": "drone", "frame_id": "41", "frame_idx": 41, "interpolated": true, "interpolated_from": [40]}], "42": [{"bbox": [98, 120, 103, 125], "label": "drone", "frame_id": "42", "frame_idx": 42, "interpolated": true, "interpolated_from": [40]}], "43": [{"bbox": [98, 120, 103, 125], "label": "drone", "frame_id": "43", "frame_idx": 43, "interpolated": true, "interpolated_from": [40]}], "44": [{"bbox": [98, 120, 103, 125], "label": "drone", "frame_id": "44", "frame_idx": 44, "interpolated": true, "interpolated_from": [40]}], "45": [{"bbox": [98, 120, 103, 125], "label": "drone", "frame_id": "45", "frame_idx": 45, "interpolated": true, "interpolated_from": [40]}], "46": [{"bbox": [98, 120, 103, 125], "label": "drone", "frame_id": "46", "frame_idx": 46, "interpolated": true, "interpolated_from": [40]}], "47": [{"bbox": [98, 120, 103, 125], "label": "drone", "frame_id": "47", "frame_idx": 47, "interpolated": true, "interpolated_from": [40]}], "48": [{"bbox": [98, 120, 103, 125], "label": "drone", "frame_id": "48", "frame_idx": 48, "interpolated": true, "interpolated_from": [40]}], "49": [{"bbox": [98, 120, 103, 125], "label": "drone", "frame_id": "49", "frame_idx": 49, "interpolated": true, "interpolated_from": [40]}]}}, "data19": {"sequence_id": "data19", "total_frames": 75, "key_frames": 8, "total_detections": 77, "direct_detections": 10, "interpolated_detections": 67, "metrics": {"precision": 0.0, "recall": 0, "f1": 0, "tp": 0, "fp": 77, "fn": 0}, "detections": {"0": [{"bbox": [207, 186, 252, 236], "label": "drone", "frame_id": "0", "frame_idx": 0}, {"bbox": [7, 10, 162, 18], "label": "drone", "frame_id": "0", "frame_idx": 0}, {"bbox": [204, 10, 296, 100], "label": "car", "frame_id": "0", "frame_idx": 0}], "10": [{"bbox": [287, 162, 307, 182], "label": "drone", "frame_id": "10", "frame_idx": 10}], "20": [{"bbox": [227, 180, 257, 210], "label": "drone", "frame_id": "20", "frame_idx": 20}], "30": [{"bbox": [227, 158, 260, 189], "label": "drone", "frame_id": "30", "frame_idx": 30}], "40": [{"bbox": [287, 163, 307, 184], "label": "drone", "frame_id": "40", "frame_idx": 40}], "50": [{"bbox": [278, 133, 308, 163], "label": "drone", "frame_id": "50", "frame_idx": 50}], "60": [{"bbox": [227, 189, 247, 209], "label": "drone", "frame_id": "60", "frame_idx": 60}], "70": [{"bbox": [228, 190, 248, 210], "label": "drone", "frame_id": "70", "frame_idx": 70}], "1": [{"bbox": [215, 183, 257, 230], "label": "drone", "frame_id": "1", "frame_idx": 1, "interpolated": true, "interpolated_from": [0, 10]}], "2": [{"bbox": [223, 181, 263, 225], "label": "drone", "frame_id": "2", "frame_idx": 2, "interpolated": true, "interpolated_from": [0, 10]}], "3": [{"bbox": [231, 178, 268, 219], "label": "drone", "frame_id": "3", "frame_idx": 3, "interpolated": true, "interpolated_from": [0, 10]}], "4": [{"bbox": [239, 176, 274, 214], "label": "drone", "frame_id": "4", "frame_idx": 4, "interpolated": true, "interpolated_from": [0, 10]}], "5": [{"bbox": [247, 174, 279, 209], "label": "drone", "frame_id": "5", "frame_idx": 5, "interpolated": true, "interpolated_from": [0, 10]}], "6": [{"bbox": [255, 171, 285, 203], "label": "drone", "frame_id": "6", "frame_idx": 6, "interpolated": true, "interpolated_from": [0, 10]}], "7": [{"bbox": [263, 169, 290, 198], "label": "drone", "frame_id": "7", "frame_idx": 7, "interpolated": true, "interpolated_from": [0, 10]}], "8": [{"bbox": [271, 166, 296, 192], "label": "drone", "frame_id": "8", "frame_idx": 8, "interpolated": true, "interpolated_from": [0, 10]}], "9": [{"bbox": [279, 164, 301, 187], "label": "drone", "frame_id": "9", "frame_idx": 9, "interpolated": true, "interpolated_from": [0, 10]}], "11": [{"bbox": [281, 163, 302, 184], "label": "drone", "frame_id": "11", "frame_idx": 11, "interpolated": true, "interpolated_from": [10, 20]}], "12": [{"bbox": [275, 165, 297, 187], "label": "drone", "frame_id": "12", "frame_idx": 12, "interpolated": true, "interpolated_from": [10, 20]}], "13": [{"bbox": [269, 167, 292, 190], "label": "drone", "frame_id": "13", "frame_idx": 13, "interpolated": true, "interpolated_from": [10, 20]}], "14": [{"bbox": [263, 169, 287, 193], "label": "drone", "frame_id": "14", "frame_idx": 14, "interpolated": true, "interpolated_from": [10, 20]}], "15": [{"bbox": [257, 171, 282, 196], "label": "drone", "frame_id": "15", "frame_idx": 15, "interpolated": true, "interpolated_from": [10, 20]}], "16": [{"bbox": [251, 172, 277, 198], "label": "drone", "frame_id": "16", "frame_idx": 16, "interpolated": true, "interpolated_from": [10, 20]}], "17": [{"bbox": [245, 174, 272, 201], "label": "drone", "frame_id": "17", "frame_idx": 17, "interpolated": true, "interpolated_from": [10, 20]}], "18": [{"bbox": [239, 176, 267, 204], "label": "drone", "frame_id": "18", "frame_idx": 18, "interpolated": true, "interpolated_from": [10, 20]}], "19": [{"bbox": [233, 178, 262, 207], "label": "drone", "frame_id": "19", "frame_idx": 19, "interpolated": true, "interpolated_from": [10, 20]}], "21": [{"bbox": [227, 177, 257, 207], "label": "drone", "frame_id": "21", "frame_idx": 21, "interpolated": true, "interpolated_from": [20, 30]}], "22": [{"bbox": [227, 175, 257, 205], "label": "drone", "frame_id": "22", "frame_idx": 22, "interpolated": true, "interpolated_from": [20, 30]}], "23": [{"bbox": [227, 173, 257, 203], "label": "drone", "frame_id": "23", "frame_idx": 23, "interpolated": true, "interpolated_from": [20, 30]}], "24": [{"bbox": [227, 171, 258, 201], "label": "drone", "frame_id": "24", "frame_idx": 24, "interpolated": true, "interpolated_from": [20, 30]}], "25": [{"bbox": [227, 169, 258, 199], "label": "drone", "frame_id": "25", "frame_idx": 25, "interpolated": true, "interpolated_from": [20, 30]}], "26": [{"bbox": [227, 166, 258, 197], "label": "drone", "frame_id": "26", "frame_idx": 26, "interpolated": true, "interpolated_from": [20, 30]}], "27": [{"bbox": [227, 164, 259, 195], "label": "drone", "frame_id": "27", "frame_idx": 27, "interpolated": true, "interpolated_from": [20, 30]}], "28": [{"bbox": [227, 162, 259, 193], "label": "drone", "frame_id": "28", "frame_idx": 28, "interpolated": true, "interpolated_from": [20, 30]}], "29": [{"bbox": [227, 160, 259, 191], "label": "drone", "frame_id": "29", "frame_idx": 29, "interpolated": true, "interpolated_from": [20, 30]}], "31": [{"bbox": [233, 158, 264, 188], "label": "drone", "frame_id": "31", "frame_idx": 31, "interpolated": true, "interpolated_from": [30, 40]}], "32": [{"bbox": [239, 159, 269, 188], "label": "drone", "frame_id": "32", "frame_idx": 32, "interpolated": true, "interpolated_from": [30, 40]}], "33": [{"bbox": [245, 159, 274, 187], "label": "drone", "frame_id": "33", "frame_idx": 33, "interpolated": true, "interpolated_from": [30, 40]}], "34": [{"bbox": [251, 160, 278, 187], "label": "drone", "frame_id": "34", "frame_idx": 34, "interpolated": true, "interpolated_from": [30, 40]}], "35": [{"bbox": [257, 160, 283, 186], "label": "drone", "frame_id": "35", "frame_idx": 35, "interpolated": true, "interpolated_from": [30, 40]}], "36": [{"bbox": [263, 161, 288, 186], "label": "drone", "frame_id": "36", "frame_idx": 36, "interpolated": true, "interpolated_from": [30, 40]}], "37": [{"bbox": [269, 161, 292, 185], "label": "drone", "frame_id": "37", "frame_idx": 37, "interpolated": true, "interpolated_from": [30, 40]}], "38": [{"bbox": [275, 162, 297, 185], "label": "drone", "frame_id": "38", "frame_idx": 38, "interpolated": true, "interpolated_from": [30, 40]}], "39": [{"bbox": [281, 162, 302, 184], "label": "drone", "frame_id": "39", "frame_idx": 39, "interpolated": true, "interpolated_from": [30, 40]}], "41": [{"bbox": [286, 160, 307, 181], "label": "drone", "frame_id": "41", "frame_idx": 41, "interpolated": true, "interpolated_from": [40, 50]}], "42": [{"bbox": [285, 157, 307, 179], "label": "drone", "frame_id": "42", "frame_idx": 42, "interpolated": true, "interpolated_from": [40, 50]}], "43": [{"bbox": [284, 154, 307, 177], "label": "drone", "frame_id": "43", "frame_idx": 43, "interpolated": true, "interpolated_from": [40, 50]}], "44": [{"bbox": [283, 151, 307, 175], "label": "drone", "frame_id": "44", "frame_idx": 44, "interpolated": true, "interpolated_from": [40, 50]}], "45": [{"bbox": [282, 148, 307, 173], "label": "drone", "frame_id": "45", "frame_idx": 45, "interpolated": true, "interpolated_from": [40, 50]}], "46": [{"bbox": [281, 145, 307, 171], "label": "drone", "frame_id": "46", "frame_idx": 46, "interpolated": true, "interpolated_from": [40, 50]}], "47": [{"bbox": [280, 142, 307, 169], "label": "drone", "frame_id": "47", "frame_idx": 47, "interpolated": true, "interpolated_from": [40, 50]}], "48": [{"bbox": [279, 139, 307, 167], "label": "drone", "frame_id": "48", "frame_idx": 48, "interpolated": true, "interpolated_from": [40, 50]}], "49": [{"bbox": [278, 136, 307, 165], "label": "drone", "frame_id": "49", "frame_idx": 49, "interpolated": true, "interpolated_from": [40, 50]}], "51": [{"bbox": [272, 138, 301, 167], "label": "drone", "frame_id": "51", "frame_idx": 51, "interpolated": true, "interpolated_from": [50, 60]}], "52": [{"bbox": [267, 144, 295, 172], "label": "drone", "frame_id": "52", "frame_idx": 52, "interpolated": true, "interpolated_from": [50, 60]}], "53": [{"bbox": [262, 149, 289, 176], "label": "drone", "frame_id": "53", "frame_idx": 53, "interpolated": true, "interpolated_from": [50, 60]}], "54": [{"bbox": [257, 155, 283, 181], "label": "drone", "frame_id": "54", "frame_idx": 54, "interpolated": true, "interpolated_from": [50, 60]}], "55": [{"bbox": [252, 161, 277, 186], "label": "drone", "frame_id": "55", "frame_idx": 55, "interpolated": true, "interpolated_from": [50, 60]}], "56": [{"bbox": [247, 166, 271, 190], "label": "drone", "frame_id": "56", "frame_idx": 56, "interpolated": true, "interpolated_from": [50, 60]}], "57": [{"bbox": [242, 172, 265, 195], "label": "drone", "frame_id": "57", "frame_idx": 57, "interpolated": true, "interpolated_from": [50, 60]}], "58": [{"bbox": [237, 177, 259, 199], "label": "drone", "frame_id": "58", "frame_idx": 58, "interpolated": true, "interpolated_from": [50, 60]}], "59": [{"bbox": [232, 183, 253, 204], "label": "drone", "frame_id": "59", "frame_idx": 59, "interpolated": true, "interpolated_from": [50, 60]}], "61": [{"bbox": [227, 189, 247, 209], "label": "drone", "frame_id": "61", "frame_idx": 61, "interpolated": true, "interpolated_from": [60, 70]}], "62": [{"bbox": [227, 189, 247, 209], "label": "drone", "frame_id": "62", "frame_idx": 62, "interpolated": true, "interpolated_from": [60, 70]}], "63": [{"bbox": [227, 189, 247, 209], "label": "drone", "frame_id": "63", "frame_idx": 63, "interpolated": true, "interpolated_from": [60, 70]}], "64": [{"bbox": [227, 189, 247, 209], "label": "drone", "frame_id": "64", "frame_idx": 64, "interpolated": true, "interpolated_from": [60, 70]}], "65": [{"bbox": [227, 189, 247, 209], "label": "drone", "frame_id": "65", "frame_idx": 65, "interpolated": true, "interpolated_from": [60, 70]}], "66": [{"bbox": [227, 189, 247, 209], "label": "drone", "frame_id": "66", "frame_idx": 66, "interpolated": true, "interpolated_from": [60, 70]}], "67": [{"bbox": [227, 189, 247, 209], "label": "drone", "frame_id": "67", "frame_idx": 67, "interpolated": true, "interpolated_from": [60, 70]}], "68": [{"bbox": [227, 189, 247, 209], "label": "drone", "frame_id": "68", "frame_idx": 68, "interpolated": true, "interpolated_from": [60, 70]}], "69": [{"bbox": [227, 189, 247, 209], "label": "drone", "frame_id": "69", "frame_idx": 69, "interpolated": true, "interpolated_from": [60, 70]}], "71": [{"bbox": [228, 190, 248, 210], "label": "drone", "frame_id": "71", "frame_idx": 71, "interpolated": true, "interpolated_from": [70]}], "72": [{"bbox": [228, 190, 248, 210], "label": "drone", "frame_id": "72", "frame_idx": 72, "interpolated": true, "interpolated_from": [70]}], "73": [{"bbox": [228, 190, 248, 210], "label": "drone", "frame_id": "73", "frame_idx": 73, "interpolated": true, "interpolated_from": [70]}], "74": [{"bbox": [228, 190, 248, 210], "label": "drone", "frame_id": "74", "frame_idx": 74, "interpolated": true, "interpolated_from": [70]}]}}, "data23": {"sequence_id": "data23", "total_frames": 50, "key_frames": 5, "total_detections": 50, "direct_detections": 5, "interpolated_detections": 45, "metrics": {"precision": 0.0, "recall": 0, "f1": 0, "tp": 0, "fp": 50, "fn": 0}, "detections": {"0": [{"bbox": [97, 189, 102, 194], "label": "drone", "frame_id": "0", "frame_idx": 0}], "10": [{"bbox": [97, 80, 102, 85], "label": "drone", "frame_id": "10", "frame_idx": 10}], "20": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_idx": 20, "frame_id": "20"}], "30": [{"bbox": [198, 79, 203, 84], "label": "drone", "frame_idx": 30, "frame_id": "30"}], "40": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "40", "frame_idx": 40}], "1": [{"bbox": [97, 178, 102, 183], "label": "drone", "frame_id": "1", "frame_idx": 1, "interpolated": true, "interpolated_from": [0, 10]}], "2": [{"bbox": [97, 167, 102, 172], "label": "drone", "frame_id": "2", "frame_idx": 2, "interpolated": true, "interpolated_from": [0, 10]}], "3": [{"bbox": [97, 156, 102, 161], "label": "drone", "frame_id": "3", "frame_idx": 3, "interpolated": true, "interpolated_from": [0, 10]}], "4": [{"bbox": [97, 145, 102, 150], "label": "drone", "frame_id": "4", "frame_idx": 4, "interpolated": true, "interpolated_from": [0, 10]}], "5": [{"bbox": [97, 134, 102, 139], "label": "drone", "frame_id": "5", "frame_idx": 5, "interpolated": true, "interpolated_from": [0, 10]}], "6": [{"bbox": [97, 123, 102, 128], "label": "drone", "frame_id": "6", "frame_idx": 6, "interpolated": true, "interpolated_from": [0, 10]}], "7": [{"bbox": [97, 112, 102, 117], "label": "drone", "frame_id": "7", "frame_idx": 7, "interpolated": true, "interpolated_from": [0, 10]}], "8": [{"bbox": [97, 101, 102, 106], "label": "drone", "frame_id": "8", "frame_idx": 8, "interpolated": true, "interpolated_from": [0, 10]}], "9": [{"bbox": [97, 90, 102, 95], "label": "drone", "frame_id": "9", "frame_idx": 9, "interpolated": true, "interpolated_from": [0, 10]}], "11": [{"bbox": [97, 79, 102, 84], "label": "drone", "frame_id": "11", "frame_idx": 11, "interpolated": true, "interpolated_from": [10, 20]}], "12": [{"bbox": [97, 79, 102, 84], "label": "drone", "frame_id": "12", "frame_idx": 12, "interpolated": true, "interpolated_from": [10, 20]}], "13": [{"bbox": [97, 79, 102, 84], "label": "drone", "frame_id": "13", "frame_idx": 13, "interpolated": true, "interpolated_from": [10, 20]}], "14": [{"bbox": [97, 79, 102, 84], "label": "drone", "frame_id": "14", "frame_idx": 14, "interpolated": true, "interpolated_from": [10, 20]}], "15": [{"bbox": [97, 79, 102, 84], "label": "drone", "frame_id": "15", "frame_idx": 15, "interpolated": true, "interpolated_from": [10, 20]}], "16": [{"bbox": [97, 78, 102, 83], "label": "drone", "frame_id": "16", "frame_idx": 16, "interpolated": true, "interpolated_from": [10, 20]}], "17": [{"bbox": [97, 78, 102, 83], "label": "drone", "frame_id": "17", "frame_idx": 17, "interpolated": true, "interpolated_from": [10, 20]}], "18": [{"bbox": [97, 78, 102, 83], "label": "drone", "frame_id": "18", "frame_idx": 18, "interpolated": true, "interpolated_from": [10, 20]}], "19": [{"bbox": [97, 78, 102, 83], "label": "drone", "frame_id": "19", "frame_idx": 19, "interpolated": true, "interpolated_from": [10, 20]}], "21": [{"bbox": [108, 78, 113, 83], "label": "drone", "frame_id": "21", "frame_idx": 21, "interpolated": true, "interpolated_from": [20, 30]}], "22": [{"bbox": [118, 78, 123, 83], "label": "drone", "frame_id": "22", "frame_idx": 22, "interpolated": true, "interpolated_from": [20, 30]}], "23": [{"bbox": [128, 78, 133, 83], "label": "drone", "frame_id": "23", "frame_idx": 23, "interpolated": true, "interpolated_from": [20, 30]}], "24": [{"bbox": [138, 78, 143, 83], "label": "drone", "frame_id": "24", "frame_idx": 24, "interpolated": true, "interpolated_from": [20, 30]}], "25": [{"bbox": [148, 78, 153, 83], "label": "drone", "frame_id": "25", "frame_idx": 25, "interpolated": true, "interpolated_from": [20, 30]}], "26": [{"bbox": [158, 78, 163, 83], "label": "drone", "frame_id": "26", "frame_idx": 26, "interpolated": true, "interpolated_from": [20, 30]}], "27": [{"bbox": [168, 78, 173, 83], "label": "drone", "frame_id": "27", "frame_idx": 27, "interpolated": true, "interpolated_from": [20, 30]}], "28": [{"bbox": [178, 78, 183, 83], "label": "drone", "frame_id": "28", "frame_idx": 28, "interpolated": true, "interpolated_from": [20, 30]}], "29": [{"bbox": [188, 78, 193, 83], "label": "drone", "frame_id": "29", "frame_idx": 29, "interpolated": true, "interpolated_from": [20, 30]}], "31": [{"bbox": [188, 79, 193, 84], "label": "drone", "frame_id": "31", "frame_idx": 31, "interpolated": true, "interpolated_from": [30, 40]}], "32": [{"bbox": [178, 79, 183, 84], "label": "drone", "frame_id": "32", "frame_idx": 32, "interpolated": true, "interpolated_from": [30, 40]}], "33": [{"bbox": [168, 79, 173, 84], "label": "drone", "frame_id": "33", "frame_idx": 33, "interpolated": true, "interpolated_from": [30, 40]}], "34": [{"bbox": [158, 79, 163, 84], "label": "drone", "frame_id": "34", "frame_idx": 34, "interpolated": true, "interpolated_from": [30, 40]}], "35": [{"bbox": [148, 79, 153, 84], "label": "drone", "frame_id": "35", "frame_idx": 35, "interpolated": true, "interpolated_from": [30, 40]}], "36": [{"bbox": [138, 79, 143, 84], "label": "drone", "frame_id": "36", "frame_idx": 36, "interpolated": true, "interpolated_from": [30, 40]}], "37": [{"bbox": [128, 79, 133, 84], "label": "drone", "frame_id": "37", "frame_idx": 37, "interpolated": true, "interpolated_from": [30, 40]}], "38": [{"bbox": [118, 79, 123, 84], "label": "drone", "frame_id": "38", "frame_idx": 38, "interpolated": true, "interpolated_from": [30, 40]}], "39": [{"bbox": [108, 79, 113, 84], "label": "drone", "frame_id": "39", "frame_idx": 39, "interpolated": true, "interpolated_from": [30, 40]}], "41": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "41", "frame_idx": 41, "interpolated": true, "interpolated_from": [40]}], "42": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "42", "frame_idx": 42, "interpolated": true, "interpolated_from": [40]}], "43": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "43", "frame_idx": 43, "interpolated": true, "interpolated_from": [40]}], "44": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "44", "frame_idx": 44, "interpolated": true, "interpolated_from": [40]}], "45": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "45", "frame_idx": 45, "interpolated": true, "interpolated_from": [40]}], "46": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "46", "frame_idx": 46, "interpolated": true, "interpolated_from": [40]}], "47": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "47", "frame_idx": 47, "interpolated": true, "interpolated_from": [40]}], "48": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "48", "frame_idx": 48, "interpolated": true, "interpolated_from": [40]}], "49": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "49", "frame_idx": 49, "interpolated": true, "interpolated_from": [40]}]}}, "data26": {"sequence_id": "data26", "total_frames": 75, "key_frames": 8, "total_detections": 76, "direct_detections": 22, "interpolated_detections": 54, "metrics": {"precision": 0.0, "recall": 0, "f1": 0, "tp": 0, "fp": 76, "fn": 0}, "detections": {"0": [{"bbox": [98, 76, 103, 81], "label": "drone", "frame_id": "0", "frame_idx": 0}, {"bbox": [97, 200, 102, 200], "label": "drone", "frame_id": "0", "frame_idx": 0}, {"bbox": [102, 200, 107, 200], "label": "car", "frame_id": "0", "frame_idx": 0}, {"bbox": [107, 200, 112, 200], "label": "ship", "frame_id": "0", "frame_idx": 0}, {"bbox": [112, 200, 117, 200], "label": "bus", "frame_id": "0", "frame_idx": 0}, {"bbox": [117, 200, 122, 200], "label": "pedestrian", "frame_id": "0", "frame_idx": 0}, {"bbox": [122, 200, 127, 200], "label": "cyclist", "frame_id": "0", "frame_idx": 0}, {"bbox": [127, 200, 132, 200], "label": "drone", "frame_id": "0", "frame_idx": 0}, {"bbox": [132, 200, 137, 200], "label": "car", "frame_id": "0", "frame_idx": 0}, {"bbox": [137, 200, 142, 200], "label": "ship", "frame_id": "0", "frame_idx": 0}, {"bbox": [142, 200, 147, 200], "label": "bus", "frame_id": "0", "frame_idx": 0}, {"bbox": [147, 200, 152, 200], "label": "pedestrian", "frame_id": "0", "frame_idx": 0}, {"bbox": [152, 200, 157, 200], "label": "cyclist", "frame_id": "0", "frame_idx": 0}, {"bbox": [157, 200, 162, 200], "label": "drone", "frame_id": "0", "frame_idx": 0}, {"bbox": [162, 200, 167, 200], "label": "car", "frame_id": "0", "frame_idx": 0}, {"bbox": [167, 200, 172, 200], "label": "drone", "frame_id": "0", "frame_idx": 0}], "10": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "10", "frame_idx": 10}], "20": [{"bbox": [97, 80, 102, 85], "label": "drone", "frame_id": "20", "frame_idx": 20}], "30": [{"bbox": [98, 77, 103, 82], "label": "drone", "frame_id": "30", "frame_idx": 30}], "40": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "40", "frame_idx": 40}], "50": [{"bbox": [98, 77, 103, 82], "label": "drone", "frame_id": "50", "frame_idx": 50}], "60": [{"bbox": [378, 59, 391, 72], "label": "drone", "frame_id": "60", "frame_idx": 60}], "70": [], "1": [{"bbox": [98, 76, 103, 81], "label": "drone", "frame_id": "1", "frame_idx": 1, "interpolated": true, "interpolated_from": [0, 10]}], "2": [{"bbox": [98, 76, 103, 81], "label": "drone", "frame_id": "2", "frame_idx": 2, "interpolated": true, "interpolated_from": [0, 10]}], "3": [{"bbox": [98, 76, 103, 81], "label": "drone", "frame_id": "3", "frame_idx": 3, "interpolated": true, "interpolated_from": [0, 10]}], "4": [{"bbox": [98, 77, 103, 82], "label": "drone", "frame_id": "4", "frame_idx": 4, "interpolated": true, "interpolated_from": [0, 10]}], "5": [{"bbox": [98, 77, 103, 82], "label": "drone", "frame_id": "5", "frame_idx": 5, "interpolated": true, "interpolated_from": [0, 10]}], "6": [{"bbox": [98, 77, 103, 82], "label": "drone", "frame_id": "6", "frame_idx": 6, "interpolated": true, "interpolated_from": [0, 10]}], "7": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "7", "frame_idx": 7, "interpolated": true, "interpolated_from": [0, 10]}], "8": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "8", "frame_idx": 8, "interpolated": true, "interpolated_from": [0, 10]}], "9": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "9", "frame_idx": 9, "interpolated": true, "interpolated_from": [0, 10]}], "11": [{"bbox": [97, 79, 102, 84], "label": "drone", "frame_id": "11", "frame_idx": 11, "interpolated": true, "interpolated_from": [10, 20]}], "12": [{"bbox": [97, 79, 102, 84], "label": "drone", "frame_id": "12", "frame_idx": 12, "interpolated": true, "interpolated_from": [10, 20]}], "13": [{"bbox": [97, 79, 102, 84], "label": "drone", "frame_id": "13", "frame_idx": 13, "interpolated": true, "interpolated_from": [10, 20]}], "14": [{"bbox": [97, 79, 102, 84], "label": "drone", "frame_id": "14", "frame_idx": 14, "interpolated": true, "interpolated_from": [10, 20]}], "15": [{"bbox": [97, 79, 102, 84], "label": "drone", "frame_id": "15", "frame_idx": 15, "interpolated": true, "interpolated_from": [10, 20]}], "16": [{"bbox": [97, 79, 102, 84], "label": "drone", "frame_id": "16", "frame_idx": 16, "interpolated": true, "interpolated_from": [10, 20]}], "17": [{"bbox": [97, 79, 102, 84], "label": "drone", "frame_id": "17", "frame_idx": 17, "interpolated": true, "interpolated_from": [10, 20]}], "18": [{"bbox": [97, 79, 102, 84], "label": "drone", "frame_id": "18", "frame_idx": 18, "interpolated": true, "interpolated_from": [10, 20]}], "19": [{"bbox": [97, 79, 102, 84], "label": "drone", "frame_id": "19", "frame_idx": 19, "interpolated": true, "interpolated_from": [10, 20]}], "21": [{"bbox": [97, 79, 102, 84], "label": "drone", "frame_id": "21", "frame_idx": 21, "interpolated": true, "interpolated_from": [20, 30]}], "22": [{"bbox": [97, 79, 102, 84], "label": "drone", "frame_id": "22", "frame_idx": 22, "interpolated": true, "interpolated_from": [20, 30]}], "23": [{"bbox": [97, 79, 102, 84], "label": "drone", "frame_id": "23", "frame_idx": 23, "interpolated": true, "interpolated_from": [20, 30]}], "24": [{"bbox": [97, 78, 102, 83], "label": "drone", "frame_id": "24", "frame_idx": 24, "interpolated": true, "interpolated_from": [20, 30]}], "25": [{"bbox": [97, 78, 102, 83], "label": "drone", "frame_id": "25", "frame_idx": 25, "interpolated": true, "interpolated_from": [20, 30]}], "26": [{"bbox": [97, 78, 102, 83], "label": "drone", "frame_id": "26", "frame_idx": 26, "interpolated": true, "interpolated_from": [20, 30]}], "27": [{"bbox": [97, 77, 102, 82], "label": "drone", "frame_id": "27", "frame_idx": 27, "interpolated": true, "interpolated_from": [20, 30]}], "28": [{"bbox": [97, 77, 102, 82], "label": "drone", "frame_id": "28", "frame_idx": 28, "interpolated": true, "interpolated_from": [20, 30]}], "29": [{"bbox": [97, 77, 102, 82], "label": "drone", "frame_id": "29", "frame_idx": 29, "interpolated": true, "interpolated_from": [20, 30]}], "31": [{"bbox": [98, 77, 103, 82], "label": "drone", "frame_id": "31", "frame_idx": 31, "interpolated": true, "interpolated_from": [30, 40]}], "32": [{"bbox": [98, 77, 103, 82], "label": "drone", "frame_id": "32", "frame_idx": 32, "interpolated": true, "interpolated_from": [30, 40]}], "33": [{"bbox": [98, 77, 103, 82], "label": "drone", "frame_id": "33", "frame_idx": 33, "interpolated": true, "interpolated_from": [30, 40]}], "34": [{"bbox": [98, 77, 103, 82], "label": "drone", "frame_id": "34", "frame_idx": 34, "interpolated": true, "interpolated_from": [30, 40]}], "35": [{"bbox": [98, 77, 103, 82], "label": "drone", "frame_id": "35", "frame_idx": 35, "interpolated": true, "interpolated_from": [30, 40]}], "36": [{"bbox": [98, 77, 103, 82], "label": "drone", "frame_id": "36", "frame_idx": 36, "interpolated": true, "interpolated_from": [30, 40]}], "37": [{"bbox": [98, 77, 103, 82], "label": "drone", "frame_id": "37", "frame_idx": 37, "interpolated": true, "interpolated_from": [30, 40]}], "38": [{"bbox": [98, 77, 103, 82], "label": "drone", "frame_id": "38", "frame_idx": 38, "interpolated": true, "interpolated_from": [30, 40]}], "39": [{"bbox": [98, 77, 103, 82], "label": "drone", "frame_id": "39", "frame_idx": 39, "interpolated": true, "interpolated_from": [30, 40]}], "41": [{"bbox": [98, 77, 103, 82], "label": "drone", "frame_id": "41", "frame_idx": 41, "interpolated": true, "interpolated_from": [40, 50]}], "42": [{"bbox": [98, 77, 103, 82], "label": "drone", "frame_id": "42", "frame_idx": 42, "interpolated": true, "interpolated_from": [40, 50]}], "43": [{"bbox": [98, 77, 103, 82], "label": "drone", "frame_id": "43", "frame_idx": 43, "interpolated": true, "interpolated_from": [40, 50]}], "44": [{"bbox": [98, 77, 103, 82], "label": "drone", "frame_id": "44", "frame_idx": 44, "interpolated": true, "interpolated_from": [40, 50]}], "45": [{"bbox": [98, 77, 103, 82], "label": "drone", "frame_id": "45", "frame_idx": 45, "interpolated": true, "interpolated_from": [40, 50]}], "46": [{"bbox": [98, 77, 103, 82], "label": "drone", "frame_id": "46", "frame_idx": 46, "interpolated": true, "interpolated_from": [40, 50]}], "47": [{"bbox": [98, 77, 103, 82], "label": "drone", "frame_id": "47", "frame_idx": 47, "interpolated": true, "interpolated_from": [40, 50]}], "48": [{"bbox": [98, 77, 103, 82], "label": "drone", "frame_id": "48", "frame_idx": 48, "interpolated": true, "interpolated_from": [40, 50]}], "49": [{"bbox": [98, 77, 103, 82], "label": "drone", "frame_id": "49", "frame_idx": 49, "interpolated": true, "interpolated_from": [40, 50]}], "51": [{"bbox": [126, 75, 131, 81], "label": "drone", "frame_id": "51", "frame_idx": 51, "interpolated": true, "interpolated_from": [50, 60]}], "52": [{"bbox": [154, 73, 160, 80], "label": "drone", "frame_id": "52", "frame_idx": 52, "interpolated": true, "interpolated_from": [50, 60]}], "53": [{"bbox": [182, 71, 189, 79], "label": "drone", "frame_id": "53", "frame_idx": 53, "interpolated": true, "interpolated_from": [50, 60]}], "54": [{"bbox": [210, 69, 218, 78], "label": "drone", "frame_id": "54", "frame_idx": 54, "interpolated": true, "interpolated_from": [50, 60]}], "55": [{"bbox": [238, 68, 247, 77], "label": "drone", "frame_id": "55", "frame_idx": 55, "interpolated": true, "interpolated_from": [50, 60]}], "56": [{"bbox": [266, 66, 275, 76], "label": "drone", "frame_id": "56", "frame_idx": 56, "interpolated": true, "interpolated_from": [50, 60]}], "57": [{"bbox": [294, 64, 304, 75], "label": "drone", "frame_id": "57", "frame_idx": 57, "interpolated": true, "interpolated_from": [50, 60]}], "58": [{"bbox": [322, 62, 333, 74], "label": "drone", "frame_id": "58", "frame_idx": 58, "interpolated": true, "interpolated_from": [50, 60]}], "59": [{"bbox": [350, 60, 362, 73], "label": "drone", "frame_id": "59", "frame_idx": 59, "interpolated": true, "interpolated_from": [50, 60]}], "61": [], "62": [], "63": [], "64": [], "65": [], "66": [], "67": [], "68": [], "69": [], "71": [], "72": [], "73": [], "74": []}}}