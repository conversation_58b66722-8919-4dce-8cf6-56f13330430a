{"data01": {"sequence_id": "data01", "total_frames": 75, "key_frames": 8, "total_detections": 0, "direct_detections": 0, "interpolated_detections": 0, "metrics": {"precision": 0, "recall": 0.0, "f1": 0, "tp": 0, "fp": 0, "fn": 75}, "detections": {"0": [], "10": [], "20": [], "30": [], "40": [], "50": [], "60": [], "70": [], "1": [], "2": [], "3": [], "4": [], "5": [], "6": [], "7": [], "8": [], "9": [], "11": [], "12": [], "13": [], "14": [], "15": [], "16": [], "17": [], "18": [], "19": [], "21": [], "22": [], "23": [], "24": [], "25": [], "26": [], "27": [], "28": [], "29": [], "31": [], "32": [], "33": [], "34": [], "35": [], "36": [], "37": [], "38": [], "39": [], "41": [], "42": [], "43": [], "44": [], "45": [], "46": [], "47": [], "48": [], "49": [], "51": [], "52": [], "53": [], "54": [], "55": [], "56": [], "57": [], "58": [], "59": [], "61": [], "62": [], "63": [], "64": [], "65": [], "66": [], "67": [], "68": [], "69": [], "71": [], "72": [], "73": [], "74": []}}, "data02": {"sequence_id": "data02", "total_frames": 50, "key_frames": 5, "total_detections": 0, "direct_detections": 0, "interpolated_detections": 0, "metrics": {"precision": 0, "recall": 0.0, "f1": 0, "tp": 0, "fp": 0, "fn": 50}, "detections": {"0": [], "10": [], "20": [], "30": [], "40": [], "1": [], "2": [], "3": [], "4": [], "5": [], "6": [], "7": [], "8": [], "9": [], "11": [], "12": [], "13": [], "14": [], "15": [], "16": [], "17": [], "18": [], "19": [], "21": [], "22": [], "23": [], "24": [], "25": [], "26": [], "27": [], "28": [], "29": [], "31": [], "32": [], "33": [], "34": [], "35": [], "36": [], "37": [], "38": [], "39": [], "41": [], "42": [], "43": [], "44": [], "45": [], "46": [], "47": [], "48": [], "49": []}}, "data04": {"sequence_id": "data04", "total_frames": 50, "key_frames": 5, "total_detections": 0, "direct_detections": 0, "interpolated_detections": 0, "metrics": {"precision": 0, "recall": 0.0, "f1": 0, "tp": 0, "fp": 0, "fn": 50}, "detections": {"0": [], "10": [], "20": [], "30": [], "40": [], "1": [], "2": [], "3": [], "4": [], "5": [], "6": [], "7": [], "8": [], "9": [], "11": [], "12": [], "13": [], "14": [], "15": [], "16": [], "17": [], "18": [], "19": [], "21": [], "22": [], "23": [], "24": [], "25": [], "26": [], "27": [], "28": [], "29": [], "31": [], "32": [], "33": [], "34": [], "35": [], "36": [], "37": [], "38": [], "39": [], "41": [], "42": [], "43": [], "44": [], "45": [], "46": [], "47": [], "48": [], "49": []}}, "data06": {"sequence_id": "data06", "total_frames": 50, "key_frames": 5, "total_detections": 0, "direct_detections": 0, "interpolated_detections": 0, "metrics": {"precision": 0, "recall": 0.0, "f1": 0, "tp": 0, "fp": 0, "fn": 50}, "detections": {"0": [], "10": [], "20": [], "30": [], "40": [], "1": [], "2": [], "3": [], "4": [], "5": [], "6": [], "7": [], "8": [], "9": [], "11": [], "12": [], "13": [], "14": [], "15": [], "16": [], "17": [], "18": [], "19": [], "21": [], "22": [], "23": [], "24": [], "25": [], "26": [], "27": [], "28": [], "29": [], "31": [], "32": [], "33": [], "34": [], "35": [], "36": [], "37": [], "38": [], "39": [], "41": [], "42": [], "43": [], "44": [], "45": [], "46": [], "47": [], "48": [], "49": []}}, "data07": {"sequence_id": "data07", "total_frames": 50, "key_frames": 5, "total_detections": 0, "direct_detections": 0, "interpolated_detections": 0, "metrics": {"precision": 0, "recall": 0.0, "f1": 0, "tp": 0, "fp": 0, "fn": 50}, "detections": {"0": [], "10": [], "20": [], "30": [], "40": [], "1": [], "2": [], "3": [], "4": [], "5": [], "6": [], "7": [], "8": [], "9": [], "11": [], "12": [], "13": [], "14": [], "15": [], "16": [], "17": [], "18": [], "19": [], "21": [], "22": [], "23": [], "24": [], "25": [], "26": [], "27": [], "28": [], "29": [], "31": [], "32": [], "33": [], "34": [], "35": [], "36": [], "37": [], "38": [], "39": [], "41": [], "42": [], "43": [], "44": [], "45": [], "46": [], "47": [], "48": [], "49": []}}, "data19": {"sequence_id": "data19", "total_frames": 75, "key_frames": 8, "total_detections": 0, "direct_detections": 0, "interpolated_detections": 0, "metrics": {"precision": 0, "recall": 0, "f1": 0, "tp": 0, "fp": 0, "fn": 0}, "detections": {"0": [], "10": [], "20": [], "30": [], "40": [], "50": [], "60": [], "70": [], "1": [], "2": [], "3": [], "4": [], "5": [], "6": [], "7": [], "8": [], "9": [], "11": [], "12": [], "13": [], "14": [], "15": [], "16": [], "17": [], "18": [], "19": [], "21": [], "22": [], "23": [], "24": [], "25": [], "26": [], "27": [], "28": [], "29": [], "31": [], "32": [], "33": [], "34": [], "35": [], "36": [], "37": [], "38": [], "39": [], "41": [], "42": [], "43": [], "44": [], "45": [], "46": [], "47": [], "48": [], "49": [], "51": [], "52": [], "53": [], "54": [], "55": [], "56": [], "57": [], "58": [], "59": [], "61": [], "62": [], "63": [], "64": [], "65": [], "66": [], "67": [], "68": [], "69": [], "71": [], "72": [], "73": [], "74": []}}, "data23": {"sequence_id": "data23", "total_frames": 50, "key_frames": 5, "total_detections": 0, "direct_detections": 0, "interpolated_detections": 0, "metrics": {"precision": 0, "recall": 0, "f1": 0, "tp": 0, "fp": 0, "fn": 0}, "detections": {"0": [], "10": [], "20": [], "30": [], "40": [], "1": [], "2": [], "3": [], "4": [], "5": [], "6": [], "7": [], "8": [], "9": [], "11": [], "12": [], "13": [], "14": [], "15": [], "16": [], "17": [], "18": [], "19": [], "21": [], "22": [], "23": [], "24": [], "25": [], "26": [], "27": [], "28": [], "29": [], "31": [], "32": [], "33": [], "34": [], "35": [], "36": [], "37": [], "38": [], "39": [], "41": [], "42": [], "43": [], "44": [], "45": [], "46": [], "47": [], "48": [], "49": []}}, "data26": {"sequence_id": "data26", "total_frames": 75, "key_frames": 8, "total_detections": 0, "direct_detections": 0, "interpolated_detections": 0, "metrics": {"precision": 0, "recall": 0, "f1": 0, "tp": 0, "fp": 0, "fn": 0}, "detections": {"0": [], "10": [], "20": [], "30": [], "40": [], "50": [], "60": [], "70": [], "1": [], "2": [], "3": [], "4": [], "5": [], "6": [], "7": [], "8": [], "9": [], "11": [], "12": [], "13": [], "14": [], "15": [], "16": [], "17": [], "18": [], "19": [], "21": [], "22": [], "23": [], "24": [], "25": [], "26": [], "27": [], "28": [], "29": [], "31": [], "32": [], "33": [], "34": [], "35": [], "36": [], "37": [], "38": [], "39": [], "41": [], "42": [], "43": [], "44": [], "45": [], "46": [], "47": [], "48": [], "49": [], "51": [], "52": [], "53": [], "54": [], "55": [], "56": [], "57": [], "58": [], "59": [], "61": [], "62": [], "63": [], "64": [], "65": [], "66": [], "67": [], "68": [], "69": [], "71": [], "72": [], "73": [], "74": []}}}