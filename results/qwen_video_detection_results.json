{"data01": {"sequence_id": "data01", "total_frames": 75, "key_frames": 8, "total_detections": 75, "direct_detections": 8, "interpolated_detections": 67, "metrics": {"precision": 0.0, "recall": 0.0, "f1": 0, "tp": 0, "fp": 75, "fn": 75}, "detections": {"0": [{"bbox": [198, 177, 220, 192], "label": "drone", "frame_id": "0", "frame_idx": 0}], "10": [{"bbox": [198, 177, 220, 192], "label": "drone", "frame_id": "10", "frame_idx": 10}], "20": [{"bbox": [198, 177, 220, 192], "label": "drone", "frame_id": "20", "frame_idx": 20}], "30": [{"bbox": [198, 177, 220, 192], "label": "drone", "frame_id": "30", "frame_idx": 30}], "40": [{"bbox": [198, 120, 203, 125], "label": "drone", "frame_id": "40", "frame_idx": 40}], "50": [{"bbox": [200, 120, 205, 125], "label": "drone", "frame_id": "50", "frame_idx": 50}], "60": [{"bbox": [202, 120, 207, 125], "label": "drone", "frame_id": "60", "frame_idx": 60}], "70": [{"bbox": [204, 120, 209, 125], "label": "drone", "frame_id": "70", "frame_idx": 70}], "1": [{"bbox": [198, 177, 220, 192], "label": "drone", "frame_id": "1", "frame_idx": 1, "interpolated": true, "interpolated_from": [0, 10]}], "2": [{"bbox": [198, 177, 220, 192], "label": "drone", "frame_id": "2", "frame_idx": 2, "interpolated": true, "interpolated_from": [0, 10]}], "3": [{"bbox": [198, 177, 220, 192], "label": "drone", "frame_id": "3", "frame_idx": 3, "interpolated": true, "interpolated_from": [0, 10]}], "4": [{"bbox": [198, 177, 220, 192], "label": "drone", "frame_id": "4", "frame_idx": 4, "interpolated": true, "interpolated_from": [0, 10]}], "5": [{"bbox": [198, 177, 220, 192], "label": "drone", "frame_id": "5", "frame_idx": 5, "interpolated": true, "interpolated_from": [0, 10]}], "6": [{"bbox": [198, 177, 220, 192], "label": "drone", "frame_id": "6", "frame_idx": 6, "interpolated": true, "interpolated_from": [0, 10]}], "7": [{"bbox": [198, 177, 220, 192], "label": "drone", "frame_id": "7", "frame_idx": 7, "interpolated": true, "interpolated_from": [0, 10]}], "8": [{"bbox": [198, 177, 220, 192], "label": "drone", "frame_id": "8", "frame_idx": 8, "interpolated": true, "interpolated_from": [0, 10]}], "9": [{"bbox": [198, 177, 220, 192], "label": "drone", "frame_id": "9", "frame_idx": 9, "interpolated": true, "interpolated_from": [0, 10]}], "11": [{"bbox": [198, 177, 220, 192], "label": "drone", "frame_id": "11", "frame_idx": 11, "interpolated": true, "interpolated_from": [10, 20]}], "12": [{"bbox": [198, 177, 220, 192], "label": "drone", "frame_id": "12", "frame_idx": 12, "interpolated": true, "interpolated_from": [10, 20]}], "13": [{"bbox": [198, 177, 220, 192], "label": "drone", "frame_id": "13", "frame_idx": 13, "interpolated": true, "interpolated_from": [10, 20]}], "14": [{"bbox": [198, 177, 220, 192], "label": "drone", "frame_id": "14", "frame_idx": 14, "interpolated": true, "interpolated_from": [10, 20]}], "15": [{"bbox": [198, 177, 220, 192], "label": "drone", "frame_id": "15", "frame_idx": 15, "interpolated": true, "interpolated_from": [10, 20]}], "16": [{"bbox": [198, 177, 220, 192], "label": "drone", "frame_id": "16", "frame_idx": 16, "interpolated": true, "interpolated_from": [10, 20]}], "17": [{"bbox": [198, 177, 220, 192], "label": "drone", "frame_id": "17", "frame_idx": 17, "interpolated": true, "interpolated_from": [10, 20]}], "18": [{"bbox": [198, 177, 220, 192], "label": "drone", "frame_id": "18", "frame_idx": 18, "interpolated": true, "interpolated_from": [10, 20]}], "19": [{"bbox": [198, 177, 220, 192], "label": "drone", "frame_id": "19", "frame_idx": 19, "interpolated": true, "interpolated_from": [10, 20]}], "21": [{"bbox": [198, 177, 220, 192], "label": "drone", "frame_id": "21", "frame_idx": 21, "interpolated": true, "interpolated_from": [20, 30]}], "22": [{"bbox": [198, 177, 220, 192], "label": "drone", "frame_id": "22", "frame_idx": 22, "interpolated": true, "interpolated_from": [20, 30]}], "23": [{"bbox": [198, 177, 220, 192], "label": "drone", "frame_id": "23", "frame_idx": 23, "interpolated": true, "interpolated_from": [20, 30]}], "24": [{"bbox": [198, 177, 220, 192], "label": "drone", "frame_id": "24", "frame_idx": 24, "interpolated": true, "interpolated_from": [20, 30]}], "25": [{"bbox": [198, 177, 220, 192], "label": "drone", "frame_id": "25", "frame_idx": 25, "interpolated": true, "interpolated_from": [20, 30]}], "26": [{"bbox": [198, 177, 220, 192], "label": "drone", "frame_id": "26", "frame_idx": 26, "interpolated": true, "interpolated_from": [20, 30]}], "27": [{"bbox": [198, 177, 220, 192], "label": "drone", "frame_id": "27", "frame_idx": 27, "interpolated": true, "interpolated_from": [20, 30]}], "28": [{"bbox": [198, 177, 220, 192], "label": "drone", "frame_id": "28", "frame_idx": 28, "interpolated": true, "interpolated_from": [20, 30]}], "29": [{"bbox": [198, 177, 220, 192], "label": "drone", "frame_id": "29", "frame_idx": 29, "interpolated": true, "interpolated_from": [20, 30]}], "31": [{"bbox": [198, 171, 218, 185], "label": "drone", "frame_id": "31", "frame_idx": 31, "interpolated": true, "interpolated_from": [30, 40]}], "32": [{"bbox": [198, 165, 216, 178], "label": "drone", "frame_id": "32", "frame_idx": 32, "interpolated": true, "interpolated_from": [30, 40]}], "33": [{"bbox": [198, 159, 214, 171], "label": "drone", "frame_id": "33", "frame_idx": 33, "interpolated": true, "interpolated_from": [30, 40]}], "34": [{"bbox": [198, 154, 213, 165], "label": "drone", "frame_id": "34", "frame_idx": 34, "interpolated": true, "interpolated_from": [30, 40]}], "35": [{"bbox": [198, 148, 211, 158], "label": "drone", "frame_id": "35", "frame_idx": 35, "interpolated": true, "interpolated_from": [30, 40]}], "36": [{"bbox": [198, 142, 209, 151], "label": "drone", "frame_id": "36", "frame_idx": 36, "interpolated": true, "interpolated_from": [30, 40]}], "37": [{"bbox": [198, 137, 208, 145], "label": "drone", "frame_id": "37", "frame_idx": 37, "interpolated": true, "interpolated_from": [30, 40]}], "38": [{"bbox": [198, 131, 206, 138], "label": "drone", "frame_id": "38", "frame_idx": 38, "interpolated": true, "interpolated_from": [30, 40]}], "39": [{"bbox": [198, 125, 204, 131], "label": "drone", "frame_id": "39", "frame_idx": 39, "interpolated": true, "interpolated_from": [30, 40]}], "41": [{"bbox": [198, 120, 203, 125], "label": "drone", "frame_id": "41", "frame_idx": 41, "interpolated": true, "interpolated_from": [40, 50]}], "42": [{"bbox": [198, 120, 203, 125], "label": "drone", "frame_id": "42", "frame_idx": 42, "interpolated": true, "interpolated_from": [40, 50]}], "43": [{"bbox": [198, 120, 203, 125], "label": "drone", "frame_id": "43", "frame_idx": 43, "interpolated": true, "interpolated_from": [40, 50]}], "44": [{"bbox": [198, 120, 203, 125], "label": "drone", "frame_id": "44", "frame_idx": 44, "interpolated": true, "interpolated_from": [40, 50]}], "45": [{"bbox": [199, 120, 204, 125], "label": "drone", "frame_id": "45", "frame_idx": 45, "interpolated": true, "interpolated_from": [40, 50]}], "46": [{"bbox": [199, 120, 204, 125], "label": "drone", "frame_id": "46", "frame_idx": 46, "interpolated": true, "interpolated_from": [40, 50]}], "47": [{"bbox": [199, 120, 204, 125], "label": "drone", "frame_id": "47", "frame_idx": 47, "interpolated": true, "interpolated_from": [40, 50]}], "48": [{"bbox": [199, 120, 204, 125], "label": "drone", "frame_id": "48", "frame_idx": 48, "interpolated": true, "interpolated_from": [40, 50]}], "49": [{"bbox": [199, 120, 204, 125], "label": "drone", "frame_id": "49", "frame_idx": 49, "interpolated": true, "interpolated_from": [40, 50]}], "51": [{"bbox": [200, 120, 205, 125], "label": "drone", "frame_id": "51", "frame_idx": 51, "interpolated": true, "interpolated_from": [50, 60]}], "52": [{"bbox": [200, 120, 205, 125], "label": "drone", "frame_id": "52", "frame_idx": 52, "interpolated": true, "interpolated_from": [50, 60]}], "53": [{"bbox": [200, 120, 205, 125], "label": "drone", "frame_id": "53", "frame_idx": 53, "interpolated": true, "interpolated_from": [50, 60]}], "54": [{"bbox": [200, 120, 205, 125], "label": "drone", "frame_id": "54", "frame_idx": 54, "interpolated": true, "interpolated_from": [50, 60]}], "55": [{"bbox": [201, 120, 206, 125], "label": "drone", "frame_id": "55", "frame_idx": 55, "interpolated": true, "interpolated_from": [50, 60]}], "56": [{"bbox": [201, 120, 206, 125], "label": "drone", "frame_id": "56", "frame_idx": 56, "interpolated": true, "interpolated_from": [50, 60]}], "57": [{"bbox": [201, 120, 206, 125], "label": "drone", "frame_id": "57", "frame_idx": 57, "interpolated": true, "interpolated_from": [50, 60]}], "58": [{"bbox": [201, 120, 206, 125], "label": "drone", "frame_id": "58", "frame_idx": 58, "interpolated": true, "interpolated_from": [50, 60]}], "59": [{"bbox": [201, 120, 206, 125], "label": "drone", "frame_id": "59", "frame_idx": 59, "interpolated": true, "interpolated_from": [50, 60]}], "61": [{"bbox": [202, 120, 207, 125], "label": "drone", "frame_id": "61", "frame_idx": 61, "interpolated": true, "interpolated_from": [60, 70]}], "62": [{"bbox": [202, 120, 207, 125], "label": "drone", "frame_id": "62", "frame_idx": 62, "interpolated": true, "interpolated_from": [60, 70]}], "63": [{"bbox": [202, 120, 207, 125], "label": "drone", "frame_id": "63", "frame_idx": 63, "interpolated": true, "interpolated_from": [60, 70]}], "64": [{"bbox": [202, 120, 207, 125], "label": "drone", "frame_id": "64", "frame_idx": 64, "interpolated": true, "interpolated_from": [60, 70]}], "65": [{"bbox": [203, 120, 208, 125], "label": "drone", "frame_id": "65", "frame_idx": 65, "interpolated": true, "interpolated_from": [60, 70]}], "66": [{"bbox": [203, 120, 208, 125], "label": "drone", "frame_id": "66", "frame_idx": 66, "interpolated": true, "interpolated_from": [60, 70]}], "67": [{"bbox": [203, 120, 208, 125], "label": "drone", "frame_id": "67", "frame_idx": 67, "interpolated": true, "interpolated_from": [60, 70]}], "68": [{"bbox": [203, 120, 208, 125], "label": "drone", "frame_id": "68", "frame_idx": 68, "interpolated": true, "interpolated_from": [60, 70]}], "69": [{"bbox": [203, 120, 208, 125], "label": "drone", "frame_id": "69", "frame_idx": 69, "interpolated": true, "interpolated_from": [60, 70]}], "71": [{"bbox": [204, 120, 209, 125], "label": "drone", "frame_id": "71", "frame_idx": 71, "interpolated": true, "interpolated_from": [70]}], "72": [{"bbox": [204, 120, 209, 125], "label": "drone", "frame_id": "72", "frame_idx": 72, "interpolated": true, "interpolated_from": [70]}], "73": [{"bbox": [204, 120, 209, 125], "label": "drone", "frame_id": "73", "frame_idx": 73, "interpolated": true, "interpolated_from": [70]}], "74": [{"bbox": [204, 120, 209, 125], "label": "drone", "frame_id": "74", "frame_idx": 74, "interpolated": true, "interpolated_from": [70]}]}}, "data02": {"sequence_id": "data02", "total_frames": 50, "key_frames": 5, "total_detections": 50, "direct_detections": 4, "interpolated_detections": 46, "metrics": {"precision": 0.0, "recall": 0.0, "f1": 0, "tp": 0, "fp": 50, "fn": 50}, "detections": {"0": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "0", "frame_idx": 0}], "10": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "10", "frame_idx": 10}], "20": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "20", "frame_idx": 20}], "30": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "30", "frame_idx": 30}], "1": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "1", "frame_idx": 1, "interpolated": true, "interpolated_from": [0, 10]}], "2": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "2", "frame_idx": 2, "interpolated": true, "interpolated_from": [0, 10]}], "3": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "3", "frame_idx": 3, "interpolated": true, "interpolated_from": [0, 10]}], "4": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "4", "frame_idx": 4, "interpolated": true, "interpolated_from": [0, 10]}], "5": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "5", "frame_idx": 5, "interpolated": true, "interpolated_from": [0, 10]}], "6": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "6", "frame_idx": 6, "interpolated": true, "interpolated_from": [0, 10]}], "7": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "7", "frame_idx": 7, "interpolated": true, "interpolated_from": [0, 10]}], "8": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "8", "frame_idx": 8, "interpolated": true, "interpolated_from": [0, 10]}], "9": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "9", "frame_idx": 9, "interpolated": true, "interpolated_from": [0, 10]}], "11": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "11", "frame_idx": 11, "interpolated": true, "interpolated_from": [10, 20]}], "12": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "12", "frame_idx": 12, "interpolated": true, "interpolated_from": [10, 20]}], "13": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "13", "frame_idx": 13, "interpolated": true, "interpolated_from": [10, 20]}], "14": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "14", "frame_idx": 14, "interpolated": true, "interpolated_from": [10, 20]}], "15": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "15", "frame_idx": 15, "interpolated": true, "interpolated_from": [10, 20]}], "16": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "16", "frame_idx": 16, "interpolated": true, "interpolated_from": [10, 20]}], "17": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "17", "frame_idx": 17, "interpolated": true, "interpolated_from": [10, 20]}], "18": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "18", "frame_idx": 18, "interpolated": true, "interpolated_from": [10, 20]}], "19": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "19", "frame_idx": 19, "interpolated": true, "interpolated_from": [10, 20]}], "21": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "21", "frame_idx": 21, "interpolated": true, "interpolated_from": [20, 30]}], "22": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "22", "frame_idx": 22, "interpolated": true, "interpolated_from": [20, 30]}], "23": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "23", "frame_idx": 23, "interpolated": true, "interpolated_from": [20, 30]}], "24": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "24", "frame_idx": 24, "interpolated": true, "interpolated_from": [20, 30]}], "25": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "25", "frame_idx": 25, "interpolated": true, "interpolated_from": [20, 30]}], "26": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "26", "frame_idx": 26, "interpolated": true, "interpolated_from": [20, 30]}], "27": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "27", "frame_idx": 27, "interpolated": true, "interpolated_from": [20, 30]}], "28": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "28", "frame_idx": 28, "interpolated": true, "interpolated_from": [20, 30]}], "29": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "29", "frame_idx": 29, "interpolated": true, "interpolated_from": [20, 30]}], "31": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "31", "frame_idx": 31, "interpolated": true, "interpolated_from": [30]}], "32": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "32", "frame_idx": 32, "interpolated": true, "interpolated_from": [30]}], "33": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "33", "frame_idx": 33, "interpolated": true, "interpolated_from": [30]}], "34": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "34", "frame_idx": 34, "interpolated": true, "interpolated_from": [30]}], "35": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "35", "frame_idx": 35, "interpolated": true, "interpolated_from": [30]}], "36": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "36", "frame_idx": 36, "interpolated": true, "interpolated_from": [30]}], "37": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "37", "frame_idx": 37, "interpolated": true, "interpolated_from": [30]}], "38": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "38", "frame_idx": 38, "interpolated": true, "interpolated_from": [30]}], "39": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "39", "frame_idx": 39, "interpolated": true, "interpolated_from": [30]}], "40": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "40", "frame_idx": 40, "interpolated": true, "interpolated_from": [30]}], "41": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "41", "frame_idx": 41, "interpolated": true, "interpolated_from": [30]}], "42": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "42", "frame_idx": 42, "interpolated": true, "interpolated_from": [30]}], "43": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "43", "frame_idx": 43, "interpolated": true, "interpolated_from": [30]}], "44": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "44", "frame_idx": 44, "interpolated": true, "interpolated_from": [30]}], "45": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "45", "frame_idx": 45, "interpolated": true, "interpolated_from": [30]}], "46": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "46", "frame_idx": 46, "interpolated": true, "interpolated_from": [30]}], "47": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "47", "frame_idx": 47, "interpolated": true, "interpolated_from": [30]}], "48": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "48", "frame_idx": 48, "interpolated": true, "interpolated_from": [30]}], "49": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "49", "frame_idx": 49, "interpolated": true, "interpolated_from": [30]}]}}, "data04": {"sequence_id": "data04", "total_frames": 50, "key_frames": 5, "total_detections": 50, "direct_detections": 4, "interpolated_detections": 46, "metrics": {"precision": 0.0, "recall": 0.0, "f1": 0, "tp": 0, "fp": 50, "fn": 50}, "detections": {"0": [{"bbox": [189, 178, 194, 183], "label": "drone", "frame_id": "0", "frame_idx": 0}], "10": [{"bbox": [189, 178, 194, 183], "label": "drone", "frame_id": "10", "frame_idx": 10}], "20": [{"bbox": [189, 178, 194, 183], "label": "drone", "frame_id": "20", "frame_idx": 20}], "30": [{"bbox": [189, 178, 194, 183], "label": "drone", "frame_id": "30", "frame_idx": 30}], "1": [{"bbox": [189, 178, 194, 183], "label": "drone", "frame_id": "1", "frame_idx": 1, "interpolated": true, "interpolated_from": [0, 10]}], "2": [{"bbox": [189, 178, 194, 183], "label": "drone", "frame_id": "2", "frame_idx": 2, "interpolated": true, "interpolated_from": [0, 10]}], "3": [{"bbox": [189, 178, 194, 183], "label": "drone", "frame_id": "3", "frame_idx": 3, "interpolated": true, "interpolated_from": [0, 10]}], "4": [{"bbox": [189, 178, 194, 183], "label": "drone", "frame_id": "4", "frame_idx": 4, "interpolated": true, "interpolated_from": [0, 10]}], "5": [{"bbox": [189, 178, 194, 183], "label": "drone", "frame_id": "5", "frame_idx": 5, "interpolated": true, "interpolated_from": [0, 10]}], "6": [{"bbox": [189, 178, 194, 183], "label": "drone", "frame_id": "6", "frame_idx": 6, "interpolated": true, "interpolated_from": [0, 10]}], "7": [{"bbox": [189, 178, 194, 183], "label": "drone", "frame_id": "7", "frame_idx": 7, "interpolated": true, "interpolated_from": [0, 10]}], "8": [{"bbox": [189, 178, 194, 183], "label": "drone", "frame_id": "8", "frame_idx": 8, "interpolated": true, "interpolated_from": [0, 10]}], "9": [{"bbox": [189, 178, 194, 183], "label": "drone", "frame_id": "9", "frame_idx": 9, "interpolated": true, "interpolated_from": [0, 10]}], "11": [{"bbox": [189, 178, 194, 183], "label": "drone", "frame_id": "11", "frame_idx": 11, "interpolated": true, "interpolated_from": [10, 20]}], "12": [{"bbox": [189, 178, 194, 183], "label": "drone", "frame_id": "12", "frame_idx": 12, "interpolated": true, "interpolated_from": [10, 20]}], "13": [{"bbox": [189, 178, 194, 183], "label": "drone", "frame_id": "13", "frame_idx": 13, "interpolated": true, "interpolated_from": [10, 20]}], "14": [{"bbox": [189, 178, 194, 183], "label": "drone", "frame_id": "14", "frame_idx": 14, "interpolated": true, "interpolated_from": [10, 20]}], "15": [{"bbox": [189, 178, 194, 183], "label": "drone", "frame_id": "15", "frame_idx": 15, "interpolated": true, "interpolated_from": [10, 20]}], "16": [{"bbox": [189, 178, 194, 183], "label": "drone", "frame_id": "16", "frame_idx": 16, "interpolated": true, "interpolated_from": [10, 20]}], "17": [{"bbox": [189, 178, 194, 183], "label": "drone", "frame_id": "17", "frame_idx": 17, "interpolated": true, "interpolated_from": [10, 20]}], "18": [{"bbox": [189, 178, 194, 183], "label": "drone", "frame_id": "18", "frame_idx": 18, "interpolated": true, "interpolated_from": [10, 20]}], "19": [{"bbox": [189, 178, 194, 183], "label": "drone", "frame_id": "19", "frame_idx": 19, "interpolated": true, "interpolated_from": [10, 20]}], "21": [{"bbox": [189, 178, 194, 183], "label": "drone", "frame_id": "21", "frame_idx": 21, "interpolated": true, "interpolated_from": [20, 30]}], "22": [{"bbox": [189, 178, 194, 183], "label": "drone", "frame_id": "22", "frame_idx": 22, "interpolated": true, "interpolated_from": [20, 30]}], "23": [{"bbox": [189, 178, 194, 183], "label": "drone", "frame_id": "23", "frame_idx": 23, "interpolated": true, "interpolated_from": [20, 30]}], "24": [{"bbox": [189, 178, 194, 183], "label": "drone", "frame_id": "24", "frame_idx": 24, "interpolated": true, "interpolated_from": [20, 30]}], "25": [{"bbox": [189, 178, 194, 183], "label": "drone", "frame_id": "25", "frame_idx": 25, "interpolated": true, "interpolated_from": [20, 30]}], "26": [{"bbox": [189, 178, 194, 183], "label": "drone", "frame_id": "26", "frame_idx": 26, "interpolated": true, "interpolated_from": [20, 30]}], "27": [{"bbox": [189, 178, 194, 183], "label": "drone", "frame_id": "27", "frame_idx": 27, "interpolated": true, "interpolated_from": [20, 30]}], "28": [{"bbox": [189, 178, 194, 183], "label": "drone", "frame_id": "28", "frame_idx": 28, "interpolated": true, "interpolated_from": [20, 30]}], "29": [{"bbox": [189, 178, 194, 183], "label": "drone", "frame_id": "29", "frame_idx": 29, "interpolated": true, "interpolated_from": [20, 30]}], "31": [{"bbox": [189, 178, 194, 183], "label": "drone", "frame_id": "31", "frame_idx": 31, "interpolated": true, "interpolated_from": [30]}], "32": [{"bbox": [189, 178, 194, 183], "label": "drone", "frame_id": "32", "frame_idx": 32, "interpolated": true, "interpolated_from": [30]}], "33": [{"bbox": [189, 178, 194, 183], "label": "drone", "frame_id": "33", "frame_idx": 33, "interpolated": true, "interpolated_from": [30]}], "34": [{"bbox": [189, 178, 194, 183], "label": "drone", "frame_id": "34", "frame_idx": 34, "interpolated": true, "interpolated_from": [30]}], "35": [{"bbox": [189, 178, 194, 183], "label": "drone", "frame_id": "35", "frame_idx": 35, "interpolated": true, "interpolated_from": [30]}], "36": [{"bbox": [189, 178, 194, 183], "label": "drone", "frame_id": "36", "frame_idx": 36, "interpolated": true, "interpolated_from": [30]}], "37": [{"bbox": [189, 178, 194, 183], "label": "drone", "frame_id": "37", "frame_idx": 37, "interpolated": true, "interpolated_from": [30]}], "38": [{"bbox": [189, 178, 194, 183], "label": "drone", "frame_id": "38", "frame_idx": 38, "interpolated": true, "interpolated_from": [30]}], "39": [{"bbox": [189, 178, 194, 183], "label": "drone", "frame_id": "39", "frame_idx": 39, "interpolated": true, "interpolated_from": [30]}], "40": [{"bbox": [189, 178, 194, 183], "label": "drone", "frame_id": "40", "frame_idx": 40, "interpolated": true, "interpolated_from": [30]}], "41": [{"bbox": [189, 178, 194, 183], "label": "drone", "frame_id": "41", "frame_idx": 41, "interpolated": true, "interpolated_from": [30]}], "42": [{"bbox": [189, 178, 194, 183], "label": "drone", "frame_id": "42", "frame_idx": 42, "interpolated": true, "interpolated_from": [30]}], "43": [{"bbox": [189, 178, 194, 183], "label": "drone", "frame_id": "43", "frame_idx": 43, "interpolated": true, "interpolated_from": [30]}], "44": [{"bbox": [189, 178, 194, 183], "label": "drone", "frame_id": "44", "frame_idx": 44, "interpolated": true, "interpolated_from": [30]}], "45": [{"bbox": [189, 178, 194, 183], "label": "drone", "frame_id": "45", "frame_idx": 45, "interpolated": true, "interpolated_from": [30]}], "46": [{"bbox": [189, 178, 194, 183], "label": "drone", "frame_id": "46", "frame_idx": 46, "interpolated": true, "interpolated_from": [30]}], "47": [{"bbox": [189, 178, 194, 183], "label": "drone", "frame_id": "47", "frame_idx": 47, "interpolated": true, "interpolated_from": [30]}], "48": [{"bbox": [189, 178, 194, 183], "label": "drone", "frame_id": "48", "frame_idx": 48, "interpolated": true, "interpolated_from": [30]}], "49": [{"bbox": [189, 178, 194, 183], "label": "drone", "frame_id": "49", "frame_idx": 49, "interpolated": true, "interpolated_from": [30]}]}}, "data06": {"sequence_id": "data06", "total_frames": 50, "key_frames": 5, "total_detections": 50, "direct_detections": 4, "interpolated_detections": 46, "metrics": {"precision": 0.0, "recall": 0.0, "f1": 0, "tp": 0, "fp": 50, "fn": 50}, "detections": {"0": [{"bbox": [178, 190, 182, 194], "label": "drone", "frame_id": "0", "frame_idx": 0}], "10": [{"bbox": [178, 190, 182, 194], "label": "drone", "frame_id": "10", "frame_idx": 10}], "20": [{"bbox": [178, 190, 182, 194], "label": "drone", "frame_id": "20", "frame_idx": 20}], "30": [{"bbox": [178, 190, 182, 194], "label": "drone", "frame_id": "30", "frame_idx": 30}], "1": [{"bbox": [178, 190, 182, 194], "label": "drone", "frame_id": "1", "frame_idx": 1, "interpolated": true, "interpolated_from": [0, 10]}], "2": [{"bbox": [178, 190, 182, 194], "label": "drone", "frame_id": "2", "frame_idx": 2, "interpolated": true, "interpolated_from": [0, 10]}], "3": [{"bbox": [178, 190, 182, 194], "label": "drone", "frame_id": "3", "frame_idx": 3, "interpolated": true, "interpolated_from": [0, 10]}], "4": [{"bbox": [178, 190, 182, 194], "label": "drone", "frame_id": "4", "frame_idx": 4, "interpolated": true, "interpolated_from": [0, 10]}], "5": [{"bbox": [178, 190, 182, 194], "label": "drone", "frame_id": "5", "frame_idx": 5, "interpolated": true, "interpolated_from": [0, 10]}], "6": [{"bbox": [178, 190, 182, 194], "label": "drone", "frame_id": "6", "frame_idx": 6, "interpolated": true, "interpolated_from": [0, 10]}], "7": [{"bbox": [178, 190, 182, 194], "label": "drone", "frame_id": "7", "frame_idx": 7, "interpolated": true, "interpolated_from": [0, 10]}], "8": [{"bbox": [178, 190, 182, 194], "label": "drone", "frame_id": "8", "frame_idx": 8, "interpolated": true, "interpolated_from": [0, 10]}], "9": [{"bbox": [178, 190, 182, 194], "label": "drone", "frame_id": "9", "frame_idx": 9, "interpolated": true, "interpolated_from": [0, 10]}], "11": [{"bbox": [178, 190, 182, 194], "label": "drone", "frame_id": "11", "frame_idx": 11, "interpolated": true, "interpolated_from": [10, 20]}], "12": [{"bbox": [178, 190, 182, 194], "label": "drone", "frame_id": "12", "frame_idx": 12, "interpolated": true, "interpolated_from": [10, 20]}], "13": [{"bbox": [178, 190, 182, 194], "label": "drone", "frame_id": "13", "frame_idx": 13, "interpolated": true, "interpolated_from": [10, 20]}], "14": [{"bbox": [178, 190, 182, 194], "label": "drone", "frame_id": "14", "frame_idx": 14, "interpolated": true, "interpolated_from": [10, 20]}], "15": [{"bbox": [178, 190, 182, 194], "label": "drone", "frame_id": "15", "frame_idx": 15, "interpolated": true, "interpolated_from": [10, 20]}], "16": [{"bbox": [178, 190, 182, 194], "label": "drone", "frame_id": "16", "frame_idx": 16, "interpolated": true, "interpolated_from": [10, 20]}], "17": [{"bbox": [178, 190, 182, 194], "label": "drone", "frame_id": "17", "frame_idx": 17, "interpolated": true, "interpolated_from": [10, 20]}], "18": [{"bbox": [178, 190, 182, 194], "label": "drone", "frame_id": "18", "frame_idx": 18, "interpolated": true, "interpolated_from": [10, 20]}], "19": [{"bbox": [178, 190, 182, 194], "label": "drone", "frame_id": "19", "frame_idx": 19, "interpolated": true, "interpolated_from": [10, 20]}], "21": [{"bbox": [178, 190, 182, 194], "label": "drone", "frame_id": "21", "frame_idx": 21, "interpolated": true, "interpolated_from": [20, 30]}], "22": [{"bbox": [178, 190, 182, 194], "label": "drone", "frame_id": "22", "frame_idx": 22, "interpolated": true, "interpolated_from": [20, 30]}], "23": [{"bbox": [178, 190, 182, 194], "label": "drone", "frame_id": "23", "frame_idx": 23, "interpolated": true, "interpolated_from": [20, 30]}], "24": [{"bbox": [178, 190, 182, 194], "label": "drone", "frame_id": "24", "frame_idx": 24, "interpolated": true, "interpolated_from": [20, 30]}], "25": [{"bbox": [178, 190, 182, 194], "label": "drone", "frame_id": "25", "frame_idx": 25, "interpolated": true, "interpolated_from": [20, 30]}], "26": [{"bbox": [178, 190, 182, 194], "label": "drone", "frame_id": "26", "frame_idx": 26, "interpolated": true, "interpolated_from": [20, 30]}], "27": [{"bbox": [178, 190, 182, 194], "label": "drone", "frame_id": "27", "frame_idx": 27, "interpolated": true, "interpolated_from": [20, 30]}], "28": [{"bbox": [178, 190, 182, 194], "label": "drone", "frame_id": "28", "frame_idx": 28, "interpolated": true, "interpolated_from": [20, 30]}], "29": [{"bbox": [178, 190, 182, 194], "label": "drone", "frame_id": "29", "frame_idx": 29, "interpolated": true, "interpolated_from": [20, 30]}], "31": [{"bbox": [178, 190, 182, 194], "label": "drone", "frame_id": "31", "frame_idx": 31, "interpolated": true, "interpolated_from": [30]}], "32": [{"bbox": [178, 190, 182, 194], "label": "drone", "frame_id": "32", "frame_idx": 32, "interpolated": true, "interpolated_from": [30]}], "33": [{"bbox": [178, 190, 182, 194], "label": "drone", "frame_id": "33", "frame_idx": 33, "interpolated": true, "interpolated_from": [30]}], "34": [{"bbox": [178, 190, 182, 194], "label": "drone", "frame_id": "34", "frame_idx": 34, "interpolated": true, "interpolated_from": [30]}], "35": [{"bbox": [178, 190, 182, 194], "label": "drone", "frame_id": "35", "frame_idx": 35, "interpolated": true, "interpolated_from": [30]}], "36": [{"bbox": [178, 190, 182, 194], "label": "drone", "frame_id": "36", "frame_idx": 36, "interpolated": true, "interpolated_from": [30]}], "37": [{"bbox": [178, 190, 182, 194], "label": "drone", "frame_id": "37", "frame_idx": 37, "interpolated": true, "interpolated_from": [30]}], "38": [{"bbox": [178, 190, 182, 194], "label": "drone", "frame_id": "38", "frame_idx": 38, "interpolated": true, "interpolated_from": [30]}], "39": [{"bbox": [178, 190, 182, 194], "label": "drone", "frame_id": "39", "frame_idx": 39, "interpolated": true, "interpolated_from": [30]}], "40": [{"bbox": [178, 190, 182, 194], "label": "drone", "frame_id": "40", "frame_idx": 40, "interpolated": true, "interpolated_from": [30]}], "41": [{"bbox": [178, 190, 182, 194], "label": "drone", "frame_id": "41", "frame_idx": 41, "interpolated": true, "interpolated_from": [30]}], "42": [{"bbox": [178, 190, 182, 194], "label": "drone", "frame_id": "42", "frame_idx": 42, "interpolated": true, "interpolated_from": [30]}], "43": [{"bbox": [178, 190, 182, 194], "label": "drone", "frame_id": "43", "frame_idx": 43, "interpolated": true, "interpolated_from": [30]}], "44": [{"bbox": [178, 190, 182, 194], "label": "drone", "frame_id": "44", "frame_idx": 44, "interpolated": true, "interpolated_from": [30]}], "45": [{"bbox": [178, 190, 182, 194], "label": "drone", "frame_id": "45", "frame_idx": 45, "interpolated": true, "interpolated_from": [30]}], "46": [{"bbox": [178, 190, 182, 194], "label": "drone", "frame_id": "46", "frame_idx": 46, "interpolated": true, "interpolated_from": [30]}], "47": [{"bbox": [178, 190, 182, 194], "label": "drone", "frame_id": "47", "frame_idx": 47, "interpolated": true, "interpolated_from": [30]}], "48": [{"bbox": [178, 190, 182, 194], "label": "drone", "frame_id": "48", "frame_idx": 48, "interpolated": true, "interpolated_from": [30]}], "49": [{"bbox": [178, 190, 182, 194], "label": "drone", "frame_id": "49", "frame_idx": 49, "interpolated": true, "interpolated_from": [30]}]}}, "data07": {"sequence_id": "data07", "total_frames": 50, "key_frames": 5, "total_detections": 50, "direct_detections": 4, "interpolated_detections": 46, "metrics": {"precision": 0.0, "recall": 0.0, "f1": 0, "tp": 0, "fp": 50, "fn": 50}, "detections": {"0": [{"bbox": [128, 190, 133, 195], "label": "drone", "frame_id": "0", "frame_idx": 0}], "10": [{"bbox": [128, 190, 133, 195], "label": "drone", "frame_id": "10", "frame_idx": 10}], "20": [{"bbox": [128, 190, 133, 195], "label": "drone", "frame_id": "20", "frame_idx": 20}], "30": [{"bbox": [128, 190, 133, 195], "label": "drone", "frame_id": "30", "frame_idx": 30}], "1": [{"bbox": [128, 190, 133, 195], "label": "drone", "frame_id": "1", "frame_idx": 1, "interpolated": true, "interpolated_from": [0, 10]}], "2": [{"bbox": [128, 190, 133, 195], "label": "drone", "frame_id": "2", "frame_idx": 2, "interpolated": true, "interpolated_from": [0, 10]}], "3": [{"bbox": [128, 190, 133, 195], "label": "drone", "frame_id": "3", "frame_idx": 3, "interpolated": true, "interpolated_from": [0, 10]}], "4": [{"bbox": [128, 190, 133, 195], "label": "drone", "frame_id": "4", "frame_idx": 4, "interpolated": true, "interpolated_from": [0, 10]}], "5": [{"bbox": [128, 190, 133, 195], "label": "drone", "frame_id": "5", "frame_idx": 5, "interpolated": true, "interpolated_from": [0, 10]}], "6": [{"bbox": [128, 190, 133, 195], "label": "drone", "frame_id": "6", "frame_idx": 6, "interpolated": true, "interpolated_from": [0, 10]}], "7": [{"bbox": [128, 190, 133, 195], "label": "drone", "frame_id": "7", "frame_idx": 7, "interpolated": true, "interpolated_from": [0, 10]}], "8": [{"bbox": [128, 190, 133, 195], "label": "drone", "frame_id": "8", "frame_idx": 8, "interpolated": true, "interpolated_from": [0, 10]}], "9": [{"bbox": [128, 190, 133, 195], "label": "drone", "frame_id": "9", "frame_idx": 9, "interpolated": true, "interpolated_from": [0, 10]}], "11": [{"bbox": [128, 190, 133, 195], "label": "drone", "frame_id": "11", "frame_idx": 11, "interpolated": true, "interpolated_from": [10, 20]}], "12": [{"bbox": [128, 190, 133, 195], "label": "drone", "frame_id": "12", "frame_idx": 12, "interpolated": true, "interpolated_from": [10, 20]}], "13": [{"bbox": [128, 190, 133, 195], "label": "drone", "frame_id": "13", "frame_idx": 13, "interpolated": true, "interpolated_from": [10, 20]}], "14": [{"bbox": [128, 190, 133, 195], "label": "drone", "frame_id": "14", "frame_idx": 14, "interpolated": true, "interpolated_from": [10, 20]}], "15": [{"bbox": [128, 190, 133, 195], "label": "drone", "frame_id": "15", "frame_idx": 15, "interpolated": true, "interpolated_from": [10, 20]}], "16": [{"bbox": [128, 190, 133, 195], "label": "drone", "frame_id": "16", "frame_idx": 16, "interpolated": true, "interpolated_from": [10, 20]}], "17": [{"bbox": [128, 190, 133, 195], "label": "drone", "frame_id": "17", "frame_idx": 17, "interpolated": true, "interpolated_from": [10, 20]}], "18": [{"bbox": [128, 190, 133, 195], "label": "drone", "frame_id": "18", "frame_idx": 18, "interpolated": true, "interpolated_from": [10, 20]}], "19": [{"bbox": [128, 190, 133, 195], "label": "drone", "frame_id": "19", "frame_idx": 19, "interpolated": true, "interpolated_from": [10, 20]}], "21": [{"bbox": [128, 190, 133, 195], "label": "drone", "frame_id": "21", "frame_idx": 21, "interpolated": true, "interpolated_from": [20, 30]}], "22": [{"bbox": [128, 190, 133, 195], "label": "drone", "frame_id": "22", "frame_idx": 22, "interpolated": true, "interpolated_from": [20, 30]}], "23": [{"bbox": [128, 190, 133, 195], "label": "drone", "frame_id": "23", "frame_idx": 23, "interpolated": true, "interpolated_from": [20, 30]}], "24": [{"bbox": [128, 190, 133, 195], "label": "drone", "frame_id": "24", "frame_idx": 24, "interpolated": true, "interpolated_from": [20, 30]}], "25": [{"bbox": [128, 190, 133, 195], "label": "drone", "frame_id": "25", "frame_idx": 25, "interpolated": true, "interpolated_from": [20, 30]}], "26": [{"bbox": [128, 190, 133, 195], "label": "drone", "frame_id": "26", "frame_idx": 26, "interpolated": true, "interpolated_from": [20, 30]}], "27": [{"bbox": [128, 190, 133, 195], "label": "drone", "frame_id": "27", "frame_idx": 27, "interpolated": true, "interpolated_from": [20, 30]}], "28": [{"bbox": [128, 190, 133, 195], "label": "drone", "frame_id": "28", "frame_idx": 28, "interpolated": true, "interpolated_from": [20, 30]}], "29": [{"bbox": [128, 190, 133, 195], "label": "drone", "frame_id": "29", "frame_idx": 29, "interpolated": true, "interpolated_from": [20, 30]}], "31": [{"bbox": [128, 190, 133, 195], "label": "drone", "frame_id": "31", "frame_idx": 31, "interpolated": true, "interpolated_from": [30]}], "32": [{"bbox": [128, 190, 133, 195], "label": "drone", "frame_id": "32", "frame_idx": 32, "interpolated": true, "interpolated_from": [30]}], "33": [{"bbox": [128, 190, 133, 195], "label": "drone", "frame_id": "33", "frame_idx": 33, "interpolated": true, "interpolated_from": [30]}], "34": [{"bbox": [128, 190, 133, 195], "label": "drone", "frame_id": "34", "frame_idx": 34, "interpolated": true, "interpolated_from": [30]}], "35": [{"bbox": [128, 190, 133, 195], "label": "drone", "frame_id": "35", "frame_idx": 35, "interpolated": true, "interpolated_from": [30]}], "36": [{"bbox": [128, 190, 133, 195], "label": "drone", "frame_id": "36", "frame_idx": 36, "interpolated": true, "interpolated_from": [30]}], "37": [{"bbox": [128, 190, 133, 195], "label": "drone", "frame_id": "37", "frame_idx": 37, "interpolated": true, "interpolated_from": [30]}], "38": [{"bbox": [128, 190, 133, 195], "label": "drone", "frame_id": "38", "frame_idx": 38, "interpolated": true, "interpolated_from": [30]}], "39": [{"bbox": [128, 190, 133, 195], "label": "drone", "frame_id": "39", "frame_idx": 39, "interpolated": true, "interpolated_from": [30]}], "40": [{"bbox": [128, 190, 133, 195], "label": "drone", "frame_id": "40", "frame_idx": 40, "interpolated": true, "interpolated_from": [30]}], "41": [{"bbox": [128, 190, 133, 195], "label": "drone", "frame_id": "41", "frame_idx": 41, "interpolated": true, "interpolated_from": [30]}], "42": [{"bbox": [128, 190, 133, 195], "label": "drone", "frame_id": "42", "frame_idx": 42, "interpolated": true, "interpolated_from": [30]}], "43": [{"bbox": [128, 190, 133, 195], "label": "drone", "frame_id": "43", "frame_idx": 43, "interpolated": true, "interpolated_from": [30]}], "44": [{"bbox": [128, 190, 133, 195], "label": "drone", "frame_id": "44", "frame_idx": 44, "interpolated": true, "interpolated_from": [30]}], "45": [{"bbox": [128, 190, 133, 195], "label": "drone", "frame_id": "45", "frame_idx": 45, "interpolated": true, "interpolated_from": [30]}], "46": [{"bbox": [128, 190, 133, 195], "label": "drone", "frame_id": "46", "frame_idx": 46, "interpolated": true, "interpolated_from": [30]}], "47": [{"bbox": [128, 190, 133, 195], "label": "drone", "frame_id": "47", "frame_idx": 47, "interpolated": true, "interpolated_from": [30]}], "48": [{"bbox": [128, 190, 133, 195], "label": "drone", "frame_id": "48", "frame_idx": 48, "interpolated": true, "interpolated_from": [30]}], "49": [{"bbox": [128, 190, 133, 195], "label": "drone", "frame_id": "49", "frame_idx": 49, "interpolated": true, "interpolated_from": [30]}]}}, "data19": {"sequence_id": "data19", "total_frames": 75, "key_frames": 8, "total_detections": 75, "direct_detections": 8, "interpolated_detections": 67, "metrics": {"precision": 0.0, "recall": 0, "f1": 0, "tp": 0, "fp": 75, "fn": 0}, "detections": {"0": [{"bbox": [178, 170, 184, 176], "label": "drone", "frame_id": "0", "frame_idx": 0}], "10": [{"bbox": [178, 170, 184, 176], "label": "drone", "frame_id": "10", "frame_idx": 10}], "20": [{"bbox": [178, 170, 184, 176], "label": "drone", "frame_id": "20", "frame_idx": 20}], "30": [{"bbox": [178, 170, 184, 176], "label": "drone", "frame_id": "30", "frame_idx": 30}], "40": [{"bbox": [168, 172, 184, 188], "label": "drone", "frame_id": "40", "frame_idx": 40}], "50": [{"bbox": [168, 172, 184, 188], "label": "drone", "frame_id": "50", "frame_idx": 50}], "60": [{"bbox": [168, 172, 184, 188], "label": "drone", "frame_id": "60", "frame_idx": 60}], "70": [{"bbox": [168, 172, 184, 188], "label": "drone", "frame_id": "70", "frame_idx": 70}], "1": [{"bbox": [178, 170, 184, 176], "label": "drone", "frame_id": "1", "frame_idx": 1, "interpolated": true, "interpolated_from": [0, 10]}], "2": [{"bbox": [178, 170, 184, 176], "label": "drone", "frame_id": "2", "frame_idx": 2, "interpolated": true, "interpolated_from": [0, 10]}], "3": [{"bbox": [178, 170, 184, 176], "label": "drone", "frame_id": "3", "frame_idx": 3, "interpolated": true, "interpolated_from": [0, 10]}], "4": [{"bbox": [178, 170, 184, 176], "label": "drone", "frame_id": "4", "frame_idx": 4, "interpolated": true, "interpolated_from": [0, 10]}], "5": [{"bbox": [178, 170, 184, 176], "label": "drone", "frame_id": "5", "frame_idx": 5, "interpolated": true, "interpolated_from": [0, 10]}], "6": [{"bbox": [178, 170, 184, 176], "label": "drone", "frame_id": "6", "frame_idx": 6, "interpolated": true, "interpolated_from": [0, 10]}], "7": [{"bbox": [178, 170, 184, 176], "label": "drone", "frame_id": "7", "frame_idx": 7, "interpolated": true, "interpolated_from": [0, 10]}], "8": [{"bbox": [178, 170, 184, 176], "label": "drone", "frame_id": "8", "frame_idx": 8, "interpolated": true, "interpolated_from": [0, 10]}], "9": [{"bbox": [178, 170, 184, 176], "label": "drone", "frame_id": "9", "frame_idx": 9, "interpolated": true, "interpolated_from": [0, 10]}], "11": [{"bbox": [178, 170, 184, 176], "label": "drone", "frame_id": "11", "frame_idx": 11, "interpolated": true, "interpolated_from": [10, 20]}], "12": [{"bbox": [178, 170, 184, 176], "label": "drone", "frame_id": "12", "frame_idx": 12, "interpolated": true, "interpolated_from": [10, 20]}], "13": [{"bbox": [178, 170, 184, 176], "label": "drone", "frame_id": "13", "frame_idx": 13, "interpolated": true, "interpolated_from": [10, 20]}], "14": [{"bbox": [178, 170, 184, 176], "label": "drone", "frame_id": "14", "frame_idx": 14, "interpolated": true, "interpolated_from": [10, 20]}], "15": [{"bbox": [178, 170, 184, 176], "label": "drone", "frame_id": "15", "frame_idx": 15, "interpolated": true, "interpolated_from": [10, 20]}], "16": [{"bbox": [178, 170, 184, 176], "label": "drone", "frame_id": "16", "frame_idx": 16, "interpolated": true, "interpolated_from": [10, 20]}], "17": [{"bbox": [178, 170, 184, 176], "label": "drone", "frame_id": "17", "frame_idx": 17, "interpolated": true, "interpolated_from": [10, 20]}], "18": [{"bbox": [178, 170, 184, 176], "label": "drone", "frame_id": "18", "frame_idx": 18, "interpolated": true, "interpolated_from": [10, 20]}], "19": [{"bbox": [178, 170, 184, 176], "label": "drone", "frame_id": "19", "frame_idx": 19, "interpolated": true, "interpolated_from": [10, 20]}], "21": [{"bbox": [178, 170, 184, 176], "label": "drone", "frame_id": "21", "frame_idx": 21, "interpolated": true, "interpolated_from": [20, 30]}], "22": [{"bbox": [178, 170, 184, 176], "label": "drone", "frame_id": "22", "frame_idx": 22, "interpolated": true, "interpolated_from": [20, 30]}], "23": [{"bbox": [178, 170, 184, 176], "label": "drone", "frame_id": "23", "frame_idx": 23, "interpolated": true, "interpolated_from": [20, 30]}], "24": [{"bbox": [178, 170, 184, 176], "label": "drone", "frame_id": "24", "frame_idx": 24, "interpolated": true, "interpolated_from": [20, 30]}], "25": [{"bbox": [178, 170, 184, 176], "label": "drone", "frame_id": "25", "frame_idx": 25, "interpolated": true, "interpolated_from": [20, 30]}], "26": [{"bbox": [178, 170, 184, 176], "label": "drone", "frame_id": "26", "frame_idx": 26, "interpolated": true, "interpolated_from": [20, 30]}], "27": [{"bbox": [178, 170, 184, 176], "label": "drone", "frame_id": "27", "frame_idx": 27, "interpolated": true, "interpolated_from": [20, 30]}], "28": [{"bbox": [178, 170, 184, 176], "label": "drone", "frame_id": "28", "frame_idx": 28, "interpolated": true, "interpolated_from": [20, 30]}], "29": [{"bbox": [178, 170, 184, 176], "label": "drone", "frame_id": "29", "frame_idx": 29, "interpolated": true, "interpolated_from": [20, 30]}], "31": [{"bbox": [177, 170, 184, 177], "label": "drone", "frame_id": "31", "frame_idx": 31, "interpolated": true, "interpolated_from": [30, 40]}], "32": [{"bbox": [176, 170, 184, 178], "label": "drone", "frame_id": "32", "frame_idx": 32, "interpolated": true, "interpolated_from": [30, 40]}], "33": [{"bbox": [175, 170, 184, 179], "label": "drone", "frame_id": "33", "frame_idx": 33, "interpolated": true, "interpolated_from": [30, 40]}], "34": [{"bbox": [174, 170, 184, 180], "label": "drone", "frame_id": "34", "frame_idx": 34, "interpolated": true, "interpolated_from": [30, 40]}], "35": [{"bbox": [173, 171, 184, 182], "label": "drone", "frame_id": "35", "frame_idx": 35, "interpolated": true, "interpolated_from": [30, 40]}], "36": [{"bbox": [172, 171, 184, 183], "label": "drone", "frame_id": "36", "frame_idx": 36, "interpolated": true, "interpolated_from": [30, 40]}], "37": [{"bbox": [171, 171, 184, 184], "label": "drone", "frame_id": "37", "frame_idx": 37, "interpolated": true, "interpolated_from": [30, 40]}], "38": [{"bbox": [170, 171, 184, 185], "label": "drone", "frame_id": "38", "frame_idx": 38, "interpolated": true, "interpolated_from": [30, 40]}], "39": [{"bbox": [169, 171, 184, 186], "label": "drone", "frame_id": "39", "frame_idx": 39, "interpolated": true, "interpolated_from": [30, 40]}], "41": [{"bbox": [168, 172, 184, 188], "label": "drone", "frame_id": "41", "frame_idx": 41, "interpolated": true, "interpolated_from": [40, 50]}], "42": [{"bbox": [168, 172, 184, 188], "label": "drone", "frame_id": "42", "frame_idx": 42, "interpolated": true, "interpolated_from": [40, 50]}], "43": [{"bbox": [168, 172, 184, 188], "label": "drone", "frame_id": "43", "frame_idx": 43, "interpolated": true, "interpolated_from": [40, 50]}], "44": [{"bbox": [168, 172, 184, 188], "label": "drone", "frame_id": "44", "frame_idx": 44, "interpolated": true, "interpolated_from": [40, 50]}], "45": [{"bbox": [168, 172, 184, 188], "label": "drone", "frame_id": "45", "frame_idx": 45, "interpolated": true, "interpolated_from": [40, 50]}], "46": [{"bbox": [168, 172, 184, 188], "label": "drone", "frame_id": "46", "frame_idx": 46, "interpolated": true, "interpolated_from": [40, 50]}], "47": [{"bbox": [168, 172, 184, 188], "label": "drone", "frame_id": "47", "frame_idx": 47, "interpolated": true, "interpolated_from": [40, 50]}], "48": [{"bbox": [168, 172, 184, 188], "label": "drone", "frame_id": "48", "frame_idx": 48, "interpolated": true, "interpolated_from": [40, 50]}], "49": [{"bbox": [168, 172, 184, 188], "label": "drone", "frame_id": "49", "frame_idx": 49, "interpolated": true, "interpolated_from": [40, 50]}], "51": [{"bbox": [168, 172, 184, 188], "label": "drone", "frame_id": "51", "frame_idx": 51, "interpolated": true, "interpolated_from": [50, 60]}], "52": [{"bbox": [168, 172, 184, 188], "label": "drone", "frame_id": "52", "frame_idx": 52, "interpolated": true, "interpolated_from": [50, 60]}], "53": [{"bbox": [168, 172, 184, 188], "label": "drone", "frame_id": "53", "frame_idx": 53, "interpolated": true, "interpolated_from": [50, 60]}], "54": [{"bbox": [168, 172, 184, 188], "label": "drone", "frame_id": "54", "frame_idx": 54, "interpolated": true, "interpolated_from": [50, 60]}], "55": [{"bbox": [168, 172, 184, 188], "label": "drone", "frame_id": "55", "frame_idx": 55, "interpolated": true, "interpolated_from": [50, 60]}], "56": [{"bbox": [168, 172, 184, 188], "label": "drone", "frame_id": "56", "frame_idx": 56, "interpolated": true, "interpolated_from": [50, 60]}], "57": [{"bbox": [168, 172, 184, 188], "label": "drone", "frame_id": "57", "frame_idx": 57, "interpolated": true, "interpolated_from": [50, 60]}], "58": [{"bbox": [168, 172, 184, 188], "label": "drone", "frame_id": "58", "frame_idx": 58, "interpolated": true, "interpolated_from": [50, 60]}], "59": [{"bbox": [168, 172, 184, 188], "label": "drone", "frame_id": "59", "frame_idx": 59, "interpolated": true, "interpolated_from": [50, 60]}], "61": [{"bbox": [168, 172, 184, 188], "label": "drone", "frame_id": "61", "frame_idx": 61, "interpolated": true, "interpolated_from": [60, 70]}], "62": [{"bbox": [168, 172, 184, 188], "label": "drone", "frame_id": "62", "frame_idx": 62, "interpolated": true, "interpolated_from": [60, 70]}], "63": [{"bbox": [168, 172, 184, 188], "label": "drone", "frame_id": "63", "frame_idx": 63, "interpolated": true, "interpolated_from": [60, 70]}], "64": [{"bbox": [168, 172, 184, 188], "label": "drone", "frame_id": "64", "frame_idx": 64, "interpolated": true, "interpolated_from": [60, 70]}], "65": [{"bbox": [168, 172, 184, 188], "label": "drone", "frame_id": "65", "frame_idx": 65, "interpolated": true, "interpolated_from": [60, 70]}], "66": [{"bbox": [168, 172, 184, 188], "label": "drone", "frame_id": "66", "frame_idx": 66, "interpolated": true, "interpolated_from": [60, 70]}], "67": [{"bbox": [168, 172, 184, 188], "label": "drone", "frame_id": "67", "frame_idx": 67, "interpolated": true, "interpolated_from": [60, 70]}], "68": [{"bbox": [168, 172, 184, 188], "label": "drone", "frame_id": "68", "frame_idx": 68, "interpolated": true, "interpolated_from": [60, 70]}], "69": [{"bbox": [168, 172, 184, 188], "label": "drone", "frame_id": "69", "frame_idx": 69, "interpolated": true, "interpolated_from": [60, 70]}], "71": [{"bbox": [168, 172, 184, 188], "label": "drone", "frame_id": "71", "frame_idx": 71, "interpolated": true, "interpolated_from": [70]}], "72": [{"bbox": [168, 172, 184, 188], "label": "drone", "frame_id": "72", "frame_idx": 72, "interpolated": true, "interpolated_from": [70]}], "73": [{"bbox": [168, 172, 184, 188], "label": "drone", "frame_id": "73", "frame_idx": 73, "interpolated": true, "interpolated_from": [70]}], "74": [{"bbox": [168, 172, 184, 188], "label": "drone", "frame_id": "74", "frame_idx": 74, "interpolated": true, "interpolated_from": [70]}]}}, "data23": {"sequence_id": "data23", "total_frames": 50, "key_frames": 5, "total_detections": 50, "direct_detections": 4, "interpolated_detections": 46, "metrics": {"precision": 0.0, "recall": 0, "f1": 0, "tp": 0, "fp": 50, "fn": 0}, "detections": {"0": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "0", "frame_idx": 0}], "10": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "10", "frame_idx": 10}], "20": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "20", "frame_idx": 20}], "30": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "30", "frame_idx": 30}], "1": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "1", "frame_idx": 1, "interpolated": true, "interpolated_from": [0, 10]}], "2": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "2", "frame_idx": 2, "interpolated": true, "interpolated_from": [0, 10]}], "3": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "3", "frame_idx": 3, "interpolated": true, "interpolated_from": [0, 10]}], "4": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "4", "frame_idx": 4, "interpolated": true, "interpolated_from": [0, 10]}], "5": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "5", "frame_idx": 5, "interpolated": true, "interpolated_from": [0, 10]}], "6": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "6", "frame_idx": 6, "interpolated": true, "interpolated_from": [0, 10]}], "7": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "7", "frame_idx": 7, "interpolated": true, "interpolated_from": [0, 10]}], "8": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "8", "frame_idx": 8, "interpolated": true, "interpolated_from": [0, 10]}], "9": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "9", "frame_idx": 9, "interpolated": true, "interpolated_from": [0, 10]}], "11": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "11", "frame_idx": 11, "interpolated": true, "interpolated_from": [10, 20]}], "12": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "12", "frame_idx": 12, "interpolated": true, "interpolated_from": [10, 20]}], "13": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "13", "frame_idx": 13, "interpolated": true, "interpolated_from": [10, 20]}], "14": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "14", "frame_idx": 14, "interpolated": true, "interpolated_from": [10, 20]}], "15": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "15", "frame_idx": 15, "interpolated": true, "interpolated_from": [10, 20]}], "16": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "16", "frame_idx": 16, "interpolated": true, "interpolated_from": [10, 20]}], "17": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "17", "frame_idx": 17, "interpolated": true, "interpolated_from": [10, 20]}], "18": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "18", "frame_idx": 18, "interpolated": true, "interpolated_from": [10, 20]}], "19": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "19", "frame_idx": 19, "interpolated": true, "interpolated_from": [10, 20]}], "21": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "21", "frame_idx": 21, "interpolated": true, "interpolated_from": [20, 30]}], "22": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "22", "frame_idx": 22, "interpolated": true, "interpolated_from": [20, 30]}], "23": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "23", "frame_idx": 23, "interpolated": true, "interpolated_from": [20, 30]}], "24": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "24", "frame_idx": 24, "interpolated": true, "interpolated_from": [20, 30]}], "25": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "25", "frame_idx": 25, "interpolated": true, "interpolated_from": [20, 30]}], "26": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "26", "frame_idx": 26, "interpolated": true, "interpolated_from": [20, 30]}], "27": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "27", "frame_idx": 27, "interpolated": true, "interpolated_from": [20, 30]}], "28": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "28", "frame_idx": 28, "interpolated": true, "interpolated_from": [20, 30]}], "29": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "29", "frame_idx": 29, "interpolated": true, "interpolated_from": [20, 30]}], "31": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "31", "frame_idx": 31, "interpolated": true, "interpolated_from": [30]}], "32": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "32", "frame_idx": 32, "interpolated": true, "interpolated_from": [30]}], "33": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "33", "frame_idx": 33, "interpolated": true, "interpolated_from": [30]}], "34": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "34", "frame_idx": 34, "interpolated": true, "interpolated_from": [30]}], "35": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "35", "frame_idx": 35, "interpolated": true, "interpolated_from": [30]}], "36": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "36", "frame_idx": 36, "interpolated": true, "interpolated_from": [30]}], "37": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "37", "frame_idx": 37, "interpolated": true, "interpolated_from": [30]}], "38": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "38", "frame_idx": 38, "interpolated": true, "interpolated_from": [30]}], "39": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "39", "frame_idx": 39, "interpolated": true, "interpolated_from": [30]}], "40": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "40", "frame_idx": 40, "interpolated": true, "interpolated_from": [30]}], "41": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "41", "frame_idx": 41, "interpolated": true, "interpolated_from": [30]}], "42": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "42", "frame_idx": 42, "interpolated": true, "interpolated_from": [30]}], "43": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "43", "frame_idx": 43, "interpolated": true, "interpolated_from": [30]}], "44": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "44", "frame_idx": 44, "interpolated": true, "interpolated_from": [30]}], "45": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "45", "frame_idx": 45, "interpolated": true, "interpolated_from": [30]}], "46": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "46", "frame_idx": 46, "interpolated": true, "interpolated_from": [30]}], "47": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "47", "frame_idx": 47, "interpolated": true, "interpolated_from": [30]}], "48": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "48", "frame_idx": 48, "interpolated": true, "interpolated_from": [30]}], "49": [{"bbox": [198, 178, 203, 183], "label": "drone", "frame_id": "49", "frame_idx": 49, "interpolated": true, "interpolated_from": [30]}]}}, "data26": {"sequence_id": "data26", "total_frames": 75, "key_frames": 8, "total_detections": 0, "direct_detections": 0, "interpolated_detections": 0, "metrics": {"precision": 0, "recall": 0, "f1": 0, "tp": 0, "fp": 0, "fn": 0}, "detections": {"0": [], "1": [], "2": [], "3": [], "4": [], "5": [], "6": [], "7": [], "8": [], "9": [], "10": [], "11": [], "12": [], "13": [], "14": [], "15": [], "16": [], "17": [], "18": [], "19": [], "20": [], "21": [], "22": [], "23": [], "24": [], "25": [], "26": [], "27": [], "28": [], "29": [], "30": [], "31": [], "32": [], "33": [], "34": [], "35": [], "36": [], "37": [], "38": [], "39": [], "40": [], "41": [], "42": [], "43": [], "44": [], "45": [], "46": [], "47": [], "48": [], "49": [], "50": [], "51": [], "52": [], "53": [], "54": [], "55": [], "56": [], "57": [], "58": [], "59": [], "60": [], "61": [], "62": [], "63": [], "64": [], "65": [], "66": [], "67": [], "68": [], "69": [], "70": [], "71": [], "72": [], "73": [], "74": []}}}