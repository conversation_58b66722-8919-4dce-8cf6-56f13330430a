{"data01": {"sequence_id": "data01", "total_frames": 75, "key_frames": 8, "total_detections": 75, "direct_detections": 8, "interpolated_detections": 67, "metrics": {"precision": 0.0, "recall": 0.0, "f1": 0, "tp": 0, "fp": 75, "fn": 75}, "detections": {"0": [{"bbox": [156, 78, 292, 100], "label": "drone", "frame_id": "0", "frame_idx": 0}], "10": [{"bbox": [156, 78, 292, 100], "label": "drone", "frame_id": "10", "frame_idx": 10}], "20": [{"bbox": [156, 78, 292, 100], "label": "drone", "frame_id": "20", "frame_idx": 20}], "30": [{"bbox": [156, 78, 292, 100], "label": "drone", "frame_id": "30", "frame_idx": 30}], "40": [{"bbox": [200, 100, 220, 120], "label": "drone", "frame_id": "40", "frame_idx": 40}], "50": [{"bbox": [180, 120, 200, 140], "label": "car", "frame_id": "50", "frame_idx": 50}], "60": [{"bbox": [160, 140, 180, 160], "label": "bus", "frame_id": "60", "frame_idx": 60}], "70": [{"bbox": [140, 160, 160, 180], "label": "pedestrian", "frame_id": "70", "frame_idx": 70}], "1": [{"bbox": [156, 78, 292, 100], "label": "drone", "frame_id": "1", "frame_idx": 1, "interpolated": true, "interpolated_from": [0, 10]}], "2": [{"bbox": [156, 78, 292, 100], "label": "drone", "frame_id": "2", "frame_idx": 2, "interpolated": true, "interpolated_from": [0, 10]}], "3": [{"bbox": [156, 78, 292, 100], "label": "drone", "frame_id": "3", "frame_idx": 3, "interpolated": true, "interpolated_from": [0, 10]}], "4": [{"bbox": [156, 78, 292, 100], "label": "drone", "frame_id": "4", "frame_idx": 4, "interpolated": true, "interpolated_from": [0, 10]}], "5": [{"bbox": [156, 78, 292, 100], "label": "drone", "frame_id": "5", "frame_idx": 5, "interpolated": true, "interpolated_from": [0, 10]}], "6": [{"bbox": [156, 78, 292, 100], "label": "drone", "frame_id": "6", "frame_idx": 6, "interpolated": true, "interpolated_from": [0, 10]}], "7": [{"bbox": [156, 78, 292, 100], "label": "drone", "frame_id": "7", "frame_idx": 7, "interpolated": true, "interpolated_from": [0, 10]}], "8": [{"bbox": [156, 78, 292, 100], "label": "drone", "frame_id": "8", "frame_idx": 8, "interpolated": true, "interpolated_from": [0, 10]}], "9": [{"bbox": [156, 78, 292, 100], "label": "drone", "frame_id": "9", "frame_idx": 9, "interpolated": true, "interpolated_from": [0, 10]}], "11": [{"bbox": [156, 78, 292, 100], "label": "drone", "frame_id": "11", "frame_idx": 11, "interpolated": true, "interpolated_from": [10, 20]}], "12": [{"bbox": [156, 78, 292, 100], "label": "drone", "frame_id": "12", "frame_idx": 12, "interpolated": true, "interpolated_from": [10, 20]}], "13": [{"bbox": [156, 78, 292, 100], "label": "drone", "frame_id": "13", "frame_idx": 13, "interpolated": true, "interpolated_from": [10, 20]}], "14": [{"bbox": [156, 78, 292, 100], "label": "drone", "frame_id": "14", "frame_idx": 14, "interpolated": true, "interpolated_from": [10, 20]}], "15": [{"bbox": [156, 78, 292, 100], "label": "drone", "frame_id": "15", "frame_idx": 15, "interpolated": true, "interpolated_from": [10, 20]}], "16": [{"bbox": [156, 78, 292, 100], "label": "drone", "frame_id": "16", "frame_idx": 16, "interpolated": true, "interpolated_from": [10, 20]}], "17": [{"bbox": [156, 78, 292, 100], "label": "drone", "frame_id": "17", "frame_idx": 17, "interpolated": true, "interpolated_from": [10, 20]}], "18": [{"bbox": [156, 78, 292, 100], "label": "drone", "frame_id": "18", "frame_idx": 18, "interpolated": true, "interpolated_from": [10, 20]}], "19": [{"bbox": [156, 78, 292, 100], "label": "drone", "frame_id": "19", "frame_idx": 19, "interpolated": true, "interpolated_from": [10, 20]}], "21": [{"bbox": [156, 78, 292, 100], "label": "drone", "frame_id": "21", "frame_idx": 21, "interpolated": true, "interpolated_from": [20, 30]}], "22": [{"bbox": [156, 78, 292, 100], "label": "drone", "frame_id": "22", "frame_idx": 22, "interpolated": true, "interpolated_from": [20, 30]}], "23": [{"bbox": [156, 78, 292, 100], "label": "drone", "frame_id": "23", "frame_idx": 23, "interpolated": true, "interpolated_from": [20, 30]}], "24": [{"bbox": [156, 78, 292, 100], "label": "drone", "frame_id": "24", "frame_idx": 24, "interpolated": true, "interpolated_from": [20, 30]}], "25": [{"bbox": [156, 78, 292, 100], "label": "drone", "frame_id": "25", "frame_idx": 25, "interpolated": true, "interpolated_from": [20, 30]}], "26": [{"bbox": [156, 78, 292, 100], "label": "drone", "frame_id": "26", "frame_idx": 26, "interpolated": true, "interpolated_from": [20, 30]}], "27": [{"bbox": [156, 78, 292, 100], "label": "drone", "frame_id": "27", "frame_idx": 27, "interpolated": true, "interpolated_from": [20, 30]}], "28": [{"bbox": [156, 78, 292, 100], "label": "drone", "frame_id": "28", "frame_idx": 28, "interpolated": true, "interpolated_from": [20, 30]}], "29": [{"bbox": [156, 78, 292, 100], "label": "drone", "frame_id": "29", "frame_idx": 29, "interpolated": true, "interpolated_from": [20, 30]}], "31": [{"bbox": [160, 80, 284, 102], "label": "drone", "frame_id": "31", "frame_idx": 31, "interpolated": true, "interpolated_from": [30, 40]}], "32": [{"bbox": [164, 82, 277, 104], "label": "drone", "frame_id": "32", "frame_idx": 32, "interpolated": true, "interpolated_from": [30, 40]}], "33": [{"bbox": [169, 84, 270, 106], "label": "drone", "frame_id": "33", "frame_idx": 33, "interpolated": true, "interpolated_from": [30, 40]}], "34": [{"bbox": [173, 86, 263, 108], "label": "drone", "frame_id": "34", "frame_idx": 34, "interpolated": true, "interpolated_from": [30, 40]}], "35": [{"bbox": [178, 89, 256, 110], "label": "drone", "frame_id": "35", "frame_idx": 35, "interpolated": true, "interpolated_from": [30, 40]}], "36": [{"bbox": [182, 91, 248, 112], "label": "drone", "frame_id": "36", "frame_idx": 36, "interpolated": true, "interpolated_from": [30, 40]}], "37": [{"bbox": [186, 93, 241, 114], "label": "drone", "frame_id": "37", "frame_idx": 37, "interpolated": true, "interpolated_from": [30, 40]}], "38": [{"bbox": [191, 95, 234, 116], "label": "drone", "frame_id": "38", "frame_idx": 38, "interpolated": true, "interpolated_from": [30, 40]}], "39": [{"bbox": [195, 97, 227, 118], "label": "drone", "frame_id": "39", "frame_idx": 39, "interpolated": true, "interpolated_from": [30, 40]}], "41": [{"bbox": [198, 102, 218, 122], "label": "drone", "frame_id": "41", "frame_idx": 41, "interpolated": true, "interpolated_from": [40, 50]}], "42": [{"bbox": [196, 104, 216, 124], "label": "drone", "frame_id": "42", "frame_idx": 42, "interpolated": true, "interpolated_from": [40, 50]}], "43": [{"bbox": [194, 106, 214, 126], "label": "drone", "frame_id": "43", "frame_idx": 43, "interpolated": true, "interpolated_from": [40, 50]}], "44": [{"bbox": [192, 108, 212, 128], "label": "drone", "frame_id": "44", "frame_idx": 44, "interpolated": true, "interpolated_from": [40, 50]}], "45": [{"bbox": [190, 110, 210, 130], "label": "drone", "frame_id": "45", "frame_idx": 45, "interpolated": true, "interpolated_from": [40, 50]}], "46": [{"bbox": [188, 112, 208, 132], "label": "drone", "frame_id": "46", "frame_idx": 46, "interpolated": true, "interpolated_from": [40, 50]}], "47": [{"bbox": [186, 114, 206, 134], "label": "drone", "frame_id": "47", "frame_idx": 47, "interpolated": true, "interpolated_from": [40, 50]}], "48": [{"bbox": [184, 116, 204, 136], "label": "drone", "frame_id": "48", "frame_idx": 48, "interpolated": true, "interpolated_from": [40, 50]}], "49": [{"bbox": [182, 118, 202, 138], "label": "drone", "frame_id": "49", "frame_idx": 49, "interpolated": true, "interpolated_from": [40, 50]}], "51": [{"bbox": [178, 122, 198, 142], "label": "car", "frame_id": "51", "frame_idx": 51, "interpolated": true, "interpolated_from": [50, 60]}], "52": [{"bbox": [176, 124, 196, 144], "label": "car", "frame_id": "52", "frame_idx": 52, "interpolated": true, "interpolated_from": [50, 60]}], "53": [{"bbox": [174, 126, 194, 146], "label": "car", "frame_id": "53", "frame_idx": 53, "interpolated": true, "interpolated_from": [50, 60]}], "54": [{"bbox": [172, 128, 192, 148], "label": "car", "frame_id": "54", "frame_idx": 54, "interpolated": true, "interpolated_from": [50, 60]}], "55": [{"bbox": [170, 130, 190, 150], "label": "car", "frame_id": "55", "frame_idx": 55, "interpolated": true, "interpolated_from": [50, 60]}], "56": [{"bbox": [168, 132, 188, 152], "label": "car", "frame_id": "56", "frame_idx": 56, "interpolated": true, "interpolated_from": [50, 60]}], "57": [{"bbox": [166, 134, 186, 154], "label": "car", "frame_id": "57", "frame_idx": 57, "interpolated": true, "interpolated_from": [50, 60]}], "58": [{"bbox": [164, 136, 184, 156], "label": "car", "frame_id": "58", "frame_idx": 58, "interpolated": true, "interpolated_from": [50, 60]}], "59": [{"bbox": [162, 138, 182, 158], "label": "car", "frame_id": "59", "frame_idx": 59, "interpolated": true, "interpolated_from": [50, 60]}], "61": [{"bbox": [158, 142, 178, 162], "label": "bus", "frame_id": "61", "frame_idx": 61, "interpolated": true, "interpolated_from": [60, 70]}], "62": [{"bbox": [156, 144, 176, 164], "label": "bus", "frame_id": "62", "frame_idx": 62, "interpolated": true, "interpolated_from": [60, 70]}], "63": [{"bbox": [154, 146, 174, 166], "label": "bus", "frame_id": "63", "frame_idx": 63, "interpolated": true, "interpolated_from": [60, 70]}], "64": [{"bbox": [152, 148, 172, 168], "label": "bus", "frame_id": "64", "frame_idx": 64, "interpolated": true, "interpolated_from": [60, 70]}], "65": [{"bbox": [150, 150, 170, 170], "label": "bus", "frame_id": "65", "frame_idx": 65, "interpolated": true, "interpolated_from": [60, 70]}], "66": [{"bbox": [148, 152, 168, 172], "label": "bus", "frame_id": "66", "frame_idx": 66, "interpolated": true, "interpolated_from": [60, 70]}], "67": [{"bbox": [146, 154, 166, 174], "label": "bus", "frame_id": "67", "frame_idx": 67, "interpolated": true, "interpolated_from": [60, 70]}], "68": [{"bbox": [144, 156, 164, 176], "label": "bus", "frame_id": "68", "frame_idx": 68, "interpolated": true, "interpolated_from": [60, 70]}], "69": [{"bbox": [142, 158, 162, 178], "label": "bus", "frame_id": "69", "frame_idx": 69, "interpolated": true, "interpolated_from": [60, 70]}], "71": [{"bbox": [140, 160, 160, 180], "label": "pedestrian", "frame_id": "71", "frame_idx": 71, "interpolated": true, "interpolated_from": [70]}], "72": [{"bbox": [140, 160, 160, 180], "label": "pedestrian", "frame_id": "72", "frame_idx": 72, "interpolated": true, "interpolated_from": [70]}], "73": [{"bbox": [140, 160, 160, 180], "label": "pedestrian", "frame_id": "73", "frame_idx": 73, "interpolated": true, "interpolated_from": [70]}], "74": [{"bbox": [140, 160, 160, 180], "label": "pedestrian", "frame_id": "74", "frame_idx": 74, "interpolated": true, "interpolated_from": [70]}]}}, "data02": {"sequence_id": "data02", "total_frames": 50, "key_frames": 5, "total_detections": 50, "direct_detections": 4, "interpolated_detections": 46, "metrics": {"precision": 0.0, "recall": 0.0, "f1": 0, "tp": 0, "fp": 50, "fn": 50}, "detections": {"0": [{"bbox": [150, 168, 179, 196], "label": "drone", "frame_id": "0", "frame_idx": 0}], "10": [{"bbox": [150, 168, 179, 196], "label": "drone", "frame_id": "10", "frame_idx": 10}], "20": [{"bbox": [150, 168, 179, 196], "label": "drone", "frame_id": "20", "frame_idx": 20}], "30": [{"bbox": [150, 168, 179, 196], "label": "drone", "frame_id": "30", "frame_idx": 30}], "1": [{"bbox": [150, 168, 179, 196], "label": "drone", "frame_id": "1", "frame_idx": 1, "interpolated": true, "interpolated_from": [0, 10]}], "2": [{"bbox": [150, 168, 179, 196], "label": "drone", "frame_id": "2", "frame_idx": 2, "interpolated": true, "interpolated_from": [0, 10]}], "3": [{"bbox": [150, 168, 179, 196], "label": "drone", "frame_id": "3", "frame_idx": 3, "interpolated": true, "interpolated_from": [0, 10]}], "4": [{"bbox": [150, 168, 179, 196], "label": "drone", "frame_id": "4", "frame_idx": 4, "interpolated": true, "interpolated_from": [0, 10]}], "5": [{"bbox": [150, 168, 179, 196], "label": "drone", "frame_id": "5", "frame_idx": 5, "interpolated": true, "interpolated_from": [0, 10]}], "6": [{"bbox": [150, 168, 179, 196], "label": "drone", "frame_id": "6", "frame_idx": 6, "interpolated": true, "interpolated_from": [0, 10]}], "7": [{"bbox": [150, 168, 179, 196], "label": "drone", "frame_id": "7", "frame_idx": 7, "interpolated": true, "interpolated_from": [0, 10]}], "8": [{"bbox": [150, 168, 179, 196], "label": "drone", "frame_id": "8", "frame_idx": 8, "interpolated": true, "interpolated_from": [0, 10]}], "9": [{"bbox": [150, 168, 179, 196], "label": "drone", "frame_id": "9", "frame_idx": 9, "interpolated": true, "interpolated_from": [0, 10]}], "11": [{"bbox": [150, 168, 179, 196], "label": "drone", "frame_id": "11", "frame_idx": 11, "interpolated": true, "interpolated_from": [10, 20]}], "12": [{"bbox": [150, 168, 179, 196], "label": "drone", "frame_id": "12", "frame_idx": 12, "interpolated": true, "interpolated_from": [10, 20]}], "13": [{"bbox": [150, 168, 179, 196], "label": "drone", "frame_id": "13", "frame_idx": 13, "interpolated": true, "interpolated_from": [10, 20]}], "14": [{"bbox": [150, 168, 179, 196], "label": "drone", "frame_id": "14", "frame_idx": 14, "interpolated": true, "interpolated_from": [10, 20]}], "15": [{"bbox": [150, 168, 179, 196], "label": "drone", "frame_id": "15", "frame_idx": 15, "interpolated": true, "interpolated_from": [10, 20]}], "16": [{"bbox": [150, 168, 179, 196], "label": "drone", "frame_id": "16", "frame_idx": 16, "interpolated": true, "interpolated_from": [10, 20]}], "17": [{"bbox": [150, 168, 179, 196], "label": "drone", "frame_id": "17", "frame_idx": 17, "interpolated": true, "interpolated_from": [10, 20]}], "18": [{"bbox": [150, 168, 179, 196], "label": "drone", "frame_id": "18", "frame_idx": 18, "interpolated": true, "interpolated_from": [10, 20]}], "19": [{"bbox": [150, 168, 179, 196], "label": "drone", "frame_id": "19", "frame_idx": 19, "interpolated": true, "interpolated_from": [10, 20]}], "21": [{"bbox": [150, 168, 179, 196], "label": "drone", "frame_id": "21", "frame_idx": 21, "interpolated": true, "interpolated_from": [20, 30]}], "22": [{"bbox": [150, 168, 179, 196], "label": "drone", "frame_id": "22", "frame_idx": 22, "interpolated": true, "interpolated_from": [20, 30]}], "23": [{"bbox": [150, 168, 179, 196], "label": "drone", "frame_id": "23", "frame_idx": 23, "interpolated": true, "interpolated_from": [20, 30]}], "24": [{"bbox": [150, 168, 179, 196], "label": "drone", "frame_id": "24", "frame_idx": 24, "interpolated": true, "interpolated_from": [20, 30]}], "25": [{"bbox": [150, 168, 179, 196], "label": "drone", "frame_id": "25", "frame_idx": 25, "interpolated": true, "interpolated_from": [20, 30]}], "26": [{"bbox": [150, 168, 179, 196], "label": "drone", "frame_id": "26", "frame_idx": 26, "interpolated": true, "interpolated_from": [20, 30]}], "27": [{"bbox": [150, 168, 179, 196], "label": "drone", "frame_id": "27", "frame_idx": 27, "interpolated": true, "interpolated_from": [20, 30]}], "28": [{"bbox": [150, 168, 179, 196], "label": "drone", "frame_id": "28", "frame_idx": 28, "interpolated": true, "interpolated_from": [20, 30]}], "29": [{"bbox": [150, 168, 179, 196], "label": "drone", "frame_id": "29", "frame_idx": 29, "interpolated": true, "interpolated_from": [20, 30]}], "31": [{"bbox": [150, 168, 179, 196], "label": "drone", "frame_id": "31", "frame_idx": 31, "interpolated": true, "interpolated_from": [30]}], "32": [{"bbox": [150, 168, 179, 196], "label": "drone", "frame_id": "32", "frame_idx": 32, "interpolated": true, "interpolated_from": [30]}], "33": [{"bbox": [150, 168, 179, 196], "label": "drone", "frame_id": "33", "frame_idx": 33, "interpolated": true, "interpolated_from": [30]}], "34": [{"bbox": [150, 168, 179, 196], "label": "drone", "frame_id": "34", "frame_idx": 34, "interpolated": true, "interpolated_from": [30]}], "35": [{"bbox": [150, 168, 179, 196], "label": "drone", "frame_id": "35", "frame_idx": 35, "interpolated": true, "interpolated_from": [30]}], "36": [{"bbox": [150, 168, 179, 196], "label": "drone", "frame_id": "36", "frame_idx": 36, "interpolated": true, "interpolated_from": [30]}], "37": [{"bbox": [150, 168, 179, 196], "label": "drone", "frame_id": "37", "frame_idx": 37, "interpolated": true, "interpolated_from": [30]}], "38": [{"bbox": [150, 168, 179, 196], "label": "drone", "frame_id": "38", "frame_idx": 38, "interpolated": true, "interpolated_from": [30]}], "39": [{"bbox": [150, 168, 179, 196], "label": "drone", "frame_id": "39", "frame_idx": 39, "interpolated": true, "interpolated_from": [30]}], "40": [{"bbox": [150, 168, 179, 196], "label": "drone", "frame_id": "40", "frame_idx": 40, "interpolated": true, "interpolated_from": [30]}], "41": [{"bbox": [150, 168, 179, 196], "label": "drone", "frame_id": "41", "frame_idx": 41, "interpolated": true, "interpolated_from": [30]}], "42": [{"bbox": [150, 168, 179, 196], "label": "drone", "frame_id": "42", "frame_idx": 42, "interpolated": true, "interpolated_from": [30]}], "43": [{"bbox": [150, 168, 179, 196], "label": "drone", "frame_id": "43", "frame_idx": 43, "interpolated": true, "interpolated_from": [30]}], "44": [{"bbox": [150, 168, 179, 196], "label": "drone", "frame_id": "44", "frame_idx": 44, "interpolated": true, "interpolated_from": [30]}], "45": [{"bbox": [150, 168, 179, 196], "label": "drone", "frame_id": "45", "frame_idx": 45, "interpolated": true, "interpolated_from": [30]}], "46": [{"bbox": [150, 168, 179, 196], "label": "drone", "frame_id": "46", "frame_idx": 46, "interpolated": true, "interpolated_from": [30]}], "47": [{"bbox": [150, 168, 179, 196], "label": "drone", "frame_id": "47", "frame_idx": 47, "interpolated": true, "interpolated_from": [30]}], "48": [{"bbox": [150, 168, 179, 196], "label": "drone", "frame_id": "48", "frame_idx": 48, "interpolated": true, "interpolated_from": [30]}], "49": [{"bbox": [150, 168, 179, 196], "label": "drone", "frame_id": "49", "frame_idx": 49, "interpolated": true, "interpolated_from": [30]}]}}, "data04": {"sequence_id": "data04", "total_frames": 50, "key_frames": 5, "total_detections": 50, "direct_detections": 4, "interpolated_detections": 46, "metrics": {"precision": 0.0, "recall": 0.0, "f1": 0, "tp": 0, "fp": 50, "fn": 50}, "detections": {"0": [{"bbox": [156, 108, 167, 119], "label": "drone", "frame_id": "0", "frame_idx": 0}], "10": [{"bbox": [156, 108, 167, 119], "label": "drone", "frame_id": "10", "frame_idx": 10}], "20": [{"bbox": [156, 108, 167, 119], "label": "drone", "frame_id": "20", "frame_idx": 20}], "30": [{"bbox": [156, 108, 167, 119], "label": "drone", "frame_id": "30", "frame_idx": 30}], "1": [{"bbox": [156, 108, 167, 119], "label": "drone", "frame_id": "1", "frame_idx": 1, "interpolated": true, "interpolated_from": [0, 10]}], "2": [{"bbox": [156, 108, 167, 119], "label": "drone", "frame_id": "2", "frame_idx": 2, "interpolated": true, "interpolated_from": [0, 10]}], "3": [{"bbox": [156, 108, 167, 119], "label": "drone", "frame_id": "3", "frame_idx": 3, "interpolated": true, "interpolated_from": [0, 10]}], "4": [{"bbox": [156, 108, 167, 119], "label": "drone", "frame_id": "4", "frame_idx": 4, "interpolated": true, "interpolated_from": [0, 10]}], "5": [{"bbox": [156, 108, 167, 119], "label": "drone", "frame_id": "5", "frame_idx": 5, "interpolated": true, "interpolated_from": [0, 10]}], "6": [{"bbox": [156, 108, 167, 119], "label": "drone", "frame_id": "6", "frame_idx": 6, "interpolated": true, "interpolated_from": [0, 10]}], "7": [{"bbox": [156, 108, 167, 119], "label": "drone", "frame_id": "7", "frame_idx": 7, "interpolated": true, "interpolated_from": [0, 10]}], "8": [{"bbox": [156, 108, 167, 119], "label": "drone", "frame_id": "8", "frame_idx": 8, "interpolated": true, "interpolated_from": [0, 10]}], "9": [{"bbox": [156, 108, 167, 119], "label": "drone", "frame_id": "9", "frame_idx": 9, "interpolated": true, "interpolated_from": [0, 10]}], "11": [{"bbox": [156, 108, 167, 119], "label": "drone", "frame_id": "11", "frame_idx": 11, "interpolated": true, "interpolated_from": [10, 20]}], "12": [{"bbox": [156, 108, 167, 119], "label": "drone", "frame_id": "12", "frame_idx": 12, "interpolated": true, "interpolated_from": [10, 20]}], "13": [{"bbox": [156, 108, 167, 119], "label": "drone", "frame_id": "13", "frame_idx": 13, "interpolated": true, "interpolated_from": [10, 20]}], "14": [{"bbox": [156, 108, 167, 119], "label": "drone", "frame_id": "14", "frame_idx": 14, "interpolated": true, "interpolated_from": [10, 20]}], "15": [{"bbox": [156, 108, 167, 119], "label": "drone", "frame_id": "15", "frame_idx": 15, "interpolated": true, "interpolated_from": [10, 20]}], "16": [{"bbox": [156, 108, 167, 119], "label": "drone", "frame_id": "16", "frame_idx": 16, "interpolated": true, "interpolated_from": [10, 20]}], "17": [{"bbox": [156, 108, 167, 119], "label": "drone", "frame_id": "17", "frame_idx": 17, "interpolated": true, "interpolated_from": [10, 20]}], "18": [{"bbox": [156, 108, 167, 119], "label": "drone", "frame_id": "18", "frame_idx": 18, "interpolated": true, "interpolated_from": [10, 20]}], "19": [{"bbox": [156, 108, 167, 119], "label": "drone", "frame_id": "19", "frame_idx": 19, "interpolated": true, "interpolated_from": [10, 20]}], "21": [{"bbox": [156, 108, 167, 119], "label": "drone", "frame_id": "21", "frame_idx": 21, "interpolated": true, "interpolated_from": [20, 30]}], "22": [{"bbox": [156, 108, 167, 119], "label": "drone", "frame_id": "22", "frame_idx": 22, "interpolated": true, "interpolated_from": [20, 30]}], "23": [{"bbox": [156, 108, 167, 119], "label": "drone", "frame_id": "23", "frame_idx": 23, "interpolated": true, "interpolated_from": [20, 30]}], "24": [{"bbox": [156, 108, 167, 119], "label": "drone", "frame_id": "24", "frame_idx": 24, "interpolated": true, "interpolated_from": [20, 30]}], "25": [{"bbox": [156, 108, 167, 119], "label": "drone", "frame_id": "25", "frame_idx": 25, "interpolated": true, "interpolated_from": [20, 30]}], "26": [{"bbox": [156, 108, 167, 119], "label": "drone", "frame_id": "26", "frame_idx": 26, "interpolated": true, "interpolated_from": [20, 30]}], "27": [{"bbox": [156, 108, 167, 119], "label": "drone", "frame_id": "27", "frame_idx": 27, "interpolated": true, "interpolated_from": [20, 30]}], "28": [{"bbox": [156, 108, 167, 119], "label": "drone", "frame_id": "28", "frame_idx": 28, "interpolated": true, "interpolated_from": [20, 30]}], "29": [{"bbox": [156, 108, 167, 119], "label": "drone", "frame_id": "29", "frame_idx": 29, "interpolated": true, "interpolated_from": [20, 30]}], "31": [{"bbox": [156, 108, 167, 119], "label": "drone", "frame_id": "31", "frame_idx": 31, "interpolated": true, "interpolated_from": [30]}], "32": [{"bbox": [156, 108, 167, 119], "label": "drone", "frame_id": "32", "frame_idx": 32, "interpolated": true, "interpolated_from": [30]}], "33": [{"bbox": [156, 108, 167, 119], "label": "drone", "frame_id": "33", "frame_idx": 33, "interpolated": true, "interpolated_from": [30]}], "34": [{"bbox": [156, 108, 167, 119], "label": "drone", "frame_id": "34", "frame_idx": 34, "interpolated": true, "interpolated_from": [30]}], "35": [{"bbox": [156, 108, 167, 119], "label": "drone", "frame_id": "35", "frame_idx": 35, "interpolated": true, "interpolated_from": [30]}], "36": [{"bbox": [156, 108, 167, 119], "label": "drone", "frame_id": "36", "frame_idx": 36, "interpolated": true, "interpolated_from": [30]}], "37": [{"bbox": [156, 108, 167, 119], "label": "drone", "frame_id": "37", "frame_idx": 37, "interpolated": true, "interpolated_from": [30]}], "38": [{"bbox": [156, 108, 167, 119], "label": "drone", "frame_id": "38", "frame_idx": 38, "interpolated": true, "interpolated_from": [30]}], "39": [{"bbox": [156, 108, 167, 119], "label": "drone", "frame_id": "39", "frame_idx": 39, "interpolated": true, "interpolated_from": [30]}], "40": [{"bbox": [156, 108, 167, 119], "label": "drone", "frame_id": "40", "frame_idx": 40, "interpolated": true, "interpolated_from": [30]}], "41": [{"bbox": [156, 108, 167, 119], "label": "drone", "frame_id": "41", "frame_idx": 41, "interpolated": true, "interpolated_from": [30]}], "42": [{"bbox": [156, 108, 167, 119], "label": "drone", "frame_id": "42", "frame_idx": 42, "interpolated": true, "interpolated_from": [30]}], "43": [{"bbox": [156, 108, 167, 119], "label": "drone", "frame_id": "43", "frame_idx": 43, "interpolated": true, "interpolated_from": [30]}], "44": [{"bbox": [156, 108, 167, 119], "label": "drone", "frame_id": "44", "frame_idx": 44, "interpolated": true, "interpolated_from": [30]}], "45": [{"bbox": [156, 108, 167, 119], "label": "drone", "frame_id": "45", "frame_idx": 45, "interpolated": true, "interpolated_from": [30]}], "46": [{"bbox": [156, 108, 167, 119], "label": "drone", "frame_id": "46", "frame_idx": 46, "interpolated": true, "interpolated_from": [30]}], "47": [{"bbox": [156, 108, 167, 119], "label": "drone", "frame_id": "47", "frame_idx": 47, "interpolated": true, "interpolated_from": [30]}], "48": [{"bbox": [156, 108, 167, 119], "label": "drone", "frame_id": "48", "frame_idx": 48, "interpolated": true, "interpolated_from": [30]}], "49": [{"bbox": [156, 108, 167, 119], "label": "drone", "frame_id": "49", "frame_idx": 49, "interpolated": true, "interpolated_from": [30]}]}}, "data06": {"sequence_id": "data06", "total_frames": 50, "key_frames": 5, "total_detections": 50, "direct_detections": 4, "interpolated_detections": 46, "metrics": {"precision": 0.0, "recall": 0.0, "f1": 0, "tp": 0, "fp": 50, "fn": 50}, "detections": {"0": [{"bbox": [175, 128, 180, 133], "label": "drone", "frame_id": "0", "frame_idx": 0}], "10": [{"bbox": [175, 128, 180, 133], "label": "drone", "frame_id": "10", "frame_idx": 10}], "20": [{"bbox": [175, 128, 180, 133], "label": "drone", "frame_id": "20", "frame_idx": 20}], "30": [{"bbox": [175, 128, 180, 133], "label": "drone", "frame_id": "30", "frame_idx": 30}], "1": [{"bbox": [175, 128, 180, 133], "label": "drone", "frame_id": "1", "frame_idx": 1, "interpolated": true, "interpolated_from": [0, 10]}], "2": [{"bbox": [175, 128, 180, 133], "label": "drone", "frame_id": "2", "frame_idx": 2, "interpolated": true, "interpolated_from": [0, 10]}], "3": [{"bbox": [175, 128, 180, 133], "label": "drone", "frame_id": "3", "frame_idx": 3, "interpolated": true, "interpolated_from": [0, 10]}], "4": [{"bbox": [175, 128, 180, 133], "label": "drone", "frame_id": "4", "frame_idx": 4, "interpolated": true, "interpolated_from": [0, 10]}], "5": [{"bbox": [175, 128, 180, 133], "label": "drone", "frame_id": "5", "frame_idx": 5, "interpolated": true, "interpolated_from": [0, 10]}], "6": [{"bbox": [175, 128, 180, 133], "label": "drone", "frame_id": "6", "frame_idx": 6, "interpolated": true, "interpolated_from": [0, 10]}], "7": [{"bbox": [175, 128, 180, 133], "label": "drone", "frame_id": "7", "frame_idx": 7, "interpolated": true, "interpolated_from": [0, 10]}], "8": [{"bbox": [175, 128, 180, 133], "label": "drone", "frame_id": "8", "frame_idx": 8, "interpolated": true, "interpolated_from": [0, 10]}], "9": [{"bbox": [175, 128, 180, 133], "label": "drone", "frame_id": "9", "frame_idx": 9, "interpolated": true, "interpolated_from": [0, 10]}], "11": [{"bbox": [175, 128, 180, 133], "label": "drone", "frame_id": "11", "frame_idx": 11, "interpolated": true, "interpolated_from": [10, 20]}], "12": [{"bbox": [175, 128, 180, 133], "label": "drone", "frame_id": "12", "frame_idx": 12, "interpolated": true, "interpolated_from": [10, 20]}], "13": [{"bbox": [175, 128, 180, 133], "label": "drone", "frame_id": "13", "frame_idx": 13, "interpolated": true, "interpolated_from": [10, 20]}], "14": [{"bbox": [175, 128, 180, 133], "label": "drone", "frame_id": "14", "frame_idx": 14, "interpolated": true, "interpolated_from": [10, 20]}], "15": [{"bbox": [175, 128, 180, 133], "label": "drone", "frame_id": "15", "frame_idx": 15, "interpolated": true, "interpolated_from": [10, 20]}], "16": [{"bbox": [175, 128, 180, 133], "label": "drone", "frame_id": "16", "frame_idx": 16, "interpolated": true, "interpolated_from": [10, 20]}], "17": [{"bbox": [175, 128, 180, 133], "label": "drone", "frame_id": "17", "frame_idx": 17, "interpolated": true, "interpolated_from": [10, 20]}], "18": [{"bbox": [175, 128, 180, 133], "label": "drone", "frame_id": "18", "frame_idx": 18, "interpolated": true, "interpolated_from": [10, 20]}], "19": [{"bbox": [175, 128, 180, 133], "label": "drone", "frame_id": "19", "frame_idx": 19, "interpolated": true, "interpolated_from": [10, 20]}], "21": [{"bbox": [175, 128, 180, 133], "label": "drone", "frame_id": "21", "frame_idx": 21, "interpolated": true, "interpolated_from": [20, 30]}], "22": [{"bbox": [175, 128, 180, 133], "label": "drone", "frame_id": "22", "frame_idx": 22, "interpolated": true, "interpolated_from": [20, 30]}], "23": [{"bbox": [175, 128, 180, 133], "label": "drone", "frame_id": "23", "frame_idx": 23, "interpolated": true, "interpolated_from": [20, 30]}], "24": [{"bbox": [175, 128, 180, 133], "label": "drone", "frame_id": "24", "frame_idx": 24, "interpolated": true, "interpolated_from": [20, 30]}], "25": [{"bbox": [175, 128, 180, 133], "label": "drone", "frame_id": "25", "frame_idx": 25, "interpolated": true, "interpolated_from": [20, 30]}], "26": [{"bbox": [175, 128, 180, 133], "label": "drone", "frame_id": "26", "frame_idx": 26, "interpolated": true, "interpolated_from": [20, 30]}], "27": [{"bbox": [175, 128, 180, 133], "label": "drone", "frame_id": "27", "frame_idx": 27, "interpolated": true, "interpolated_from": [20, 30]}], "28": [{"bbox": [175, 128, 180, 133], "label": "drone", "frame_id": "28", "frame_idx": 28, "interpolated": true, "interpolated_from": [20, 30]}], "29": [{"bbox": [175, 128, 180, 133], "label": "drone", "frame_id": "29", "frame_idx": 29, "interpolated": true, "interpolated_from": [20, 30]}], "31": [{"bbox": [175, 128, 180, 133], "label": "drone", "frame_id": "31", "frame_idx": 31, "interpolated": true, "interpolated_from": [30]}], "32": [{"bbox": [175, 128, 180, 133], "label": "drone", "frame_id": "32", "frame_idx": 32, "interpolated": true, "interpolated_from": [30]}], "33": [{"bbox": [175, 128, 180, 133], "label": "drone", "frame_id": "33", "frame_idx": 33, "interpolated": true, "interpolated_from": [30]}], "34": [{"bbox": [175, 128, 180, 133], "label": "drone", "frame_id": "34", "frame_idx": 34, "interpolated": true, "interpolated_from": [30]}], "35": [{"bbox": [175, 128, 180, 133], "label": "drone", "frame_id": "35", "frame_idx": 35, "interpolated": true, "interpolated_from": [30]}], "36": [{"bbox": [175, 128, 180, 133], "label": "drone", "frame_id": "36", "frame_idx": 36, "interpolated": true, "interpolated_from": [30]}], "37": [{"bbox": [175, 128, 180, 133], "label": "drone", "frame_id": "37", "frame_idx": 37, "interpolated": true, "interpolated_from": [30]}], "38": [{"bbox": [175, 128, 180, 133], "label": "drone", "frame_id": "38", "frame_idx": 38, "interpolated": true, "interpolated_from": [30]}], "39": [{"bbox": [175, 128, 180, 133], "label": "drone", "frame_id": "39", "frame_idx": 39, "interpolated": true, "interpolated_from": [30]}], "40": [{"bbox": [175, 128, 180, 133], "label": "drone", "frame_id": "40", "frame_idx": 40, "interpolated": true, "interpolated_from": [30]}], "41": [{"bbox": [175, 128, 180, 133], "label": "drone", "frame_id": "41", "frame_idx": 41, "interpolated": true, "interpolated_from": [30]}], "42": [{"bbox": [175, 128, 180, 133], "label": "drone", "frame_id": "42", "frame_idx": 42, "interpolated": true, "interpolated_from": [30]}], "43": [{"bbox": [175, 128, 180, 133], "label": "drone", "frame_id": "43", "frame_idx": 43, "interpolated": true, "interpolated_from": [30]}], "44": [{"bbox": [175, 128, 180, 133], "label": "drone", "frame_id": "44", "frame_idx": 44, "interpolated": true, "interpolated_from": [30]}], "45": [{"bbox": [175, 128, 180, 133], "label": "drone", "frame_id": "45", "frame_idx": 45, "interpolated": true, "interpolated_from": [30]}], "46": [{"bbox": [175, 128, 180, 133], "label": "drone", "frame_id": "46", "frame_idx": 46, "interpolated": true, "interpolated_from": [30]}], "47": [{"bbox": [175, 128, 180, 133], "label": "drone", "frame_id": "47", "frame_idx": 47, "interpolated": true, "interpolated_from": [30]}], "48": [{"bbox": [175, 128, 180, 133], "label": "drone", "frame_id": "48", "frame_idx": 48, "interpolated": true, "interpolated_from": [30]}], "49": [{"bbox": [175, 128, 180, 133], "label": "drone", "frame_id": "49", "frame_idx": 49, "interpolated": true, "interpolated_from": [30]}]}}, "data07": {"sequence_id": "data07", "total_frames": 50, "key_frames": 5, "total_detections": 50, "direct_detections": 4, "interpolated_detections": 46, "metrics": {"precision": 0.0, "recall": 0.0, "f1": 0, "tp": 0, "fp": 50, "fn": 50}, "detections": {"0": [{"bbox": [250, 168, 260, 178], "label": "drone", "frame_id": "0", "frame_idx": 0}], "10": [{"bbox": [250, 168, 260, 178], "label": "drone", "frame_id": "10", "frame_idx": 10}], "20": [{"bbox": [250, 168, 260, 178], "label": "drone", "frame_id": "20", "frame_idx": 20}], "30": [{"bbox": [250, 168, 260, 178], "label": "drone", "frame_id": "30", "frame_idx": 30}], "1": [{"bbox": [250, 168, 260, 178], "label": "drone", "frame_id": "1", "frame_idx": 1, "interpolated": true, "interpolated_from": [0, 10]}], "2": [{"bbox": [250, 168, 260, 178], "label": "drone", "frame_id": "2", "frame_idx": 2, "interpolated": true, "interpolated_from": [0, 10]}], "3": [{"bbox": [250, 168, 260, 178], "label": "drone", "frame_id": "3", "frame_idx": 3, "interpolated": true, "interpolated_from": [0, 10]}], "4": [{"bbox": [250, 168, 260, 178], "label": "drone", "frame_id": "4", "frame_idx": 4, "interpolated": true, "interpolated_from": [0, 10]}], "5": [{"bbox": [250, 168, 260, 178], "label": "drone", "frame_id": "5", "frame_idx": 5, "interpolated": true, "interpolated_from": [0, 10]}], "6": [{"bbox": [250, 168, 260, 178], "label": "drone", "frame_id": "6", "frame_idx": 6, "interpolated": true, "interpolated_from": [0, 10]}], "7": [{"bbox": [250, 168, 260, 178], "label": "drone", "frame_id": "7", "frame_idx": 7, "interpolated": true, "interpolated_from": [0, 10]}], "8": [{"bbox": [250, 168, 260, 178], "label": "drone", "frame_id": "8", "frame_idx": 8, "interpolated": true, "interpolated_from": [0, 10]}], "9": [{"bbox": [250, 168, 260, 178], "label": "drone", "frame_id": "9", "frame_idx": 9, "interpolated": true, "interpolated_from": [0, 10]}], "11": [{"bbox": [250, 168, 260, 178], "label": "drone", "frame_id": "11", "frame_idx": 11, "interpolated": true, "interpolated_from": [10, 20]}], "12": [{"bbox": [250, 168, 260, 178], "label": "drone", "frame_id": "12", "frame_idx": 12, "interpolated": true, "interpolated_from": [10, 20]}], "13": [{"bbox": [250, 168, 260, 178], "label": "drone", "frame_id": "13", "frame_idx": 13, "interpolated": true, "interpolated_from": [10, 20]}], "14": [{"bbox": [250, 168, 260, 178], "label": "drone", "frame_id": "14", "frame_idx": 14, "interpolated": true, "interpolated_from": [10, 20]}], "15": [{"bbox": [250, 168, 260, 178], "label": "drone", "frame_id": "15", "frame_idx": 15, "interpolated": true, "interpolated_from": [10, 20]}], "16": [{"bbox": [250, 168, 260, 178], "label": "drone", "frame_id": "16", "frame_idx": 16, "interpolated": true, "interpolated_from": [10, 20]}], "17": [{"bbox": [250, 168, 260, 178], "label": "drone", "frame_id": "17", "frame_idx": 17, "interpolated": true, "interpolated_from": [10, 20]}], "18": [{"bbox": [250, 168, 260, 178], "label": "drone", "frame_id": "18", "frame_idx": 18, "interpolated": true, "interpolated_from": [10, 20]}], "19": [{"bbox": [250, 168, 260, 178], "label": "drone", "frame_id": "19", "frame_idx": 19, "interpolated": true, "interpolated_from": [10, 20]}], "21": [{"bbox": [250, 168, 260, 178], "label": "drone", "frame_id": "21", "frame_idx": 21, "interpolated": true, "interpolated_from": [20, 30]}], "22": [{"bbox": [250, 168, 260, 178], "label": "drone", "frame_id": "22", "frame_idx": 22, "interpolated": true, "interpolated_from": [20, 30]}], "23": [{"bbox": [250, 168, 260, 178], "label": "drone", "frame_id": "23", "frame_idx": 23, "interpolated": true, "interpolated_from": [20, 30]}], "24": [{"bbox": [250, 168, 260, 178], "label": "drone", "frame_id": "24", "frame_idx": 24, "interpolated": true, "interpolated_from": [20, 30]}], "25": [{"bbox": [250, 168, 260, 178], "label": "drone", "frame_id": "25", "frame_idx": 25, "interpolated": true, "interpolated_from": [20, 30]}], "26": [{"bbox": [250, 168, 260, 178], "label": "drone", "frame_id": "26", "frame_idx": 26, "interpolated": true, "interpolated_from": [20, 30]}], "27": [{"bbox": [250, 168, 260, 178], "label": "drone", "frame_id": "27", "frame_idx": 27, "interpolated": true, "interpolated_from": [20, 30]}], "28": [{"bbox": [250, 168, 260, 178], "label": "drone", "frame_id": "28", "frame_idx": 28, "interpolated": true, "interpolated_from": [20, 30]}], "29": [{"bbox": [250, 168, 260, 178], "label": "drone", "frame_id": "29", "frame_idx": 29, "interpolated": true, "interpolated_from": [20, 30]}], "31": [{"bbox": [250, 168, 260, 178], "label": "drone", "frame_id": "31", "frame_idx": 31, "interpolated": true, "interpolated_from": [30]}], "32": [{"bbox": [250, 168, 260, 178], "label": "drone", "frame_id": "32", "frame_idx": 32, "interpolated": true, "interpolated_from": [30]}], "33": [{"bbox": [250, 168, 260, 178], "label": "drone", "frame_id": "33", "frame_idx": 33, "interpolated": true, "interpolated_from": [30]}], "34": [{"bbox": [250, 168, 260, 178], "label": "drone", "frame_id": "34", "frame_idx": 34, "interpolated": true, "interpolated_from": [30]}], "35": [{"bbox": [250, 168, 260, 178], "label": "drone", "frame_id": "35", "frame_idx": 35, "interpolated": true, "interpolated_from": [30]}], "36": [{"bbox": [250, 168, 260, 178], "label": "drone", "frame_id": "36", "frame_idx": 36, "interpolated": true, "interpolated_from": [30]}], "37": [{"bbox": [250, 168, 260, 178], "label": "drone", "frame_id": "37", "frame_idx": 37, "interpolated": true, "interpolated_from": [30]}], "38": [{"bbox": [250, 168, 260, 178], "label": "drone", "frame_id": "38", "frame_idx": 38, "interpolated": true, "interpolated_from": [30]}], "39": [{"bbox": [250, 168, 260, 178], "label": "drone", "frame_id": "39", "frame_idx": 39, "interpolated": true, "interpolated_from": [30]}], "40": [{"bbox": [250, 168, 260, 178], "label": "drone", "frame_id": "40", "frame_idx": 40, "interpolated": true, "interpolated_from": [30]}], "41": [{"bbox": [250, 168, 260, 178], "label": "drone", "frame_id": "41", "frame_idx": 41, "interpolated": true, "interpolated_from": [30]}], "42": [{"bbox": [250, 168, 260, 178], "label": "drone", "frame_id": "42", "frame_idx": 42, "interpolated": true, "interpolated_from": [30]}], "43": [{"bbox": [250, 168, 260, 178], "label": "drone", "frame_id": "43", "frame_idx": 43, "interpolated": true, "interpolated_from": [30]}], "44": [{"bbox": [250, 168, 260, 178], "label": "drone", "frame_id": "44", "frame_idx": 44, "interpolated": true, "interpolated_from": [30]}], "45": [{"bbox": [250, 168, 260, 178], "label": "drone", "frame_id": "45", "frame_idx": 45, "interpolated": true, "interpolated_from": [30]}], "46": [{"bbox": [250, 168, 260, 178], "label": "drone", "frame_id": "46", "frame_idx": 46, "interpolated": true, "interpolated_from": [30]}], "47": [{"bbox": [250, 168, 260, 178], "label": "drone", "frame_id": "47", "frame_idx": 47, "interpolated": true, "interpolated_from": [30]}], "48": [{"bbox": [250, 168, 260, 178], "label": "drone", "frame_id": "48", "frame_idx": 48, "interpolated": true, "interpolated_from": [30]}], "49": [{"bbox": [250, 168, 260, 178], "label": "drone", "frame_id": "49", "frame_idx": 49, "interpolated": true, "interpolated_from": [30]}]}}, "data19": {"sequence_id": "data19", "total_frames": 75, "key_frames": 8, "total_detections": 75, "direct_detections": 8, "interpolated_detections": 67, "metrics": {"precision": 0.0, "recall": 0, "f1": 0, "tp": 0, "fp": 75, "fn": 0}, "detections": {"0": [{"bbox": [185, 176, 200, 190], "label": "drone", "frame_id": "0", "frame_idx": 0}], "10": [{"bbox": [185, 176, 200, 190], "label": "drone", "frame_id": "10", "frame_idx": 10}], "20": [{"bbox": [185, 176, 200, 190], "label": "drone", "frame_id": "20", "frame_idx": 20}], "30": [{"bbox": [185, 176, 200, 190], "label": "drone", "frame_id": "30", "frame_idx": 30}], "40": [{"bbox": [189, 199, 214, 224], "label": "drone", "frame_id": "40", "frame_idx": 40}], "50": [{"bbox": [190, 200, 214, 224], "label": "drone", "frame_id": "50", "frame_idx": 50}], "60": [{"bbox": [190, 200, 214, 224], "label": "drone", "frame_id": "60", "frame_idx": 60}], "70": [{"bbox": [190, 200, 214, 224], "label": "drone", "frame_id": "70", "frame_idx": 70}], "1": [{"bbox": [185, 176, 200, 190], "label": "drone", "frame_id": "1", "frame_idx": 1, "interpolated": true, "interpolated_from": [0, 10]}], "2": [{"bbox": [185, 176, 200, 190], "label": "drone", "frame_id": "2", "frame_idx": 2, "interpolated": true, "interpolated_from": [0, 10]}], "3": [{"bbox": [185, 176, 200, 190], "label": "drone", "frame_id": "3", "frame_idx": 3, "interpolated": true, "interpolated_from": [0, 10]}], "4": [{"bbox": [185, 176, 200, 190], "label": "drone", "frame_id": "4", "frame_idx": 4, "interpolated": true, "interpolated_from": [0, 10]}], "5": [{"bbox": [185, 176, 200, 190], "label": "drone", "frame_id": "5", "frame_idx": 5, "interpolated": true, "interpolated_from": [0, 10]}], "6": [{"bbox": [185, 176, 200, 190], "label": "drone", "frame_id": "6", "frame_idx": 6, "interpolated": true, "interpolated_from": [0, 10]}], "7": [{"bbox": [185, 176, 200, 190], "label": "drone", "frame_id": "7", "frame_idx": 7, "interpolated": true, "interpolated_from": [0, 10]}], "8": [{"bbox": [185, 176, 200, 190], "label": "drone", "frame_id": "8", "frame_idx": 8, "interpolated": true, "interpolated_from": [0, 10]}], "9": [{"bbox": [185, 176, 200, 190], "label": "drone", "frame_id": "9", "frame_idx": 9, "interpolated": true, "interpolated_from": [0, 10]}], "11": [{"bbox": [185, 176, 200, 190], "label": "drone", "frame_id": "11", "frame_idx": 11, "interpolated": true, "interpolated_from": [10, 20]}], "12": [{"bbox": [185, 176, 200, 190], "label": "drone", "frame_id": "12", "frame_idx": 12, "interpolated": true, "interpolated_from": [10, 20]}], "13": [{"bbox": [185, 176, 200, 190], "label": "drone", "frame_id": "13", "frame_idx": 13, "interpolated": true, "interpolated_from": [10, 20]}], "14": [{"bbox": [185, 176, 200, 190], "label": "drone", "frame_id": "14", "frame_idx": 14, "interpolated": true, "interpolated_from": [10, 20]}], "15": [{"bbox": [185, 176, 200, 190], "label": "drone", "frame_id": "15", "frame_idx": 15, "interpolated": true, "interpolated_from": [10, 20]}], "16": [{"bbox": [185, 176, 200, 190], "label": "drone", "frame_id": "16", "frame_idx": 16, "interpolated": true, "interpolated_from": [10, 20]}], "17": [{"bbox": [185, 176, 200, 190], "label": "drone", "frame_id": "17", "frame_idx": 17, "interpolated": true, "interpolated_from": [10, 20]}], "18": [{"bbox": [185, 176, 200, 190], "label": "drone", "frame_id": "18", "frame_idx": 18, "interpolated": true, "interpolated_from": [10, 20]}], "19": [{"bbox": [185, 176, 200, 190], "label": "drone", "frame_id": "19", "frame_idx": 19, "interpolated": true, "interpolated_from": [10, 20]}], "21": [{"bbox": [185, 176, 200, 190], "label": "drone", "frame_id": "21", "frame_idx": 21, "interpolated": true, "interpolated_from": [20, 30]}], "22": [{"bbox": [185, 176, 200, 190], "label": "drone", "frame_id": "22", "frame_idx": 22, "interpolated": true, "interpolated_from": [20, 30]}], "23": [{"bbox": [185, 176, 200, 190], "label": "drone", "frame_id": "23", "frame_idx": 23, "interpolated": true, "interpolated_from": [20, 30]}], "24": [{"bbox": [185, 176, 200, 190], "label": "drone", "frame_id": "24", "frame_idx": 24, "interpolated": true, "interpolated_from": [20, 30]}], "25": [{"bbox": [185, 176, 200, 190], "label": "drone", "frame_id": "25", "frame_idx": 25, "interpolated": true, "interpolated_from": [20, 30]}], "26": [{"bbox": [185, 176, 200, 190], "label": "drone", "frame_id": "26", "frame_idx": 26, "interpolated": true, "interpolated_from": [20, 30]}], "27": [{"bbox": [185, 176, 200, 190], "label": "drone", "frame_id": "27", "frame_idx": 27, "interpolated": true, "interpolated_from": [20, 30]}], "28": [{"bbox": [185, 176, 200, 190], "label": "drone", "frame_id": "28", "frame_idx": 28, "interpolated": true, "interpolated_from": [20, 30]}], "29": [{"bbox": [185, 176, 200, 190], "label": "drone", "frame_id": "29", "frame_idx": 29, "interpolated": true, "interpolated_from": [20, 30]}], "31": [{"bbox": [185, 178, 201, 193], "label": "drone", "frame_id": "31", "frame_idx": 31, "interpolated": true, "interpolated_from": [30, 40]}], "32": [{"bbox": [185, 180, 202, 196], "label": "drone", "frame_id": "32", "frame_idx": 32, "interpolated": true, "interpolated_from": [30, 40]}], "33": [{"bbox": [186, 182, 204, 200], "label": "drone", "frame_id": "33", "frame_idx": 33, "interpolated": true, "interpolated_from": [30, 40]}], "34": [{"bbox": [186, 185, 205, 203], "label": "drone", "frame_id": "34", "frame_idx": 34, "interpolated": true, "interpolated_from": [30, 40]}], "35": [{"bbox": [187, 187, 207, 207], "label": "drone", "frame_id": "35", "frame_idx": 35, "interpolated": true, "interpolated_from": [30, 40]}], "36": [{"bbox": [187, 189, 208, 210], "label": "drone", "frame_id": "36", "frame_idx": 36, "interpolated": true, "interpolated_from": [30, 40]}], "37": [{"bbox": [187, 192, 209, 213], "label": "drone", "frame_id": "37", "frame_idx": 37, "interpolated": true, "interpolated_from": [30, 40]}], "38": [{"bbox": [188, 194, 211, 217], "label": "drone", "frame_id": "38", "frame_idx": 38, "interpolated": true, "interpolated_from": [30, 40]}], "39": [{"bbox": [188, 196, 212, 220], "label": "drone", "frame_id": "39", "frame_idx": 39, "interpolated": true, "interpolated_from": [30, 40]}], "41": [{"bbox": [189, 199, 214, 224], "label": "drone", "frame_id": "41", "frame_idx": 41, "interpolated": true, "interpolated_from": [40, 50]}], "42": [{"bbox": [189, 199, 214, 224], "label": "drone", "frame_id": "42", "frame_idx": 42, "interpolated": true, "interpolated_from": [40, 50]}], "43": [{"bbox": [189, 199, 214, 224], "label": "drone", "frame_id": "43", "frame_idx": 43, "interpolated": true, "interpolated_from": [40, 50]}], "44": [{"bbox": [189, 199, 214, 224], "label": "drone", "frame_id": "44", "frame_idx": 44, "interpolated": true, "interpolated_from": [40, 50]}], "45": [{"bbox": [189, 199, 214, 224], "label": "drone", "frame_id": "45", "frame_idx": 45, "interpolated": true, "interpolated_from": [40, 50]}], "46": [{"bbox": [189, 199, 214, 224], "label": "drone", "frame_id": "46", "frame_idx": 46, "interpolated": true, "interpolated_from": [40, 50]}], "47": [{"bbox": [189, 199, 214, 224], "label": "drone", "frame_id": "47", "frame_idx": 47, "interpolated": true, "interpolated_from": [40, 50]}], "48": [{"bbox": [189, 199, 214, 224], "label": "drone", "frame_id": "48", "frame_idx": 48, "interpolated": true, "interpolated_from": [40, 50]}], "49": [{"bbox": [189, 199, 214, 224], "label": "drone", "frame_id": "49", "frame_idx": 49, "interpolated": true, "interpolated_from": [40, 50]}], "51": [{"bbox": [190, 200, 214, 224], "label": "drone", "frame_id": "51", "frame_idx": 51, "interpolated": true, "interpolated_from": [50, 60]}], "52": [{"bbox": [190, 200, 214, 224], "label": "drone", "frame_id": "52", "frame_idx": 52, "interpolated": true, "interpolated_from": [50, 60]}], "53": [{"bbox": [190, 200, 214, 224], "label": "drone", "frame_id": "53", "frame_idx": 53, "interpolated": true, "interpolated_from": [50, 60]}], "54": [{"bbox": [190, 200, 214, 224], "label": "drone", "frame_id": "54", "frame_idx": 54, "interpolated": true, "interpolated_from": [50, 60]}], "55": [{"bbox": [190, 200, 214, 224], "label": "drone", "frame_id": "55", "frame_idx": 55, "interpolated": true, "interpolated_from": [50, 60]}], "56": [{"bbox": [190, 200, 214, 224], "label": "drone", "frame_id": "56", "frame_idx": 56, "interpolated": true, "interpolated_from": [50, 60]}], "57": [{"bbox": [190, 200, 214, 224], "label": "drone", "frame_id": "57", "frame_idx": 57, "interpolated": true, "interpolated_from": [50, 60]}], "58": [{"bbox": [190, 200, 214, 224], "label": "drone", "frame_id": "58", "frame_idx": 58, "interpolated": true, "interpolated_from": [50, 60]}], "59": [{"bbox": [190, 200, 214, 224], "label": "drone", "frame_id": "59", "frame_idx": 59, "interpolated": true, "interpolated_from": [50, 60]}], "61": [{"bbox": [190, 200, 214, 224], "label": "drone", "frame_id": "61", "frame_idx": 61, "interpolated": true, "interpolated_from": [60, 70]}], "62": [{"bbox": [190, 200, 214, 224], "label": "drone", "frame_id": "62", "frame_idx": 62, "interpolated": true, "interpolated_from": [60, 70]}], "63": [{"bbox": [190, 200, 214, 224], "label": "drone", "frame_id": "63", "frame_idx": 63, "interpolated": true, "interpolated_from": [60, 70]}], "64": [{"bbox": [190, 200, 214, 224], "label": "drone", "frame_id": "64", "frame_idx": 64, "interpolated": true, "interpolated_from": [60, 70]}], "65": [{"bbox": [190, 200, 214, 224], "label": "drone", "frame_id": "65", "frame_idx": 65, "interpolated": true, "interpolated_from": [60, 70]}], "66": [{"bbox": [190, 200, 214, 224], "label": "drone", "frame_id": "66", "frame_idx": 66, "interpolated": true, "interpolated_from": [60, 70]}], "67": [{"bbox": [190, 200, 214, 224], "label": "drone", "frame_id": "67", "frame_idx": 67, "interpolated": true, "interpolated_from": [60, 70]}], "68": [{"bbox": [190, 200, 214, 224], "label": "drone", "frame_id": "68", "frame_idx": 68, "interpolated": true, "interpolated_from": [60, 70]}], "69": [{"bbox": [190, 200, 214, 224], "label": "drone", "frame_id": "69", "frame_idx": 69, "interpolated": true, "interpolated_from": [60, 70]}], "71": [{"bbox": [190, 200, 214, 224], "label": "drone", "frame_id": "71", "frame_idx": 71, "interpolated": true, "interpolated_from": [70]}], "72": [{"bbox": [190, 200, 214, 224], "label": "drone", "frame_id": "72", "frame_idx": 72, "interpolated": true, "interpolated_from": [70]}], "73": [{"bbox": [190, 200, 214, 224], "label": "drone", "frame_id": "73", "frame_idx": 73, "interpolated": true, "interpolated_from": [70]}], "74": [{"bbox": [190, 200, 214, 224], "label": "drone", "frame_id": "74", "frame_idx": 74, "interpolated": true, "interpolated_from": [70]}]}}, "data23": {"sequence_id": "data23", "total_frames": 50, "key_frames": 5, "total_detections": 50, "direct_detections": 4, "interpolated_detections": 46, "metrics": {"precision": 0.0, "recall": 0, "f1": 0, "tp": 0, "fp": 50, "fn": 0}, "detections": {"0": [{"bbox": [150, 68, 190, 107], "label": "drone", "frame_id": "0", "frame_idx": 0}], "10": [{"bbox": [150, 68, 190, 107], "label": "drone", "frame_id": "10", "frame_idx": 10}], "20": [{"bbox": [150, 68, 190, 107], "label": "drone", "frame_id": "20", "frame_idx": 20}], "30": [{"bbox": [150, 68, 190, 107], "label": "drone", "frame_id": "30", "frame_idx": 30}], "1": [{"bbox": [150, 68, 190, 107], "label": "drone", "frame_id": "1", "frame_idx": 1, "interpolated": true, "interpolated_from": [0, 10]}], "2": [{"bbox": [150, 68, 190, 107], "label": "drone", "frame_id": "2", "frame_idx": 2, "interpolated": true, "interpolated_from": [0, 10]}], "3": [{"bbox": [150, 68, 190, 107], "label": "drone", "frame_id": "3", "frame_idx": 3, "interpolated": true, "interpolated_from": [0, 10]}], "4": [{"bbox": [150, 68, 190, 107], "label": "drone", "frame_id": "4", "frame_idx": 4, "interpolated": true, "interpolated_from": [0, 10]}], "5": [{"bbox": [150, 68, 190, 107], "label": "drone", "frame_id": "5", "frame_idx": 5, "interpolated": true, "interpolated_from": [0, 10]}], "6": [{"bbox": [150, 68, 190, 107], "label": "drone", "frame_id": "6", "frame_idx": 6, "interpolated": true, "interpolated_from": [0, 10]}], "7": [{"bbox": [150, 68, 190, 107], "label": "drone", "frame_id": "7", "frame_idx": 7, "interpolated": true, "interpolated_from": [0, 10]}], "8": [{"bbox": [150, 68, 190, 107], "label": "drone", "frame_id": "8", "frame_idx": 8, "interpolated": true, "interpolated_from": [0, 10]}], "9": [{"bbox": [150, 68, 190, 107], "label": "drone", "frame_id": "9", "frame_idx": 9, "interpolated": true, "interpolated_from": [0, 10]}], "11": [{"bbox": [150, 68, 190, 107], "label": "drone", "frame_id": "11", "frame_idx": 11, "interpolated": true, "interpolated_from": [10, 20]}], "12": [{"bbox": [150, 68, 190, 107], "label": "drone", "frame_id": "12", "frame_idx": 12, "interpolated": true, "interpolated_from": [10, 20]}], "13": [{"bbox": [150, 68, 190, 107], "label": "drone", "frame_id": "13", "frame_idx": 13, "interpolated": true, "interpolated_from": [10, 20]}], "14": [{"bbox": [150, 68, 190, 107], "label": "drone", "frame_id": "14", "frame_idx": 14, "interpolated": true, "interpolated_from": [10, 20]}], "15": [{"bbox": [150, 68, 190, 107], "label": "drone", "frame_id": "15", "frame_idx": 15, "interpolated": true, "interpolated_from": [10, 20]}], "16": [{"bbox": [150, 68, 190, 107], "label": "drone", "frame_id": "16", "frame_idx": 16, "interpolated": true, "interpolated_from": [10, 20]}], "17": [{"bbox": [150, 68, 190, 107], "label": "drone", "frame_id": "17", "frame_idx": 17, "interpolated": true, "interpolated_from": [10, 20]}], "18": [{"bbox": [150, 68, 190, 107], "label": "drone", "frame_id": "18", "frame_idx": 18, "interpolated": true, "interpolated_from": [10, 20]}], "19": [{"bbox": [150, 68, 190, 107], "label": "drone", "frame_id": "19", "frame_idx": 19, "interpolated": true, "interpolated_from": [10, 20]}], "21": [{"bbox": [150, 68, 190, 107], "label": "drone", "frame_id": "21", "frame_idx": 21, "interpolated": true, "interpolated_from": [20, 30]}], "22": [{"bbox": [150, 68, 190, 107], "label": "drone", "frame_id": "22", "frame_idx": 22, "interpolated": true, "interpolated_from": [20, 30]}], "23": [{"bbox": [150, 68, 190, 107], "label": "drone", "frame_id": "23", "frame_idx": 23, "interpolated": true, "interpolated_from": [20, 30]}], "24": [{"bbox": [150, 68, 190, 107], "label": "drone", "frame_id": "24", "frame_idx": 24, "interpolated": true, "interpolated_from": [20, 30]}], "25": [{"bbox": [150, 68, 190, 107], "label": "drone", "frame_id": "25", "frame_idx": 25, "interpolated": true, "interpolated_from": [20, 30]}], "26": [{"bbox": [150, 68, 190, 107], "label": "drone", "frame_id": "26", "frame_idx": 26, "interpolated": true, "interpolated_from": [20, 30]}], "27": [{"bbox": [150, 68, 190, 107], "label": "drone", "frame_id": "27", "frame_idx": 27, "interpolated": true, "interpolated_from": [20, 30]}], "28": [{"bbox": [150, 68, 190, 107], "label": "drone", "frame_id": "28", "frame_idx": 28, "interpolated": true, "interpolated_from": [20, 30]}], "29": [{"bbox": [150, 68, 190, 107], "label": "drone", "frame_id": "29", "frame_idx": 29, "interpolated": true, "interpolated_from": [20, 30]}], "31": [{"bbox": [150, 68, 190, 107], "label": "drone", "frame_id": "31", "frame_idx": 31, "interpolated": true, "interpolated_from": [30]}], "32": [{"bbox": [150, 68, 190, 107], "label": "drone", "frame_id": "32", "frame_idx": 32, "interpolated": true, "interpolated_from": [30]}], "33": [{"bbox": [150, 68, 190, 107], "label": "drone", "frame_id": "33", "frame_idx": 33, "interpolated": true, "interpolated_from": [30]}], "34": [{"bbox": [150, 68, 190, 107], "label": "drone", "frame_id": "34", "frame_idx": 34, "interpolated": true, "interpolated_from": [30]}], "35": [{"bbox": [150, 68, 190, 107], "label": "drone", "frame_id": "35", "frame_idx": 35, "interpolated": true, "interpolated_from": [30]}], "36": [{"bbox": [150, 68, 190, 107], "label": "drone", "frame_id": "36", "frame_idx": 36, "interpolated": true, "interpolated_from": [30]}], "37": [{"bbox": [150, 68, 190, 107], "label": "drone", "frame_id": "37", "frame_idx": 37, "interpolated": true, "interpolated_from": [30]}], "38": [{"bbox": [150, 68, 190, 107], "label": "drone", "frame_id": "38", "frame_idx": 38, "interpolated": true, "interpolated_from": [30]}], "39": [{"bbox": [150, 68, 190, 107], "label": "drone", "frame_id": "39", "frame_idx": 39, "interpolated": true, "interpolated_from": [30]}], "40": [{"bbox": [150, 68, 190, 107], "label": "drone", "frame_id": "40", "frame_idx": 40, "interpolated": true, "interpolated_from": [30]}], "41": [{"bbox": [150, 68, 190, 107], "label": "drone", "frame_id": "41", "frame_idx": 41, "interpolated": true, "interpolated_from": [30]}], "42": [{"bbox": [150, 68, 190, 107], "label": "drone", "frame_id": "42", "frame_idx": 42, "interpolated": true, "interpolated_from": [30]}], "43": [{"bbox": [150, 68, 190, 107], "label": "drone", "frame_id": "43", "frame_idx": 43, "interpolated": true, "interpolated_from": [30]}], "44": [{"bbox": [150, 68, 190, 107], "label": "drone", "frame_id": "44", "frame_idx": 44, "interpolated": true, "interpolated_from": [30]}], "45": [{"bbox": [150, 68, 190, 107], "label": "drone", "frame_id": "45", "frame_idx": 45, "interpolated": true, "interpolated_from": [30]}], "46": [{"bbox": [150, 68, 190, 107], "label": "drone", "frame_id": "46", "frame_idx": 46, "interpolated": true, "interpolated_from": [30]}], "47": [{"bbox": [150, 68, 190, 107], "label": "drone", "frame_id": "47", "frame_idx": 47, "interpolated": true, "interpolated_from": [30]}], "48": [{"bbox": [150, 68, 190, 107], "label": "drone", "frame_id": "48", "frame_idx": 48, "interpolated": true, "interpolated_from": [30]}], "49": [{"bbox": [150, 68, 190, 107], "label": "drone", "frame_id": "49", "frame_idx": 49, "interpolated": true, "interpolated_from": [30]}]}}, "data26": {"sequence_id": "data26", "total_frames": 75, "key_frames": 8, "total_detections": 75, "direct_detections": 8, "interpolated_detections": 67, "metrics": {"precision": 0.0, "recall": 0, "f1": 0, "tp": 0, "fp": 75, "fn": 0}, "detections": {"0": [{"bbox": [285, 297, 300, 312], "label": "drone", "frame_id": "0", "frame_idx": 0}], "10": [{"bbox": [285, 297, 300, 312], "label": "drone", "frame_id": "10", "frame_idx": 10}], "20": [{"bbox": [285, 297, 300, 312], "label": "drone", "frame_id": "20", "frame_idx": 20}], "30": [{"bbox": [285, 297, 300, 312], "label": "drone", "frame_id": "30", "frame_idx": 30}], "40": [{"bbox": [198, 100, 210, 110], "label": "drone", "frame_id": "40", "frame_idx": 40}], "50": [{"bbox": [100, 150, 110, 160], "label": "drone", "frame_id": "50", "frame_idx": 50}], "60": [{"bbox": [200, 200, 210, 210], "label": "drone", "frame_id": "60", "frame_idx": 60}], "70": [{"bbox": [100, 250, 110, 260], "label": "drone", "frame_id": "70", "frame_idx": 70}], "1": [{"bbox": [285, 297, 300, 312], "label": "drone", "frame_id": "1", "frame_idx": 1, "interpolated": true, "interpolated_from": [0, 10]}], "2": [{"bbox": [285, 297, 300, 312], "label": "drone", "frame_id": "2", "frame_idx": 2, "interpolated": true, "interpolated_from": [0, 10]}], "3": [{"bbox": [285, 297, 300, 312], "label": "drone", "frame_id": "3", "frame_idx": 3, "interpolated": true, "interpolated_from": [0, 10]}], "4": [{"bbox": [285, 297, 300, 312], "label": "drone", "frame_id": "4", "frame_idx": 4, "interpolated": true, "interpolated_from": [0, 10]}], "5": [{"bbox": [285, 297, 300, 312], "label": "drone", "frame_id": "5", "frame_idx": 5, "interpolated": true, "interpolated_from": [0, 10]}], "6": [{"bbox": [285, 297, 300, 312], "label": "drone", "frame_id": "6", "frame_idx": 6, "interpolated": true, "interpolated_from": [0, 10]}], "7": [{"bbox": [285, 297, 300, 312], "label": "drone", "frame_id": "7", "frame_idx": 7, "interpolated": true, "interpolated_from": [0, 10]}], "8": [{"bbox": [285, 297, 300, 312], "label": "drone", "frame_id": "8", "frame_idx": 8, "interpolated": true, "interpolated_from": [0, 10]}], "9": [{"bbox": [285, 297, 300, 312], "label": "drone", "frame_id": "9", "frame_idx": 9, "interpolated": true, "interpolated_from": [0, 10]}], "11": [{"bbox": [285, 297, 300, 312], "label": "drone", "frame_id": "11", "frame_idx": 11, "interpolated": true, "interpolated_from": [10, 20]}], "12": [{"bbox": [285, 297, 300, 312], "label": "drone", "frame_id": "12", "frame_idx": 12, "interpolated": true, "interpolated_from": [10, 20]}], "13": [{"bbox": [285, 297, 300, 312], "label": "drone", "frame_id": "13", "frame_idx": 13, "interpolated": true, "interpolated_from": [10, 20]}], "14": [{"bbox": [285, 297, 300, 312], "label": "drone", "frame_id": "14", "frame_idx": 14, "interpolated": true, "interpolated_from": [10, 20]}], "15": [{"bbox": [285, 297, 300, 312], "label": "drone", "frame_id": "15", "frame_idx": 15, "interpolated": true, "interpolated_from": [10, 20]}], "16": [{"bbox": [285, 297, 300, 312], "label": "drone", "frame_id": "16", "frame_idx": 16, "interpolated": true, "interpolated_from": [10, 20]}], "17": [{"bbox": [285, 297, 300, 312], "label": "drone", "frame_id": "17", "frame_idx": 17, "interpolated": true, "interpolated_from": [10, 20]}], "18": [{"bbox": [285, 297, 300, 312], "label": "drone", "frame_id": "18", "frame_idx": 18, "interpolated": true, "interpolated_from": [10, 20]}], "19": [{"bbox": [285, 297, 300, 312], "label": "drone", "frame_id": "19", "frame_idx": 19, "interpolated": true, "interpolated_from": [10, 20]}], "21": [{"bbox": [285, 297, 300, 312], "label": "drone", "frame_id": "21", "frame_idx": 21, "interpolated": true, "interpolated_from": [20, 30]}], "22": [{"bbox": [285, 297, 300, 312], "label": "drone", "frame_id": "22", "frame_idx": 22, "interpolated": true, "interpolated_from": [20, 30]}], "23": [{"bbox": [285, 297, 300, 312], "label": "drone", "frame_id": "23", "frame_idx": 23, "interpolated": true, "interpolated_from": [20, 30]}], "24": [{"bbox": [285, 297, 300, 312], "label": "drone", "frame_id": "24", "frame_idx": 24, "interpolated": true, "interpolated_from": [20, 30]}], "25": [{"bbox": [285, 297, 300, 312], "label": "drone", "frame_id": "25", "frame_idx": 25, "interpolated": true, "interpolated_from": [20, 30]}], "26": [{"bbox": [285, 297, 300, 312], "label": "drone", "frame_id": "26", "frame_idx": 26, "interpolated": true, "interpolated_from": [20, 30]}], "27": [{"bbox": [285, 297, 300, 312], "label": "drone", "frame_id": "27", "frame_idx": 27, "interpolated": true, "interpolated_from": [20, 30]}], "28": [{"bbox": [285, 297, 300, 312], "label": "drone", "frame_id": "28", "frame_idx": 28, "interpolated": true, "interpolated_from": [20, 30]}], "29": [{"bbox": [285, 297, 300, 312], "label": "drone", "frame_id": "29", "frame_idx": 29, "interpolated": true, "interpolated_from": [20, 30]}], "31": [{"bbox": [276, 277, 291, 291], "label": "drone", "frame_id": "31", "frame_idx": 31, "interpolated": true, "interpolated_from": [30, 40]}], "32": [{"bbox": [267, 257, 282, 271], "label": "drone", "frame_id": "32", "frame_idx": 32, "interpolated": true, "interpolated_from": [30, 40]}], "33": [{"bbox": [258, 237, 273, 251], "label": "drone", "frame_id": "33", "frame_idx": 33, "interpolated": true, "interpolated_from": [30, 40]}], "34": [{"bbox": [250, 218, 264, 231], "label": "drone", "frame_id": "34", "frame_idx": 34, "interpolated": true, "interpolated_from": [30, 40]}], "35": [{"bbox": [241, 198, 255, 211], "label": "drone", "frame_id": "35", "frame_idx": 35, "interpolated": true, "interpolated_from": [30, 40]}], "36": [{"bbox": [232, 178, 246, 190], "label": "drone", "frame_id": "36", "frame_idx": 36, "interpolated": true, "interpolated_from": [30, 40]}], "37": [{"bbox": [224, 159, 237, 170], "label": "drone", "frame_id": "37", "frame_idx": 37, "interpolated": true, "interpolated_from": [30, 40]}], "38": [{"bbox": [215, 139, 228, 150], "label": "drone", "frame_id": "38", "frame_idx": 38, "interpolated": true, "interpolated_from": [30, 40]}], "39": [{"bbox": [206, 119, 219, 130], "label": "drone", "frame_id": "39", "frame_idx": 39, "interpolated": true, "interpolated_from": [30, 40]}], "41": [{"bbox": [188, 105, 200, 115], "label": "drone", "frame_id": "41", "frame_idx": 41, "interpolated": true, "interpolated_from": [40, 50]}], "42": [{"bbox": [178, 110, 190, 120], "label": "drone", "frame_id": "42", "frame_idx": 42, "interpolated": true, "interpolated_from": [40, 50]}], "43": [{"bbox": [168, 115, 180, 125], "label": "drone", "frame_id": "43", "frame_idx": 43, "interpolated": true, "interpolated_from": [40, 50]}], "44": [{"bbox": [158, 120, 170, 130], "label": "drone", "frame_id": "44", "frame_idx": 44, "interpolated": true, "interpolated_from": [40, 50]}], "45": [{"bbox": [149, 125, 160, 135], "label": "drone", "frame_id": "45", "frame_idx": 45, "interpolated": true, "interpolated_from": [40, 50]}], "46": [{"bbox": [139, 130, 150, 140], "label": "drone", "frame_id": "46", "frame_idx": 46, "interpolated": true, "interpolated_from": [40, 50]}], "47": [{"bbox": [129, 135, 140, 145], "label": "drone", "frame_id": "47", "frame_idx": 47, "interpolated": true, "interpolated_from": [40, 50]}], "48": [{"bbox": [119, 140, 130, 150], "label": "drone", "frame_id": "48", "frame_idx": 48, "interpolated": true, "interpolated_from": [40, 50]}], "49": [{"bbox": [109, 145, 120, 155], "label": "drone", "frame_id": "49", "frame_idx": 49, "interpolated": true, "interpolated_from": [40, 50]}], "51": [{"bbox": [110, 155, 120, 165], "label": "drone", "frame_id": "51", "frame_idx": 51, "interpolated": true, "interpolated_from": [50, 60]}], "52": [{"bbox": [120, 160, 130, 170], "label": "drone", "frame_id": "52", "frame_idx": 52, "interpolated": true, "interpolated_from": [50, 60]}], "53": [{"bbox": [130, 165, 140, 175], "label": "drone", "frame_id": "53", "frame_idx": 53, "interpolated": true, "interpolated_from": [50, 60]}], "54": [{"bbox": [140, 170, 150, 180], "label": "drone", "frame_id": "54", "frame_idx": 54, "interpolated": true, "interpolated_from": [50, 60]}], "55": [{"bbox": [150, 175, 160, 185], "label": "drone", "frame_id": "55", "frame_idx": 55, "interpolated": true, "interpolated_from": [50, 60]}], "56": [{"bbox": [160, 180, 170, 190], "label": "drone", "frame_id": "56", "frame_idx": 56, "interpolated": true, "interpolated_from": [50, 60]}], "57": [{"bbox": [170, 185, 180, 195], "label": "drone", "frame_id": "57", "frame_idx": 57, "interpolated": true, "interpolated_from": [50, 60]}], "58": [{"bbox": [180, 190, 190, 200], "label": "drone", "frame_id": "58", "frame_idx": 58, "interpolated": true, "interpolated_from": [50, 60]}], "59": [{"bbox": [190, 195, 200, 205], "label": "drone", "frame_id": "59", "frame_idx": 59, "interpolated": true, "interpolated_from": [50, 60]}], "61": [{"bbox": [190, 205, 200, 215], "label": "drone", "frame_id": "61", "frame_idx": 61, "interpolated": true, "interpolated_from": [60, 70]}], "62": [{"bbox": [180, 210, 190, 220], "label": "drone", "frame_id": "62", "frame_idx": 62, "interpolated": true, "interpolated_from": [60, 70]}], "63": [{"bbox": [170, 215, 180, 225], "label": "drone", "frame_id": "63", "frame_idx": 63, "interpolated": true, "interpolated_from": [60, 70]}], "64": [{"bbox": [160, 220, 170, 230], "label": "drone", "frame_id": "64", "frame_idx": 64, "interpolated": true, "interpolated_from": [60, 70]}], "65": [{"bbox": [150, 225, 160, 235], "label": "drone", "frame_id": "65", "frame_idx": 65, "interpolated": true, "interpolated_from": [60, 70]}], "66": [{"bbox": [140, 230, 150, 240], "label": "drone", "frame_id": "66", "frame_idx": 66, "interpolated": true, "interpolated_from": [60, 70]}], "67": [{"bbox": [130, 235, 140, 245], "label": "drone", "frame_id": "67", "frame_idx": 67, "interpolated": true, "interpolated_from": [60, 70]}], "68": [{"bbox": [120, 240, 130, 250], "label": "drone", "frame_id": "68", "frame_idx": 68, "interpolated": true, "interpolated_from": [60, 70]}], "69": [{"bbox": [110, 245, 120, 255], "label": "drone", "frame_id": "69", "frame_idx": 69, "interpolated": true, "interpolated_from": [60, 70]}], "71": [{"bbox": [100, 250, 110, 260], "label": "drone", "frame_id": "71", "frame_idx": 71, "interpolated": true, "interpolated_from": [70]}], "72": [{"bbox": [100, 250, 110, 260], "label": "drone", "frame_id": "72", "frame_idx": 72, "interpolated": true, "interpolated_from": [70]}], "73": [{"bbox": [100, 250, 110, 260], "label": "drone", "frame_id": "73", "frame_idx": 73, "interpolated": true, "interpolated_from": [70]}], "74": [{"bbox": [100, 250, 110, 260], "label": "drone", "frame_id": "74", "frame_idx": 74, "interpolated": true, "interpolated_from": [70]}]}}}