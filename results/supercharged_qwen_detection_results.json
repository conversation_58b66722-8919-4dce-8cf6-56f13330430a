{"data01": {"sequence_id": "data01", "total_frames": 75, "key_frames": 8, "total_detections": 221, "direct_detections": 20, "interpolated_detections": 201, "metrics": {"precision": 0.0, "recall": 0.0, "f1": 0, "tp": 0, "fp": 221, "fn": 75}, "detections": {"0": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "0", "frame_idx": 0}, {"bbox": [96, 80, 104, 88], "label": "小目标", "frame_idx": 0, "frame_id": "0"}], "10": [{"bbox": [98, 127, 103, 132], "label": "drone", "frame_id": "10", "frame_idx": 10}, {"bbox": [1, 1, 2, 2], "label": "drone", "frame_idx": 10, "frame_id": "10"}, {"bbox": [187, 192, 197, 202], "label": "drone", "frame_idx": 10, "frame_id": "10"}, {"bbox": [97, 80, 102, 85], "label": "小目标", "frame_idx": 10, "frame_id": "10"}], "20": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "20", "frame_idx": 20}, {"bbox": [60, 97, 65, 102], "label": "小目标", "frame_idx": 20, "frame_id": "20"}], "30": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "30", "frame_idx": 30}, {"bbox": [67, 83, 72, 88], "label": "drone", "frame_idx": 30, "frame_id": "30"}, {"bbox": [1, 2, 3, 4], "label": "drone", "frame_idx": 30, "frame_id": "30"}, {"bbox": [60, 84, 65, 90], "label": "小目标", "frame_idx": 30, "frame_id": "30"}], "40": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "40", "frame_idx": 40}, {"bbox": [76, 93, 81, 98], "label": "drone", "frame_idx": 40, "frame_id": "40"}, {"bbox": [60, 94, 70, 104], "label": "小目标", "frame_idx": 40, "frame_id": "40"}], "50": [{"bbox": [97, 80, 102, 85], "label": "drone", "frame_id": "50", "frame_idx": 50}, {"bbox": [69, 47, 80, 58], "label": "小目标", "frame_idx": 50, "frame_id": "50"}], "60": [{"bbox": [98, 70, 103, 75], "label": "drone", "frame_id": "60", "frame_idx": 60}], "70": [{"bbox": [98, 100, 103, 105], "label": "drone", "frame_id": "70", "frame_idx": 70}, {"bbox": [76, 94, 81, 99], "label": "drone", "frame_idx": 70, "frame_id": "70"}], "1": [{"bbox": [97, 79, 102, 84], "label": "drone", "frame_id": "1", "frame_idx": 1, "interpolated": true, "trajectory_based": true}, {"bbox": [60, 97, 65, 102], "label": "小目标", "frame_id": "1", "frame_idx": 1, "interpolated": true, "extrapolated": true}, {"bbox": [67, 83, 72, 88], "label": "drone", "frame_id": "1", "frame_idx": 1, "interpolated": true, "extrapolated": true}], "2": [{"bbox": [97, 79, 102, 84], "label": "drone", "frame_id": "2", "frame_idx": 2, "interpolated": true, "trajectory_based": true}, {"bbox": [60, 97, 65, 102], "label": "小目标", "frame_id": "2", "frame_idx": 2, "interpolated": true, "extrapolated": true}, {"bbox": [67, 83, 72, 88], "label": "drone", "frame_id": "2", "frame_idx": 2, "interpolated": true, "extrapolated": true}], "3": [{"bbox": [97, 79, 102, 84], "label": "drone", "frame_id": "3", "frame_idx": 3, "interpolated": true, "trajectory_based": true}, {"bbox": [60, 97, 65, 102], "label": "小目标", "frame_id": "3", "frame_idx": 3, "interpolated": true, "extrapolated": true}, {"bbox": [67, 83, 72, 88], "label": "drone", "frame_id": "3", "frame_idx": 3, "interpolated": true, "extrapolated": true}], "4": [{"bbox": [97, 79, 102, 84], "label": "drone", "frame_id": "4", "frame_idx": 4, "interpolated": true, "trajectory_based": true}, {"bbox": [60, 97, 65, 102], "label": "小目标", "frame_id": "4", "frame_idx": 4, "interpolated": true, "extrapolated": true}, {"bbox": [67, 83, 72, 88], "label": "drone", "frame_id": "4", "frame_idx": 4, "interpolated": true, "extrapolated": true}], "5": [{"bbox": [97, 79, 102, 84], "label": "drone", "frame_id": "5", "frame_idx": 5, "interpolated": true, "trajectory_based": true}, {"bbox": [60, 97, 65, 102], "label": "小目标", "frame_id": "5", "frame_idx": 5, "interpolated": true, "extrapolated": true}, {"bbox": [67, 83, 72, 88], "label": "drone", "frame_id": "5", "frame_idx": 5, "interpolated": true, "extrapolated": true}], "6": [{"bbox": [97, 79, 102, 84], "label": "drone", "frame_id": "6", "frame_idx": 6, "interpolated": true, "trajectory_based": true}, {"bbox": [60, 97, 65, 102], "label": "小目标", "frame_id": "6", "frame_idx": 6, "interpolated": true, "extrapolated": true}, {"bbox": [67, 83, 72, 88], "label": "drone", "frame_id": "6", "frame_idx": 6, "interpolated": true, "extrapolated": true}], "7": [{"bbox": [97, 79, 102, 84], "label": "drone", "frame_id": "7", "frame_idx": 7, "interpolated": true, "trajectory_based": true}, {"bbox": [60, 97, 65, 102], "label": "小目标", "frame_id": "7", "frame_idx": 7, "interpolated": true, "extrapolated": true}, {"bbox": [67, 83, 72, 88], "label": "drone", "frame_id": "7", "frame_idx": 7, "interpolated": true, "extrapolated": true}], "8": [{"bbox": [97, 79, 102, 84], "label": "drone", "frame_id": "8", "frame_idx": 8, "interpolated": true, "trajectory_based": true}, {"bbox": [60, 97, 65, 102], "label": "小目标", "frame_id": "8", "frame_idx": 8, "interpolated": true, "extrapolated": true}, {"bbox": [67, 83, 72, 88], "label": "drone", "frame_id": "8", "frame_idx": 8, "interpolated": true, "extrapolated": true}], "9": [{"bbox": [97, 79, 102, 84], "label": "drone", "frame_id": "9", "frame_idx": 9, "interpolated": true, "trajectory_based": true}, {"bbox": [60, 97, 65, 102], "label": "小目标", "frame_id": "9", "frame_idx": 9, "interpolated": true, "extrapolated": true}, {"bbox": [67, 83, 72, 88], "label": "drone", "frame_id": "9", "frame_idx": 9, "interpolated": true, "extrapolated": true}], "11": [{"bbox": [97, 79, 102, 84], "label": "小目标", "frame_id": "11", "frame_idx": 11, "interpolated": true, "trajectory_based": true}, {"bbox": [60, 97, 65, 102], "label": "小目标", "frame_id": "11", "frame_idx": 11, "interpolated": true, "extrapolated": true}, {"bbox": [67, 83, 72, 88], "label": "drone", "frame_id": "11", "frame_idx": 11, "interpolated": true, "extrapolated": true}], "12": [{"bbox": [97, 79, 102, 84], "label": "小目标", "frame_id": "12", "frame_idx": 12, "interpolated": true, "trajectory_based": true}, {"bbox": [60, 97, 65, 102], "label": "小目标", "frame_id": "12", "frame_idx": 12, "interpolated": true, "extrapolated": true}, {"bbox": [67, 83, 72, 88], "label": "drone", "frame_id": "12", "frame_idx": 12, "interpolated": true, "extrapolated": true}], "13": [{"bbox": [97, 79, 102, 84], "label": "小目标", "frame_id": "13", "frame_idx": 13, "interpolated": true, "trajectory_based": true}, {"bbox": [60, 97, 65, 102], "label": "小目标", "frame_id": "13", "frame_idx": 13, "interpolated": true, "extrapolated": true}, {"bbox": [67, 83, 72, 88], "label": "drone", "frame_id": "13", "frame_idx": 13, "interpolated": true, "extrapolated": true}], "14": [{"bbox": [97, 79, 102, 84], "label": "小目标", "frame_id": "14", "frame_idx": 14, "interpolated": true, "trajectory_based": true}, {"bbox": [60, 97, 65, 102], "label": "小目标", "frame_id": "14", "frame_idx": 14, "interpolated": true, "extrapolated": true}, {"bbox": [67, 83, 72, 88], "label": "drone", "frame_id": "14", "frame_idx": 14, "interpolated": true, "extrapolated": true}], "15": [{"bbox": [97, 79, 102, 84], "label": "小目标", "frame_id": "15", "frame_idx": 15, "interpolated": true, "trajectory_based": true}, {"bbox": [60, 97, 65, 102], "label": "小目标", "frame_id": "15", "frame_idx": 15, "interpolated": true, "extrapolated": true}, {"bbox": [67, 83, 72, 88], "label": "drone", "frame_id": "15", "frame_idx": 15, "interpolated": true, "extrapolated": true}], "16": [{"bbox": [97, 78, 102, 83], "label": "小目标", "frame_id": "16", "frame_idx": 16, "interpolated": true, "trajectory_based": true}, {"bbox": [60, 97, 65, 102], "label": "小目标", "frame_id": "16", "frame_idx": 16, "interpolated": true, "extrapolated": true}, {"bbox": [67, 83, 72, 88], "label": "drone", "frame_id": "16", "frame_idx": 16, "interpolated": true, "extrapolated": true}], "17": [{"bbox": [97, 78, 102, 83], "label": "小目标", "frame_id": "17", "frame_idx": 17, "interpolated": true, "trajectory_based": true}, {"bbox": [60, 97, 65, 102], "label": "小目标", "frame_id": "17", "frame_idx": 17, "interpolated": true, "extrapolated": true}, {"bbox": [67, 83, 72, 88], "label": "drone", "frame_id": "17", "frame_idx": 17, "interpolated": true, "extrapolated": true}], "18": [{"bbox": [97, 78, 102, 83], "label": "小目标", "frame_id": "18", "frame_idx": 18, "interpolated": true, "trajectory_based": true}, {"bbox": [60, 97, 65, 102], "label": "小目标", "frame_id": "18", "frame_idx": 18, "interpolated": true, "extrapolated": true}, {"bbox": [67, 83, 72, 88], "label": "drone", "frame_id": "18", "frame_idx": 18, "interpolated": true, "extrapolated": true}], "19": [{"bbox": [97, 78, 102, 83], "label": "小目标", "frame_id": "19", "frame_idx": 19, "interpolated": true, "trajectory_based": true}, {"bbox": [60, 97, 65, 102], "label": "小目标", "frame_id": "19", "frame_idx": 19, "interpolated": true, "extrapolated": true}, {"bbox": [67, 83, 72, 88], "label": "drone", "frame_id": "19", "frame_idx": 19, "interpolated": true, "extrapolated": true}], "21": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "21", "frame_idx": 21, "interpolated": true, "trajectory_based": true}, {"bbox": [60, 95, 65, 100], "label": "小目标", "frame_id": "21", "frame_idx": 21, "interpolated": true, "trajectory_based": true}, {"bbox": [67, 83, 72, 88], "label": "drone", "frame_id": "21", "frame_idx": 21, "interpolated": true, "extrapolated": true}], "22": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "22", "frame_idx": 22, "interpolated": true, "trajectory_based": true}, {"bbox": [60, 94, 65, 99], "label": "小目标", "frame_id": "22", "frame_idx": 22, "interpolated": true, "trajectory_based": true}, {"bbox": [67, 83, 72, 88], "label": "drone", "frame_id": "22", "frame_idx": 22, "interpolated": true, "extrapolated": true}], "23": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "23", "frame_idx": 23, "interpolated": true, "trajectory_based": true}, {"bbox": [60, 93, 65, 98], "label": "小目标", "frame_id": "23", "frame_idx": 23, "interpolated": true, "trajectory_based": true}, {"bbox": [67, 83, 72, 88], "label": "drone", "frame_id": "23", "frame_idx": 23, "interpolated": true, "extrapolated": true}], "24": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "24", "frame_idx": 24, "interpolated": true, "trajectory_based": true}, {"bbox": [60, 91, 65, 97], "label": "小目标", "frame_id": "24", "frame_idx": 24, "interpolated": true, "trajectory_based": true}, {"bbox": [67, 83, 72, 88], "label": "drone", "frame_id": "24", "frame_idx": 24, "interpolated": true, "extrapolated": true}], "25": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "25", "frame_idx": 25, "interpolated": true, "trajectory_based": true}, {"bbox": [60, 90, 65, 96], "label": "小目标", "frame_id": "25", "frame_idx": 25, "interpolated": true, "trajectory_based": true}, {"bbox": [67, 83, 72, 88], "label": "drone", "frame_id": "25", "frame_idx": 25, "interpolated": true, "extrapolated": true}], "26": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "26", "frame_idx": 26, "interpolated": true, "trajectory_based": true}, {"bbox": [60, 89, 65, 94], "label": "小目标", "frame_id": "26", "frame_idx": 26, "interpolated": true, "trajectory_based": true}, {"bbox": [67, 83, 72, 88], "label": "drone", "frame_id": "26", "frame_idx": 26, "interpolated": true, "extrapolated": true}], "27": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "27", "frame_idx": 27, "interpolated": true, "trajectory_based": true}, {"bbox": [60, 87, 65, 93], "label": "小目标", "frame_id": "27", "frame_idx": 27, "interpolated": true, "trajectory_based": true}, {"bbox": [67, 83, 72, 88], "label": "drone", "frame_id": "27", "frame_idx": 27, "interpolated": true, "extrapolated": true}], "28": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "28", "frame_idx": 28, "interpolated": true, "trajectory_based": true}, {"bbox": [60, 86, 65, 92], "label": "小目标", "frame_id": "28", "frame_idx": 28, "interpolated": true, "trajectory_based": true}, {"bbox": [67, 83, 72, 88], "label": "drone", "frame_id": "28", "frame_idx": 28, "interpolated": true, "extrapolated": true}], "29": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "29", "frame_idx": 29, "interpolated": true, "trajectory_based": true}, {"bbox": [60, 85, 65, 91], "label": "小目标", "frame_id": "29", "frame_idx": 29, "interpolated": true, "trajectory_based": true}, {"bbox": [67, 83, 72, 88], "label": "drone", "frame_id": "29", "frame_idx": 29, "interpolated": true, "extrapolated": true}], "31": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "31", "frame_idx": 31, "interpolated": true, "trajectory_based": true}, {"bbox": [60, 85, 65, 91], "label": "小目标", "frame_id": "31", "frame_idx": 31, "interpolated": true, "trajectory_based": true}, {"bbox": [67, 84, 72, 89], "label": "drone", "frame_id": "31", "frame_idx": 31, "interpolated": true, "trajectory_based": true}], "32": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "32", "frame_idx": 32, "interpolated": true, "trajectory_based": true}, {"bbox": [60, 86, 66, 92], "label": "小目标", "frame_id": "32", "frame_idx": 32, "interpolated": true, "trajectory_based": true}, {"bbox": [68, 85, 73, 90], "label": "drone", "frame_id": "32", "frame_idx": 32, "interpolated": true, "trajectory_based": true}], "33": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "33", "frame_idx": 33, "interpolated": true, "trajectory_based": true}, {"bbox": [60, 87, 66, 94], "label": "小目标", "frame_id": "33", "frame_idx": 33, "interpolated": true, "trajectory_based": true}, {"bbox": [69, 86, 74, 91], "label": "drone", "frame_id": "33", "frame_idx": 33, "interpolated": true, "trajectory_based": true}], "34": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "34", "frame_idx": 34, "interpolated": true, "trajectory_based": true}, {"bbox": [60, 88, 67, 95], "label": "小目标", "frame_id": "34", "frame_idx": 34, "interpolated": true, "trajectory_based": true}, {"bbox": [70, 87, 75, 92], "label": "drone", "frame_id": "34", "frame_idx": 34, "interpolated": true, "trajectory_based": true}], "35": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "35", "frame_idx": 35, "interpolated": true, "trajectory_based": true}, {"bbox": [60, 89, 67, 97], "label": "小目标", "frame_id": "35", "frame_idx": 35, "interpolated": true, "trajectory_based": true}, {"bbox": [71, 88, 76, 93], "label": "drone", "frame_id": "35", "frame_idx": 35, "interpolated": true, "trajectory_based": true}], "36": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "36", "frame_idx": 36, "interpolated": true, "trajectory_based": true}, {"bbox": [60, 90, 68, 98], "label": "小目标", "frame_id": "36", "frame_idx": 36, "interpolated": true, "trajectory_based": true}, {"bbox": [72, 89, 77, 94], "label": "drone", "frame_id": "36", "frame_idx": 36, "interpolated": true, "trajectory_based": true}], "37": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "37", "frame_idx": 37, "interpolated": true, "trajectory_based": true}, {"bbox": [60, 91, 68, 99], "label": "小目标", "frame_id": "37", "frame_idx": 37, "interpolated": true, "trajectory_based": true}, {"bbox": [73, 90, 78, 95], "label": "drone", "frame_id": "37", "frame_idx": 37, "interpolated": true, "trajectory_based": true}], "38": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "38", "frame_idx": 38, "interpolated": true, "trajectory_based": true}, {"bbox": [60, 92, 69, 101], "label": "小目标", "frame_id": "38", "frame_idx": 38, "interpolated": true, "trajectory_based": true}, {"bbox": [74, 91, 79, 96], "label": "drone", "frame_id": "38", "frame_idx": 38, "interpolated": true, "trajectory_based": true}], "39": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "39", "frame_idx": 39, "interpolated": true, "trajectory_based": true}, {"bbox": [60, 93, 69, 102], "label": "小目标", "frame_id": "39", "frame_idx": 39, "interpolated": true, "trajectory_based": true}, {"bbox": [75, 92, 80, 97], "label": "drone", "frame_id": "39", "frame_idx": 39, "interpolated": true, "trajectory_based": true}], "41": [{"bbox": [97, 78, 102, 83], "label": "drone", "frame_id": "41", "frame_idx": 41, "interpolated": true, "trajectory_based": true}, {"bbox": [60, 89, 71, 99], "label": "小目标", "frame_id": "41", "frame_idx": 41, "interpolated": true, "trajectory_based": true}, {"bbox": [76, 93, 81, 98], "label": "drone", "frame_id": "41", "frame_idx": 41, "interpolated": true, "extrapolated": true}], "42": [{"bbox": [97, 78, 102, 83], "label": "drone", "frame_id": "42", "frame_idx": 42, "interpolated": true, "trajectory_based": true}, {"bbox": [61, 84, 72, 94], "label": "小目标", "frame_id": "42", "frame_idx": 42, "interpolated": true, "trajectory_based": true}, {"bbox": [76, 93, 81, 98], "label": "drone", "frame_id": "42", "frame_idx": 42, "interpolated": true, "extrapolated": true}], "43": [{"bbox": [97, 78, 102, 83], "label": "drone", "frame_id": "43", "frame_idx": 43, "interpolated": true, "trajectory_based": true}, {"bbox": [62, 79, 73, 90], "label": "小目标", "frame_id": "43", "frame_idx": 43, "interpolated": true, "trajectory_based": true}, {"bbox": [76, 93, 81, 98], "label": "drone", "frame_id": "43", "frame_idx": 43, "interpolated": true, "extrapolated": true}], "44": [{"bbox": [97, 78, 102, 83], "label": "drone", "frame_id": "44", "frame_idx": 44, "interpolated": true, "trajectory_based": true}, {"bbox": [63, 75, 74, 85], "label": "小目标", "frame_id": "44", "frame_idx": 44, "interpolated": true, "trajectory_based": true}, {"bbox": [76, 93, 81, 98], "label": "drone", "frame_id": "44", "frame_idx": 44, "interpolated": true, "extrapolated": true}], "45": [{"bbox": [97, 79, 102, 84], "label": "drone", "frame_id": "45", "frame_idx": 45, "interpolated": true, "trajectory_based": true}, {"bbox": [64, 70, 75, 81], "label": "小目标", "frame_id": "45", "frame_idx": 45, "interpolated": true, "trajectory_based": true}, {"bbox": [76, 93, 81, 98], "label": "drone", "frame_id": "45", "frame_idx": 45, "interpolated": true, "extrapolated": true}], "46": [{"bbox": [97, 79, 102, 84], "label": "drone", "frame_id": "46", "frame_idx": 46, "interpolated": true, "trajectory_based": true}, {"bbox": [65, 65, 76, 76], "label": "小目标", "frame_id": "46", "frame_idx": 46, "interpolated": true, "trajectory_based": true}, {"bbox": [76, 93, 81, 98], "label": "drone", "frame_id": "46", "frame_idx": 46, "interpolated": true, "extrapolated": true}], "47": [{"bbox": [97, 79, 102, 84], "label": "drone", "frame_id": "47", "frame_idx": 47, "interpolated": true, "trajectory_based": true}, {"bbox": [66, 61, 77, 71], "label": "小目标", "frame_id": "47", "frame_idx": 47, "interpolated": true, "trajectory_based": true}, {"bbox": [76, 93, 81, 98], "label": "drone", "frame_id": "47", "frame_idx": 47, "interpolated": true, "extrapolated": true}], "48": [{"bbox": [97, 79, 102, 84], "label": "drone", "frame_id": "48", "frame_idx": 48, "interpolated": true, "trajectory_based": true}, {"bbox": [67, 56, 78, 67], "label": "小目标", "frame_id": "48", "frame_idx": 48, "interpolated": true, "trajectory_based": true}, {"bbox": [76, 93, 81, 98], "label": "drone", "frame_id": "48", "frame_idx": 48, "interpolated": true, "extrapolated": true}], "49": [{"bbox": [97, 79, 102, 84], "label": "drone", "frame_id": "49", "frame_idx": 49, "interpolated": true, "trajectory_based": true}, {"bbox": [68, 51, 79, 62], "label": "小目标", "frame_id": "49", "frame_idx": 49, "interpolated": true, "trajectory_based": true}, {"bbox": [76, 93, 81, 98], "label": "drone", "frame_id": "49", "frame_idx": 49, "interpolated": true, "extrapolated": true}], "51": [{"bbox": [97, 79, 102, 84], "label": "drone", "frame_id": "51", "frame_idx": 51, "interpolated": true, "trajectory_based": true}, {"bbox": [69, 47, 80, 58], "label": "小目标", "frame_id": "51", "frame_idx": 51, "interpolated": true, "extrapolated": true}, {"bbox": [76, 93, 81, 98], "label": "drone", "frame_id": "51", "frame_idx": 51, "interpolated": true, "extrapolated": true}], "52": [{"bbox": [97, 78, 102, 83], "label": "drone", "frame_id": "52", "frame_idx": 52, "interpolated": true, "trajectory_based": true}, {"bbox": [69, 47, 80, 58], "label": "小目标", "frame_id": "52", "frame_idx": 52, "interpolated": true, "extrapolated": true}, {"bbox": [76, 93, 81, 98], "label": "drone", "frame_id": "52", "frame_idx": 52, "interpolated": true, "extrapolated": true}], "53": [{"bbox": [97, 77, 102, 82], "label": "drone", "frame_id": "53", "frame_idx": 53, "interpolated": true, "trajectory_based": true}, {"bbox": [69, 47, 80, 58], "label": "小目标", "frame_id": "53", "frame_idx": 53, "interpolated": true, "extrapolated": true}, {"bbox": [76, 93, 81, 98], "label": "drone", "frame_id": "53", "frame_idx": 53, "interpolated": true, "extrapolated": true}], "54": [{"bbox": [97, 76, 102, 81], "label": "drone", "frame_id": "54", "frame_idx": 54, "interpolated": true, "trajectory_based": true}, {"bbox": [69, 47, 80, 58], "label": "小目标", "frame_id": "54", "frame_idx": 54, "interpolated": true, "extrapolated": true}, {"bbox": [76, 93, 81, 98], "label": "drone", "frame_id": "54", "frame_idx": 54, "interpolated": true, "extrapolated": true}], "55": [{"bbox": [97, 75, 102, 80], "label": "drone", "frame_id": "55", "frame_idx": 55, "interpolated": true, "trajectory_based": true}, {"bbox": [69, 47, 80, 58], "label": "小目标", "frame_id": "55", "frame_idx": 55, "interpolated": true, "extrapolated": true}, {"bbox": [76, 93, 81, 98], "label": "drone", "frame_id": "55", "frame_idx": 55, "interpolated": true, "extrapolated": true}], "56": [{"bbox": [97, 74, 102, 79], "label": "drone", "frame_id": "56", "frame_idx": 56, "interpolated": true, "trajectory_based": true}, {"bbox": [69, 47, 80, 58], "label": "小目标", "frame_id": "56", "frame_idx": 56, "interpolated": true, "extrapolated": true}, {"bbox": [76, 93, 81, 98], "label": "drone", "frame_id": "56", "frame_idx": 56, "interpolated": true, "extrapolated": true}], "57": [{"bbox": [97, 73, 102, 78], "label": "drone", "frame_id": "57", "frame_idx": 57, "interpolated": true, "trajectory_based": true}, {"bbox": [69, 47, 80, 58], "label": "小目标", "frame_id": "57", "frame_idx": 57, "interpolated": true, "extrapolated": true}, {"bbox": [76, 93, 81, 98], "label": "drone", "frame_id": "57", "frame_idx": 57, "interpolated": true, "extrapolated": true}], "58": [{"bbox": [97, 72, 102, 77], "label": "drone", "frame_id": "58", "frame_idx": 58, "interpolated": true, "trajectory_based": true}, {"bbox": [69, 47, 80, 58], "label": "小目标", "frame_id": "58", "frame_idx": 58, "interpolated": true, "extrapolated": true}, {"bbox": [76, 93, 81, 98], "label": "drone", "frame_id": "58", "frame_idx": 58, "interpolated": true, "extrapolated": true}], "59": [{"bbox": [97, 71, 102, 76], "label": "drone", "frame_id": "59", "frame_idx": 59, "interpolated": true, "trajectory_based": true}, {"bbox": [69, 47, 80, 58], "label": "小目标", "frame_id": "59", "frame_idx": 59, "interpolated": true, "extrapolated": true}, {"bbox": [76, 93, 81, 98], "label": "drone", "frame_id": "59", "frame_idx": 59, "interpolated": true, "extrapolated": true}], "61": [{"bbox": [98, 73, 103, 78], "label": "drone", "frame_id": "61", "frame_idx": 61, "interpolated": true, "trajectory_based": true}, {"bbox": [69, 47, 80, 58], "label": "小目标", "frame_id": "61", "frame_idx": 61, "interpolated": true, "extrapolated": true}, {"bbox": [76, 93, 81, 98], "label": "drone", "frame_id": "61", "frame_idx": 61, "interpolated": true, "extrapolated": true}], "62": [{"bbox": [98, 76, 103, 81], "label": "drone", "frame_id": "62", "frame_idx": 62, "interpolated": true, "trajectory_based": true}, {"bbox": [69, 47, 80, 58], "label": "小目标", "frame_id": "62", "frame_idx": 62, "interpolated": true, "extrapolated": true}, {"bbox": [76, 93, 81, 98], "label": "drone", "frame_id": "62", "frame_idx": 62, "interpolated": true, "extrapolated": true}], "63": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "63", "frame_idx": 63, "interpolated": true, "trajectory_based": true}, {"bbox": [69, 47, 80, 58], "label": "小目标", "frame_id": "63", "frame_idx": 63, "interpolated": true, "extrapolated": true}, {"bbox": [76, 93, 81, 98], "label": "drone", "frame_id": "63", "frame_idx": 63, "interpolated": true, "extrapolated": true}], "64": [{"bbox": [98, 82, 103, 87], "label": "drone", "frame_id": "64", "frame_idx": 64, "interpolated": true, "trajectory_based": true}, {"bbox": [69, 47, 80, 58], "label": "小目标", "frame_id": "64", "frame_idx": 64, "interpolated": true, "extrapolated": true}, {"bbox": [76, 93, 81, 98], "label": "drone", "frame_id": "64", "frame_idx": 64, "interpolated": true, "extrapolated": true}], "65": [{"bbox": [98, 85, 103, 90], "label": "drone", "frame_id": "65", "frame_idx": 65, "interpolated": true, "trajectory_based": true}, {"bbox": [69, 47, 80, 58], "label": "小目标", "frame_id": "65", "frame_idx": 65, "interpolated": true, "extrapolated": true}, {"bbox": [76, 93, 81, 98], "label": "drone", "frame_id": "65", "frame_idx": 65, "interpolated": true, "extrapolated": true}], "66": [{"bbox": [98, 88, 103, 93], "label": "drone", "frame_id": "66", "frame_idx": 66, "interpolated": true, "trajectory_based": true}, {"bbox": [69, 47, 80, 58], "label": "小目标", "frame_id": "66", "frame_idx": 66, "interpolated": true, "extrapolated": true}, {"bbox": [76, 93, 81, 98], "label": "drone", "frame_id": "66", "frame_idx": 66, "interpolated": true, "extrapolated": true}], "67": [{"bbox": [98, 91, 103, 96], "label": "drone", "frame_id": "67", "frame_idx": 67, "interpolated": true, "trajectory_based": true}, {"bbox": [69, 47, 80, 58], "label": "小目标", "frame_id": "67", "frame_idx": 67, "interpolated": true, "extrapolated": true}, {"bbox": [76, 93, 81, 98], "label": "drone", "frame_id": "67", "frame_idx": 67, "interpolated": true, "extrapolated": true}], "68": [{"bbox": [98, 94, 103, 99], "label": "drone", "frame_id": "68", "frame_idx": 68, "interpolated": true, "trajectory_based": true}, {"bbox": [69, 47, 80, 58], "label": "小目标", "frame_id": "68", "frame_idx": 68, "interpolated": true, "extrapolated": true}, {"bbox": [76, 93, 81, 98], "label": "drone", "frame_id": "68", "frame_idx": 68, "interpolated": true, "extrapolated": true}], "69": [{"bbox": [98, 97, 103, 102], "label": "drone", "frame_id": "69", "frame_idx": 69, "interpolated": true, "trajectory_based": true}, {"bbox": [69, 47, 80, 58], "label": "小目标", "frame_id": "69", "frame_idx": 69, "interpolated": true, "extrapolated": true}, {"bbox": [76, 93, 81, 98], "label": "drone", "frame_id": "69", "frame_idx": 69, "interpolated": true, "extrapolated": true}], "71": [{"bbox": [98, 100, 103, 105], "label": "drone", "frame_id": "71", "frame_idx": 71, "interpolated": true, "extrapolated": true}, {"bbox": [69, 47, 80, 58], "label": "小目标", "frame_id": "71", "frame_idx": 71, "interpolated": true, "extrapolated": true}, {"bbox": [76, 93, 81, 98], "label": "drone", "frame_id": "71", "frame_idx": 71, "interpolated": true, "extrapolated": true}], "72": [{"bbox": [98, 100, 103, 105], "label": "drone", "frame_id": "72", "frame_idx": 72, "interpolated": true, "extrapolated": true}, {"bbox": [69, 47, 80, 58], "label": "小目标", "frame_id": "72", "frame_idx": 72, "interpolated": true, "extrapolated": true}, {"bbox": [76, 93, 81, 98], "label": "drone", "frame_id": "72", "frame_idx": 72, "interpolated": true, "extrapolated": true}], "73": [{"bbox": [98, 100, 103, 105], "label": "drone", "frame_id": "73", "frame_idx": 73, "interpolated": true, "extrapolated": true}, {"bbox": [69, 47, 80, 58], "label": "小目标", "frame_id": "73", "frame_idx": 73, "interpolated": true, "extrapolated": true}, {"bbox": [76, 93, 81, 98], "label": "drone", "frame_id": "73", "frame_idx": 73, "interpolated": true, "extrapolated": true}], "74": [{"bbox": [98, 100, 103, 105], "label": "drone", "frame_id": "74", "frame_idx": 74, "interpolated": true, "extrapolated": true}, {"bbox": [69, 47, 80, 58], "label": "小目标", "frame_id": "74", "frame_idx": 74, "interpolated": true, "extrapolated": true}, {"bbox": [76, 93, 81, 98], "label": "drone", "frame_id": "74", "frame_idx": 74, "interpolated": true, "extrapolated": true}]}}, "data02": {"sequence_id": "data02", "total_frames": 50, "key_frames": 5, "total_detections": 149, "direct_detections": 14, "interpolated_detections": 135, "metrics": {"precision": 0.0, "recall": 0.0, "f1": 0, "tp": 0, "fp": 149, "fn": 50}, "detections": {"0": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "0", "frame_idx": 0}, {"bbox": [67, 149, 72, 154], "label": "car", "frame_idx": 0, "frame_id": "0"}, {"bbox": [60, 94, 65, 99], "label": "小目标", "frame_idx": 0, "frame_id": "0"}], "10": [{"bbox": [97, 80, 102, 85], "label": "drone", "frame_id": "10", "frame_idx": 10}, {"bbox": [94, 80, 99, 85], "label": "小目标", "frame_idx": 10, "frame_id": "10"}], "20": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "20", "frame_idx": 20}, {"bbox": [69, 84, 74, 89], "label": "drone", "frame_idx": 20, "frame_id": "20"}, {"bbox": [90, 86, 95, 91], "label": "小目标", "frame_idx": 20, "frame_id": "20"}], "30": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "30", "frame_idx": 30}, {"bbox": [69, 84, 74, 89], "label": "drone", "frame_idx": 30, "frame_id": "30"}, {"bbox": [90, 84, 95, 89], "label": "小目标", "frame_idx": 30, "frame_id": "30"}], "40": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "40", "frame_idx": 40}, {"bbox": [69, 84, 74, 89], "label": "drone", "frame_idx": 40, "frame_id": "40"}, {"bbox": [96, 80, 104, 88], "label": "小目标", "frame_idx": 40, "frame_id": "40"}], "1": [{"bbox": [97, 78, 102, 83], "label": "drone", "frame_id": "1", "frame_idx": 1, "interpolated": true, "trajectory_based": true}, {"bbox": [63, 92, 68, 97], "label": "小目标", "frame_id": "1", "frame_idx": 1, "interpolated": true, "trajectory_based": true}, {"bbox": [69, 84, 74, 89], "label": "drone", "frame_id": "1", "frame_idx": 1, "interpolated": true, "extrapolated": true}], "2": [{"bbox": [97, 78, 102, 83], "label": "drone", "frame_id": "2", "frame_idx": 2, "interpolated": true, "trajectory_based": true}, {"bbox": [66, 91, 71, 96], "label": "小目标", "frame_id": "2", "frame_idx": 2, "interpolated": true, "trajectory_based": true}, {"bbox": [69, 84, 74, 89], "label": "drone", "frame_id": "2", "frame_idx": 2, "interpolated": true, "extrapolated": true}], "3": [{"bbox": [97, 78, 102, 83], "label": "drone", "frame_id": "3", "frame_idx": 3, "interpolated": true, "trajectory_based": true}, {"bbox": [70, 89, 75, 94], "label": "小目标", "frame_id": "3", "frame_idx": 3, "interpolated": true, "trajectory_based": true}, {"bbox": [69, 84, 74, 89], "label": "drone", "frame_id": "3", "frame_idx": 3, "interpolated": true, "extrapolated": true}], "4": [{"bbox": [97, 78, 102, 83], "label": "drone", "frame_id": "4", "frame_idx": 4, "interpolated": true, "trajectory_based": true}, {"bbox": [73, 88, 78, 93], "label": "小目标", "frame_id": "4", "frame_idx": 4, "interpolated": true, "trajectory_based": true}, {"bbox": [69, 84, 74, 89], "label": "drone", "frame_id": "4", "frame_idx": 4, "interpolated": true, "extrapolated": true}], "5": [{"bbox": [97, 79, 102, 84], "label": "drone", "frame_id": "5", "frame_idx": 5, "interpolated": true, "trajectory_based": true}, {"bbox": [77, 87, 82, 92], "label": "小目标", "frame_id": "5", "frame_idx": 5, "interpolated": true, "trajectory_based": true}, {"bbox": [69, 84, 74, 89], "label": "drone", "frame_id": "5", "frame_idx": 5, "interpolated": true, "extrapolated": true}], "6": [{"bbox": [97, 79, 102, 84], "label": "drone", "frame_id": "6", "frame_idx": 6, "interpolated": true, "trajectory_based": true}, {"bbox": [80, 85, 85, 90], "label": "小目标", "frame_id": "6", "frame_idx": 6, "interpolated": true, "trajectory_based": true}, {"bbox": [69, 84, 74, 89], "label": "drone", "frame_id": "6", "frame_idx": 6, "interpolated": true, "extrapolated": true}], "7": [{"bbox": [97, 79, 102, 84], "label": "drone", "frame_id": "7", "frame_idx": 7, "interpolated": true, "trajectory_based": true}, {"bbox": [83, 84, 88, 89], "label": "小目标", "frame_id": "7", "frame_idx": 7, "interpolated": true, "trajectory_based": true}, {"bbox": [69, 84, 74, 89], "label": "drone", "frame_id": "7", "frame_idx": 7, "interpolated": true, "extrapolated": true}], "8": [{"bbox": [97, 79, 102, 84], "label": "drone", "frame_id": "8", "frame_idx": 8, "interpolated": true, "trajectory_based": true}, {"bbox": [87, 82, 92, 87], "label": "小目标", "frame_id": "8", "frame_idx": 8, "interpolated": true, "trajectory_based": true}, {"bbox": [69, 84, 74, 89], "label": "drone", "frame_id": "8", "frame_idx": 8, "interpolated": true, "extrapolated": true}], "9": [{"bbox": [97, 79, 102, 84], "label": "drone", "frame_id": "9", "frame_idx": 9, "interpolated": true, "trajectory_based": true}, {"bbox": [90, 81, 95, 86], "label": "小目标", "frame_id": "9", "frame_idx": 9, "interpolated": true, "trajectory_based": true}, {"bbox": [69, 84, 74, 89], "label": "drone", "frame_id": "9", "frame_idx": 9, "interpolated": true, "extrapolated": true}], "11": [{"bbox": [97, 79, 102, 84], "label": "drone", "frame_id": "11", "frame_idx": 11, "interpolated": true, "trajectory_based": true}, {"bbox": [93, 80, 98, 85], "label": "小目标", "frame_id": "11", "frame_idx": 11, "interpolated": true, "trajectory_based": true}, {"bbox": [69, 84, 74, 89], "label": "drone", "frame_id": "11", "frame_idx": 11, "interpolated": true, "extrapolated": true}], "12": [{"bbox": [97, 79, 102, 84], "label": "drone", "frame_id": "12", "frame_idx": 12, "interpolated": true, "trajectory_based": true}, {"bbox": [93, 81, 98, 86], "label": "小目标", "frame_id": "12", "frame_idx": 12, "interpolated": true, "trajectory_based": true}, {"bbox": [69, 84, 74, 89], "label": "drone", "frame_id": "12", "frame_idx": 12, "interpolated": true, "extrapolated": true}], "13": [{"bbox": [97, 79, 102, 84], "label": "drone", "frame_id": "13", "frame_idx": 13, "interpolated": true, "trajectory_based": true}, {"bbox": [92, 81, 97, 86], "label": "小目标", "frame_id": "13", "frame_idx": 13, "interpolated": true, "trajectory_based": true}, {"bbox": [69, 84, 74, 89], "label": "drone", "frame_id": "13", "frame_idx": 13, "interpolated": true, "extrapolated": true}], "14": [{"bbox": [97, 79, 102, 84], "label": "drone", "frame_id": "14", "frame_idx": 14, "interpolated": true, "trajectory_based": true}, {"bbox": [92, 82, 97, 87], "label": "小目标", "frame_id": "14", "frame_idx": 14, "interpolated": true, "trajectory_based": true}, {"bbox": [69, 84, 74, 89], "label": "drone", "frame_id": "14", "frame_idx": 14, "interpolated": true, "extrapolated": true}], "15": [{"bbox": [97, 79, 102, 84], "label": "drone", "frame_id": "15", "frame_idx": 15, "interpolated": true, "trajectory_based": true}, {"bbox": [92, 83, 97, 88], "label": "小目标", "frame_id": "15", "frame_idx": 15, "interpolated": true, "trajectory_based": true}, {"bbox": [69, 84, 74, 89], "label": "drone", "frame_id": "15", "frame_idx": 15, "interpolated": true, "extrapolated": true}], "16": [{"bbox": [97, 79, 102, 84], "label": "drone", "frame_id": "16", "frame_idx": 16, "interpolated": true, "trajectory_based": true}, {"bbox": [91, 83, 96, 88], "label": "小目标", "frame_id": "16", "frame_idx": 16, "interpolated": true, "trajectory_based": true}, {"bbox": [69, 84, 74, 89], "label": "drone", "frame_id": "16", "frame_idx": 16, "interpolated": true, "extrapolated": true}], "17": [{"bbox": [97, 79, 102, 84], "label": "drone", "frame_id": "17", "frame_idx": 17, "interpolated": true, "trajectory_based": true}, {"bbox": [91, 84, 96, 89], "label": "小目标", "frame_id": "17", "frame_idx": 17, "interpolated": true, "trajectory_based": true}, {"bbox": [69, 84, 74, 89], "label": "drone", "frame_id": "17", "frame_idx": 17, "interpolated": true, "extrapolated": true}], "18": [{"bbox": [97, 79, 102, 84], "label": "drone", "frame_id": "18", "frame_idx": 18, "interpolated": true, "trajectory_based": true}, {"bbox": [90, 84, 95, 89], "label": "小目标", "frame_id": "18", "frame_idx": 18, "interpolated": true, "trajectory_based": true}, {"bbox": [69, 84, 74, 89], "label": "drone", "frame_id": "18", "frame_idx": 18, "interpolated": true, "extrapolated": true}], "19": [{"bbox": [97, 79, 102, 84], "label": "drone", "frame_id": "19", "frame_idx": 19, "interpolated": true, "trajectory_based": true}, {"bbox": [90, 85, 95, 90], "label": "小目标", "frame_id": "19", "frame_idx": 19, "interpolated": true, "trajectory_based": true}, {"bbox": [69, 84, 74, 89], "label": "drone", "frame_id": "19", "frame_idx": 19, "interpolated": true, "extrapolated": true}], "21": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "21", "frame_idx": 21, "interpolated": true, "trajectory_based": true}, {"bbox": [90, 85, 95, 90], "label": "小目标", "frame_id": "21", "frame_idx": 21, "interpolated": true, "trajectory_based": true}, {"bbox": [69, 84, 74, 89], "label": "drone", "frame_id": "21", "frame_idx": 21, "interpolated": true, "trajectory_based": true}], "22": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "22", "frame_idx": 22, "interpolated": true, "trajectory_based": true}, {"bbox": [90, 85, 95, 90], "label": "小目标", "frame_id": "22", "frame_idx": 22, "interpolated": true, "trajectory_based": true}, {"bbox": [69, 84, 74, 89], "label": "drone", "frame_id": "22", "frame_idx": 22, "interpolated": true, "trajectory_based": true}], "23": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "23", "frame_idx": 23, "interpolated": true, "trajectory_based": true}, {"bbox": [90, 85, 95, 90], "label": "小目标", "frame_id": "23", "frame_idx": 23, "interpolated": true, "trajectory_based": true}, {"bbox": [69, 84, 74, 89], "label": "drone", "frame_id": "23", "frame_idx": 23, "interpolated": true, "trajectory_based": true}], "24": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "24", "frame_idx": 24, "interpolated": true, "trajectory_based": true}, {"bbox": [90, 85, 95, 90], "label": "小目标", "frame_id": "24", "frame_idx": 24, "interpolated": true, "trajectory_based": true}, {"bbox": [69, 84, 74, 89], "label": "drone", "frame_id": "24", "frame_idx": 24, "interpolated": true, "trajectory_based": true}], "25": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "25", "frame_idx": 25, "interpolated": true, "trajectory_based": true}, {"bbox": [90, 85, 95, 90], "label": "小目标", "frame_id": "25", "frame_idx": 25, "interpolated": true, "trajectory_based": true}, {"bbox": [69, 84, 74, 89], "label": "drone", "frame_id": "25", "frame_idx": 25, "interpolated": true, "trajectory_based": true}], "26": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "26", "frame_idx": 26, "interpolated": true, "trajectory_based": true}, {"bbox": [90, 84, 95, 89], "label": "小目标", "frame_id": "26", "frame_idx": 26, "interpolated": true, "trajectory_based": true}, {"bbox": [69, 84, 74, 89], "label": "drone", "frame_id": "26", "frame_idx": 26, "interpolated": true, "trajectory_based": true}], "27": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "27", "frame_idx": 27, "interpolated": true, "trajectory_based": true}, {"bbox": [90, 84, 95, 89], "label": "小目标", "frame_id": "27", "frame_idx": 27, "interpolated": true, "trajectory_based": true}, {"bbox": [69, 84, 74, 89], "label": "drone", "frame_id": "27", "frame_idx": 27, "interpolated": true, "trajectory_based": true}], "28": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "28", "frame_idx": 28, "interpolated": true, "trajectory_based": true}, {"bbox": [90, 84, 95, 89], "label": "小目标", "frame_id": "28", "frame_idx": 28, "interpolated": true, "trajectory_based": true}, {"bbox": [69, 84, 74, 89], "label": "drone", "frame_id": "28", "frame_idx": 28, "interpolated": true, "trajectory_based": true}], "29": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "29", "frame_idx": 29, "interpolated": true, "trajectory_based": true}, {"bbox": [90, 84, 95, 89], "label": "小目标", "frame_id": "29", "frame_idx": 29, "interpolated": true, "trajectory_based": true}, {"bbox": [69, 84, 74, 89], "label": "drone", "frame_id": "29", "frame_idx": 29, "interpolated": true, "trajectory_based": true}], "31": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "31", "frame_idx": 31, "interpolated": true, "trajectory_based": true}, {"bbox": [90, 83, 95, 88], "label": "小目标", "frame_id": "31", "frame_idx": 31, "interpolated": true, "trajectory_based": true}, {"bbox": [69, 84, 74, 89], "label": "drone", "frame_id": "31", "frame_idx": 31, "interpolated": true, "trajectory_based": true}], "32": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "32", "frame_idx": 32, "interpolated": true, "trajectory_based": true}, {"bbox": [91, 83, 96, 88], "label": "小目标", "frame_id": "32", "frame_idx": 32, "interpolated": true, "trajectory_based": true}, {"bbox": [69, 84, 74, 89], "label": "drone", "frame_id": "32", "frame_idx": 32, "interpolated": true, "trajectory_based": true}], "33": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "33", "frame_idx": 33, "interpolated": true, "trajectory_based": true}, {"bbox": [91, 82, 97, 88], "label": "小目标", "frame_id": "33", "frame_idx": 33, "interpolated": true, "trajectory_based": true}, {"bbox": [69, 84, 74, 89], "label": "drone", "frame_id": "33", "frame_idx": 33, "interpolated": true, "trajectory_based": true}], "34": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "34", "frame_idx": 34, "interpolated": true, "trajectory_based": true}, {"bbox": [92, 82, 98, 88], "label": "小目标", "frame_id": "34", "frame_idx": 34, "interpolated": true, "trajectory_based": true}, {"bbox": [69, 84, 74, 89], "label": "drone", "frame_id": "34", "frame_idx": 34, "interpolated": true, "trajectory_based": true}], "35": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "35", "frame_idx": 35, "interpolated": true, "trajectory_based": true}, {"bbox": [93, 82, 99, 88], "label": "小目标", "frame_id": "35", "frame_idx": 35, "interpolated": true, "trajectory_based": true}, {"bbox": [69, 84, 74, 89], "label": "drone", "frame_id": "35", "frame_idx": 35, "interpolated": true, "trajectory_based": true}], "36": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "36", "frame_idx": 36, "interpolated": true, "trajectory_based": true}, {"bbox": [93, 81, 100, 88], "label": "小目标", "frame_id": "36", "frame_idx": 36, "interpolated": true, "trajectory_based": true}, {"bbox": [69, 84, 74, 89], "label": "drone", "frame_id": "36", "frame_idx": 36, "interpolated": true, "trajectory_based": true}], "37": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "37", "frame_idx": 37, "interpolated": true, "trajectory_based": true}, {"bbox": [94, 81, 101, 88], "label": "小目标", "frame_id": "37", "frame_idx": 37, "interpolated": true, "trajectory_based": true}, {"bbox": [69, 84, 74, 89], "label": "drone", "frame_id": "37", "frame_idx": 37, "interpolated": true, "trajectory_based": true}], "38": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "38", "frame_idx": 38, "interpolated": true, "trajectory_based": true}, {"bbox": [94, 80, 102, 88], "label": "小目标", "frame_id": "38", "frame_idx": 38, "interpolated": true, "trajectory_based": true}, {"bbox": [69, 84, 74, 89], "label": "drone", "frame_id": "38", "frame_idx": 38, "interpolated": true, "trajectory_based": true}], "39": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "39", "frame_idx": 39, "interpolated": true, "trajectory_based": true}, {"bbox": [95, 80, 103, 88], "label": "小目标", "frame_id": "39", "frame_idx": 39, "interpolated": true, "trajectory_based": true}, {"bbox": [69, 84, 74, 89], "label": "drone", "frame_id": "39", "frame_idx": 39, "interpolated": true, "trajectory_based": true}], "41": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "41", "frame_idx": 41, "interpolated": true, "extrapolated": true}, {"bbox": [96, 80, 104, 88], "label": "小目标", "frame_id": "41", "frame_idx": 41, "interpolated": true, "extrapolated": true}, {"bbox": [69, 84, 74, 89], "label": "drone", "frame_id": "41", "frame_idx": 41, "interpolated": true, "extrapolated": true}], "42": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "42", "frame_idx": 42, "interpolated": true, "extrapolated": true}, {"bbox": [96, 80, 104, 88], "label": "小目标", "frame_id": "42", "frame_idx": 42, "interpolated": true, "extrapolated": true}, {"bbox": [69, 84, 74, 89], "label": "drone", "frame_id": "42", "frame_idx": 42, "interpolated": true, "extrapolated": true}], "43": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "43", "frame_idx": 43, "interpolated": true, "extrapolated": true}, {"bbox": [96, 80, 104, 88], "label": "小目标", "frame_id": "43", "frame_idx": 43, "interpolated": true, "extrapolated": true}, {"bbox": [69, 84, 74, 89], "label": "drone", "frame_id": "43", "frame_idx": 43, "interpolated": true, "extrapolated": true}], "44": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "44", "frame_idx": 44, "interpolated": true, "extrapolated": true}, {"bbox": [96, 80, 104, 88], "label": "小目标", "frame_id": "44", "frame_idx": 44, "interpolated": true, "extrapolated": true}, {"bbox": [69, 84, 74, 89], "label": "drone", "frame_id": "44", "frame_idx": 44, "interpolated": true, "extrapolated": true}], "45": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "45", "frame_idx": 45, "interpolated": true, "extrapolated": true}, {"bbox": [96, 80, 104, 88], "label": "小目标", "frame_id": "45", "frame_idx": 45, "interpolated": true, "extrapolated": true}, {"bbox": [69, 84, 74, 89], "label": "drone", "frame_id": "45", "frame_idx": 45, "interpolated": true, "extrapolated": true}], "46": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "46", "frame_idx": 46, "interpolated": true, "extrapolated": true}, {"bbox": [96, 80, 104, 88], "label": "小目标", "frame_id": "46", "frame_idx": 46, "interpolated": true, "extrapolated": true}, {"bbox": [69, 84, 74, 89], "label": "drone", "frame_id": "46", "frame_idx": 46, "interpolated": true, "extrapolated": true}], "47": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "47", "frame_idx": 47, "interpolated": true, "extrapolated": true}, {"bbox": [96, 80, 104, 88], "label": "小目标", "frame_id": "47", "frame_idx": 47, "interpolated": true, "extrapolated": true}, {"bbox": [69, 84, 74, 89], "label": "drone", "frame_id": "47", "frame_idx": 47, "interpolated": true, "extrapolated": true}], "48": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "48", "frame_idx": 48, "interpolated": true, "extrapolated": true}, {"bbox": [96, 80, 104, 88], "label": "小目标", "frame_id": "48", "frame_idx": 48, "interpolated": true, "extrapolated": true}, {"bbox": [69, 84, 74, 89], "label": "drone", "frame_id": "48", "frame_idx": 48, "interpolated": true, "extrapolated": true}], "49": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "49", "frame_idx": 49, "interpolated": true, "extrapolated": true}, {"bbox": [96, 80, 104, 88], "label": "小目标", "frame_id": "49", "frame_idx": 49, "interpolated": true, "extrapolated": true}, {"bbox": [69, 84, 74, 89], "label": "drone", "frame_id": "49", "frame_idx": 49, "interpolated": true, "extrapolated": true}]}}, "data04": {"sequence_id": "data04", "total_frames": 50, "key_frames": 5, "total_detections": 147, "direct_detections": 12, "interpolated_detections": 135, "metrics": {"precision": 0.006802721088435374, "recall": 0.02, "f1": 0.01015228426395939, "tp": 1, "fp": 146, "fn": 49}, "detections": {"0": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "0", "frame_idx": 0}, {"bbox": [67, 89, 72, 94], "label": "drone", "frame_idx": 0, "frame_id": "0"}, {"bbox": [96, 80, 104, 88], "label": "小目标", "frame_idx": 0, "frame_id": "0"}], "10": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "10", "frame_idx": 10}, {"bbox": [67, 84, 72, 99], "label": "drone", "frame_idx": 10, "frame_id": "10"}, {"bbox": [67, 80, 72, 85], "label": "小目标", "frame_idx": 10, "frame_id": "10"}], "20": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "20", "frame_idx": 20}, {"bbox": [96, 84, 101, 89], "label": "小目标", "frame_idx": 20, "frame_id": "20"}], "30": [{"bbox": [98, 77, 104, 83], "label": "小目标", "frame_idx": 30, "frame_id": "30"}], "40": [{"bbox": [98, 77, 103, 82], "label": "drone", "frame_id": "40", "frame_idx": 40}, {"bbox": [69, 74, 74, 79], "label": "drone", "frame_idx": 40, "frame_id": "40"}, {"bbox": [60, 84, 65, 90], "label": "小目标", "frame_idx": 40, "frame_id": "40"}], "1": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "1", "frame_idx": 1, "interpolated": true, "trajectory_based": true}, {"bbox": [67, 88, 72, 94], "label": "drone", "frame_id": "1", "frame_idx": 1, "interpolated": true, "trajectory_based": true}, {"bbox": [93, 80, 100, 87], "label": "小目标", "frame_id": "1", "frame_idx": 1, "interpolated": true, "trajectory_based": true}], "2": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "2", "frame_idx": 2, "interpolated": true, "trajectory_based": true}, {"bbox": [67, 88, 72, 95], "label": "drone", "frame_id": "2", "frame_idx": 2, "interpolated": true, "trajectory_based": true}, {"bbox": [90, 80, 97, 87], "label": "小目标", "frame_id": "2", "frame_idx": 2, "interpolated": true, "trajectory_based": true}], "3": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "3", "frame_idx": 3, "interpolated": true, "trajectory_based": true}, {"bbox": [67, 87, 72, 95], "label": "drone", "frame_id": "3", "frame_idx": 3, "interpolated": true, "trajectory_based": true}, {"bbox": [87, 80, 94, 87], "label": "小目标", "frame_id": "3", "frame_idx": 3, "interpolated": true, "trajectory_based": true}], "4": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "4", "frame_idx": 4, "interpolated": true, "trajectory_based": true}, {"bbox": [67, 87, 72, 96], "label": "drone", "frame_id": "4", "frame_idx": 4, "interpolated": true, "trajectory_based": true}, {"bbox": [84, 80, 91, 86], "label": "小目标", "frame_id": "4", "frame_idx": 4, "interpolated": true, "trajectory_based": true}], "5": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "5", "frame_idx": 5, "interpolated": true, "trajectory_based": true}, {"bbox": [67, 86, 72, 96], "label": "drone", "frame_id": "5", "frame_idx": 5, "interpolated": true, "trajectory_based": true}, {"bbox": [81, 80, 88, 86], "label": "小目标", "frame_id": "5", "frame_idx": 5, "interpolated": true, "trajectory_based": true}], "6": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "6", "frame_idx": 6, "interpolated": true, "trajectory_based": true}, {"bbox": [67, 86, 72, 97], "label": "drone", "frame_id": "6", "frame_idx": 6, "interpolated": true, "trajectory_based": true}, {"bbox": [78, 80, 84, 86], "label": "小目标", "frame_id": "6", "frame_idx": 6, "interpolated": true, "trajectory_based": true}], "7": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "7", "frame_idx": 7, "interpolated": true, "trajectory_based": true}, {"bbox": [67, 85, 72, 97], "label": "drone", "frame_id": "7", "frame_idx": 7, "interpolated": true, "trajectory_based": true}, {"bbox": [75, 80, 81, 85], "label": "小目标", "frame_id": "7", "frame_idx": 7, "interpolated": true, "trajectory_based": true}], "8": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "8", "frame_idx": 8, "interpolated": true, "trajectory_based": true}, {"bbox": [67, 85, 72, 98], "label": "drone", "frame_id": "8", "frame_idx": 8, "interpolated": true, "trajectory_based": true}, {"bbox": [72, 80, 78, 85], "label": "小目标", "frame_id": "8", "frame_idx": 8, "interpolated": true, "trajectory_based": true}], "9": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "9", "frame_idx": 9, "interpolated": true, "trajectory_based": true}, {"bbox": [67, 84, 72, 98], "label": "drone", "frame_id": "9", "frame_idx": 9, "interpolated": true, "trajectory_based": true}, {"bbox": [69, 80, 75, 85], "label": "小目标", "frame_id": "9", "frame_idx": 9, "interpolated": true, "trajectory_based": true}], "11": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "11", "frame_idx": 11, "interpolated": true, "trajectory_based": true}, {"bbox": [67, 84, 72, 99], "label": "drone", "frame_id": "11", "frame_idx": 11, "interpolated": true, "extrapolated": true}, {"bbox": [69, 80, 74, 85], "label": "小目标", "frame_id": "11", "frame_idx": 11, "interpolated": true, "trajectory_based": true}], "12": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "12", "frame_idx": 12, "interpolated": true, "trajectory_based": true}, {"bbox": [67, 84, 72, 99], "label": "drone", "frame_id": "12", "frame_idx": 12, "interpolated": true, "extrapolated": true}, {"bbox": [72, 80, 77, 85], "label": "小目标", "frame_id": "12", "frame_idx": 12, "interpolated": true, "trajectory_based": true}], "13": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "13", "frame_idx": 13, "interpolated": true, "trajectory_based": true}, {"bbox": [67, 84, 72, 99], "label": "drone", "frame_id": "13", "frame_idx": 13, "interpolated": true, "extrapolated": true}, {"bbox": [75, 81, 80, 86], "label": "小目标", "frame_id": "13", "frame_idx": 13, "interpolated": true, "trajectory_based": true}], "14": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "14", "frame_idx": 14, "interpolated": true, "trajectory_based": true}, {"bbox": [67, 84, 72, 99], "label": "drone", "frame_id": "14", "frame_idx": 14, "interpolated": true, "extrapolated": true}, {"bbox": [78, 81, 83, 86], "label": "小目标", "frame_id": "14", "frame_idx": 14, "interpolated": true, "trajectory_based": true}], "15": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "15", "frame_idx": 15, "interpolated": true, "trajectory_based": true}, {"bbox": [67, 84, 72, 99], "label": "drone", "frame_id": "15", "frame_idx": 15, "interpolated": true, "extrapolated": true}, {"bbox": [81, 82, 86, 87], "label": "小目标", "frame_id": "15", "frame_idx": 15, "interpolated": true, "trajectory_based": true}], "16": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "16", "frame_idx": 16, "interpolated": true, "trajectory_based": true}, {"bbox": [67, 84, 72, 99], "label": "drone", "frame_id": "16", "frame_idx": 16, "interpolated": true, "extrapolated": true}, {"bbox": [84, 82, 89, 87], "label": "小目标", "frame_id": "16", "frame_idx": 16, "interpolated": true, "trajectory_based": true}], "17": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "17", "frame_idx": 17, "interpolated": true, "trajectory_based": true}, {"bbox": [67, 84, 72, 99], "label": "drone", "frame_id": "17", "frame_idx": 17, "interpolated": true, "extrapolated": true}, {"bbox": [87, 82, 92, 87], "label": "小目标", "frame_id": "17", "frame_idx": 17, "interpolated": true, "trajectory_based": true}], "18": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "18", "frame_idx": 18, "interpolated": true, "trajectory_based": true}, {"bbox": [67, 84, 72, 99], "label": "drone", "frame_id": "18", "frame_idx": 18, "interpolated": true, "extrapolated": true}, {"bbox": [90, 83, 95, 88], "label": "小目标", "frame_id": "18", "frame_idx": 18, "interpolated": true, "trajectory_based": true}], "19": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "19", "frame_idx": 19, "interpolated": true, "trajectory_based": true}, {"bbox": [67, 84, 72, 99], "label": "drone", "frame_id": "19", "frame_idx": 19, "interpolated": true, "extrapolated": true}, {"bbox": [93, 83, 98, 88], "label": "小目标", "frame_id": "19", "frame_idx": 19, "interpolated": true, "trajectory_based": true}], "21": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "21", "frame_idx": 21, "interpolated": true, "trajectory_based": true}, {"bbox": [67, 84, 72, 99], "label": "drone", "frame_id": "21", "frame_idx": 21, "interpolated": true, "extrapolated": true}, {"bbox": [96, 84, 101, 89], "label": "小目标", "frame_id": "21", "frame_idx": 21, "interpolated": true, "extrapolated": true}], "22": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "22", "frame_idx": 22, "interpolated": true, "trajectory_based": true}, {"bbox": [67, 84, 72, 99], "label": "drone", "frame_id": "22", "frame_idx": 22, "interpolated": true, "extrapolated": true}, {"bbox": [96, 84, 101, 89], "label": "小目标", "frame_id": "22", "frame_idx": 22, "interpolated": true, "extrapolated": true}], "23": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "23", "frame_idx": 23, "interpolated": true, "trajectory_based": true}, {"bbox": [67, 84, 72, 99], "label": "drone", "frame_id": "23", "frame_idx": 23, "interpolated": true, "extrapolated": true}, {"bbox": [96, 84, 101, 89], "label": "小目标", "frame_id": "23", "frame_idx": 23, "interpolated": true, "extrapolated": true}], "24": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "24", "frame_idx": 24, "interpolated": true, "trajectory_based": true}, {"bbox": [67, 84, 72, 99], "label": "drone", "frame_id": "24", "frame_idx": 24, "interpolated": true, "extrapolated": true}, {"bbox": [96, 84, 101, 89], "label": "小目标", "frame_id": "24", "frame_idx": 24, "interpolated": true, "extrapolated": true}], "25": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "25", "frame_idx": 25, "interpolated": true, "trajectory_based": true}, {"bbox": [67, 84, 72, 99], "label": "drone", "frame_id": "25", "frame_idx": 25, "interpolated": true, "extrapolated": true}, {"bbox": [96, 84, 101, 89], "label": "小目标", "frame_id": "25", "frame_idx": 25, "interpolated": true, "extrapolated": true}], "26": [{"bbox": [98, 77, 103, 83], "label": "drone", "frame_id": "26", "frame_idx": 26, "interpolated": true, "trajectory_based": true}, {"bbox": [67, 84, 72, 99], "label": "drone", "frame_id": "26", "frame_idx": 26, "interpolated": true, "extrapolated": true}, {"bbox": [96, 84, 101, 89], "label": "小目标", "frame_id": "26", "frame_idx": 26, "interpolated": true, "extrapolated": true}], "27": [{"bbox": [98, 77, 103, 83], "label": "drone", "frame_id": "27", "frame_idx": 27, "interpolated": true, "trajectory_based": true}, {"bbox": [67, 84, 72, 99], "label": "drone", "frame_id": "27", "frame_idx": 27, "interpolated": true, "extrapolated": true}, {"bbox": [96, 84, 101, 89], "label": "小目标", "frame_id": "27", "frame_idx": 27, "interpolated": true, "extrapolated": true}], "28": [{"bbox": [98, 77, 103, 83], "label": "drone", "frame_id": "28", "frame_idx": 28, "interpolated": true, "trajectory_based": true}, {"bbox": [67, 84, 72, 99], "label": "drone", "frame_id": "28", "frame_idx": 28, "interpolated": true, "extrapolated": true}, {"bbox": [96, 84, 101, 89], "label": "小目标", "frame_id": "28", "frame_idx": 28, "interpolated": true, "extrapolated": true}], "29": [{"bbox": [98, 77, 103, 83], "label": "drone", "frame_id": "29", "frame_idx": 29, "interpolated": true, "trajectory_based": true}, {"bbox": [67, 84, 72, 99], "label": "drone", "frame_id": "29", "frame_idx": 29, "interpolated": true, "extrapolated": true}, {"bbox": [96, 84, 101, 89], "label": "小目标", "frame_id": "29", "frame_idx": 29, "interpolated": true, "extrapolated": true}], "31": [{"bbox": [98, 77, 103, 82], "label": "小目标", "frame_id": "31", "frame_idx": 31, "interpolated": true, "trajectory_based": true}, {"bbox": [67, 84, 72, 99], "label": "drone", "frame_id": "31", "frame_idx": 31, "interpolated": true, "extrapolated": true}, {"bbox": [96, 84, 101, 89], "label": "小目标", "frame_id": "31", "frame_idx": 31, "interpolated": true, "extrapolated": true}], "32": [{"bbox": [98, 77, 103, 82], "label": "小目标", "frame_id": "32", "frame_idx": 32, "interpolated": true, "trajectory_based": true}, {"bbox": [67, 84, 72, 99], "label": "drone", "frame_id": "32", "frame_idx": 32, "interpolated": true, "extrapolated": true}, {"bbox": [96, 84, 101, 89], "label": "小目标", "frame_id": "32", "frame_idx": 32, "interpolated": true, "extrapolated": true}], "33": [{"bbox": [98, 77, 103, 82], "label": "小目标", "frame_id": "33", "frame_idx": 33, "interpolated": true, "trajectory_based": true}, {"bbox": [67, 84, 72, 99], "label": "drone", "frame_id": "33", "frame_idx": 33, "interpolated": true, "extrapolated": true}, {"bbox": [96, 84, 101, 89], "label": "小目标", "frame_id": "33", "frame_idx": 33, "interpolated": true, "extrapolated": true}], "34": [{"bbox": [98, 77, 103, 82], "label": "小目标", "frame_id": "34", "frame_idx": 34, "interpolated": true, "trajectory_based": true}, {"bbox": [67, 84, 72, 99], "label": "drone", "frame_id": "34", "frame_idx": 34, "interpolated": true, "extrapolated": true}, {"bbox": [96, 84, 101, 89], "label": "小目标", "frame_id": "34", "frame_idx": 34, "interpolated": true, "extrapolated": true}], "35": [{"bbox": [98, 77, 103, 82], "label": "小目标", "frame_id": "35", "frame_idx": 35, "interpolated": true, "trajectory_based": true}, {"bbox": [67, 84, 72, 99], "label": "drone", "frame_id": "35", "frame_idx": 35, "interpolated": true, "extrapolated": true}, {"bbox": [96, 84, 101, 89], "label": "小目标", "frame_id": "35", "frame_idx": 35, "interpolated": true, "extrapolated": true}], "36": [{"bbox": [98, 77, 103, 82], "label": "小目标", "frame_id": "36", "frame_idx": 36, "interpolated": true, "trajectory_based": true}, {"bbox": [67, 84, 72, 99], "label": "drone", "frame_id": "36", "frame_idx": 36, "interpolated": true, "extrapolated": true}, {"bbox": [96, 84, 101, 89], "label": "小目标", "frame_id": "36", "frame_idx": 36, "interpolated": true, "extrapolated": true}], "37": [{"bbox": [98, 77, 103, 82], "label": "小目标", "frame_id": "37", "frame_idx": 37, "interpolated": true, "trajectory_based": true}, {"bbox": [67, 84, 72, 99], "label": "drone", "frame_id": "37", "frame_idx": 37, "interpolated": true, "extrapolated": true}, {"bbox": [96, 84, 101, 89], "label": "小目标", "frame_id": "37", "frame_idx": 37, "interpolated": true, "extrapolated": true}], "38": [{"bbox": [98, 77, 103, 82], "label": "小目标", "frame_id": "38", "frame_idx": 38, "interpolated": true, "trajectory_based": true}, {"bbox": [67, 84, 72, 99], "label": "drone", "frame_id": "38", "frame_idx": 38, "interpolated": true, "extrapolated": true}, {"bbox": [96, 84, 101, 89], "label": "小目标", "frame_id": "38", "frame_idx": 38, "interpolated": true, "extrapolated": true}], "39": [{"bbox": [98, 77, 103, 82], "label": "小目标", "frame_id": "39", "frame_idx": 39, "interpolated": true, "trajectory_based": true}, {"bbox": [67, 84, 72, 99], "label": "drone", "frame_id": "39", "frame_idx": 39, "interpolated": true, "extrapolated": true}, {"bbox": [96, 84, 101, 89], "label": "小目标", "frame_id": "39", "frame_idx": 39, "interpolated": true, "extrapolated": true}], "41": [{"bbox": [98, 77, 103, 82], "label": "drone", "frame_id": "41", "frame_idx": 41, "interpolated": true, "extrapolated": true}, {"bbox": [67, 84, 72, 99], "label": "drone", "frame_id": "41", "frame_idx": 41, "interpolated": true, "extrapolated": true}, {"bbox": [96, 84, 101, 89], "label": "小目标", "frame_id": "41", "frame_idx": 41, "interpolated": true, "extrapolated": true}], "42": [{"bbox": [98, 77, 103, 82], "label": "drone", "frame_id": "42", "frame_idx": 42, "interpolated": true, "extrapolated": true}, {"bbox": [67, 84, 72, 99], "label": "drone", "frame_id": "42", "frame_idx": 42, "interpolated": true, "extrapolated": true}, {"bbox": [96, 84, 101, 89], "label": "小目标", "frame_id": "42", "frame_idx": 42, "interpolated": true, "extrapolated": true}], "43": [{"bbox": [98, 77, 103, 82], "label": "drone", "frame_id": "43", "frame_idx": 43, "interpolated": true, "extrapolated": true}, {"bbox": [67, 84, 72, 99], "label": "drone", "frame_id": "43", "frame_idx": 43, "interpolated": true, "extrapolated": true}, {"bbox": [96, 84, 101, 89], "label": "小目标", "frame_id": "43", "frame_idx": 43, "interpolated": true, "extrapolated": true}], "44": [{"bbox": [98, 77, 103, 82], "label": "drone", "frame_id": "44", "frame_idx": 44, "interpolated": true, "extrapolated": true}, {"bbox": [67, 84, 72, 99], "label": "drone", "frame_id": "44", "frame_idx": 44, "interpolated": true, "extrapolated": true}, {"bbox": [96, 84, 101, 89], "label": "小目标", "frame_id": "44", "frame_idx": 44, "interpolated": true, "extrapolated": true}], "45": [{"bbox": [98, 77, 103, 82], "label": "drone", "frame_id": "45", "frame_idx": 45, "interpolated": true, "extrapolated": true}, {"bbox": [67, 84, 72, 99], "label": "drone", "frame_id": "45", "frame_idx": 45, "interpolated": true, "extrapolated": true}, {"bbox": [96, 84, 101, 89], "label": "小目标", "frame_id": "45", "frame_idx": 45, "interpolated": true, "extrapolated": true}], "46": [{"bbox": [98, 77, 103, 82], "label": "drone", "frame_id": "46", "frame_idx": 46, "interpolated": true, "extrapolated": true}, {"bbox": [67, 84, 72, 99], "label": "drone", "frame_id": "46", "frame_idx": 46, "interpolated": true, "extrapolated": true}, {"bbox": [96, 84, 101, 89], "label": "小目标", "frame_id": "46", "frame_idx": 46, "interpolated": true, "extrapolated": true}], "47": [{"bbox": [98, 77, 103, 82], "label": "drone", "frame_id": "47", "frame_idx": 47, "interpolated": true, "extrapolated": true}, {"bbox": [67, 84, 72, 99], "label": "drone", "frame_id": "47", "frame_idx": 47, "interpolated": true, "extrapolated": true}, {"bbox": [96, 84, 101, 89], "label": "小目标", "frame_id": "47", "frame_idx": 47, "interpolated": true, "extrapolated": true}], "48": [{"bbox": [98, 77, 103, 82], "label": "drone", "frame_id": "48", "frame_idx": 48, "interpolated": true, "extrapolated": true}, {"bbox": [67, 84, 72, 99], "label": "drone", "frame_id": "48", "frame_idx": 48, "interpolated": true, "extrapolated": true}, {"bbox": [96, 84, 101, 89], "label": "小目标", "frame_id": "48", "frame_idx": 48, "interpolated": true, "extrapolated": true}], "49": [{"bbox": [98, 77, 103, 82], "label": "drone", "frame_id": "49", "frame_idx": 49, "interpolated": true, "extrapolated": true}, {"bbox": [67, 84, 72, 99], "label": "drone", "frame_id": "49", "frame_idx": 49, "interpolated": true, "extrapolated": true}, {"bbox": [96, 84, 101, 89], "label": "小目标", "frame_id": "49", "frame_idx": 49, "interpolated": true, "extrapolated": true}]}}, "data06": {"sequence_id": "data06", "total_frames": 50, "key_frames": 5, "total_detections": 99, "direct_detections": 9, "interpolated_detections": 90, "metrics": {"precision": 0.0, "recall": 0.0, "f1": 0, "tp": 0, "fp": 99, "fn": 50}, "detections": {"0": [{"bbox": [96, 84, 101, 89], "label": "drone", "frame_idx": 0, "frame_id": "0"}, {"bbox": [96, 80, 104, 88], "label": "小目标", "frame_idx": 0, "frame_id": "0"}], "10": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "10", "frame_idx": 10}, {"bbox": [68, 84, 73, 89], "label": "小目标", "frame_idx": 10, "frame_id": "10"}], "20": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "20", "frame_idx": 20}], "30": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "30", "frame_idx": 30}, {"bbox": [69, 78, 74, 83], "label": "drone", "frame_idx": 30, "frame_id": "30"}, {"bbox": [60, 84, 65, 90], "label": "小目标", "frame_idx": 30, "frame_id": "30"}], "40": [{"bbox": [96, 86, 103, 93], "label": "小目标", "frame_idx": 40, "frame_id": "40"}], "1": [{"bbox": [96, 83, 101, 88], "label": "drone", "frame_id": "1", "frame_idx": 1, "interpolated": true, "trajectory_based": true}, {"bbox": [93, 80, 100, 88], "label": "小目标", "frame_id": "1", "frame_idx": 1, "interpolated": true, "trajectory_based": true}], "2": [{"bbox": [96, 82, 101, 87], "label": "drone", "frame_id": "2", "frame_idx": 2, "interpolated": true, "trajectory_based": true}, {"bbox": [90, 80, 97, 88], "label": "小目标", "frame_id": "2", "frame_idx": 2, "interpolated": true, "trajectory_based": true}], "3": [{"bbox": [96, 82, 101, 87], "label": "drone", "frame_id": "3", "frame_idx": 3, "interpolated": true, "trajectory_based": true}, {"bbox": [87, 81, 94, 88], "label": "小目标", "frame_id": "3", "frame_idx": 3, "interpolated": true, "trajectory_based": true}], "4": [{"bbox": [96, 81, 101, 86], "label": "drone", "frame_id": "4", "frame_idx": 4, "interpolated": true, "trajectory_based": true}, {"bbox": [84, 81, 91, 88], "label": "小目标", "frame_id": "4", "frame_idx": 4, "interpolated": true, "trajectory_based": true}], "5": [{"bbox": [97, 81, 102, 86], "label": "drone", "frame_id": "5", "frame_idx": 5, "interpolated": true, "trajectory_based": true}, {"bbox": [82, 82, 88, 88], "label": "小目标", "frame_id": "5", "frame_idx": 5, "interpolated": true, "trajectory_based": true}], "6": [{"bbox": [97, 80, 102, 85], "label": "drone", "frame_id": "6", "frame_idx": 6, "interpolated": true, "trajectory_based": true}, {"bbox": [79, 82, 85, 88], "label": "小目标", "frame_id": "6", "frame_idx": 6, "interpolated": true, "trajectory_based": true}], "7": [{"bbox": [97, 79, 102, 84], "label": "drone", "frame_id": "7", "frame_idx": 7, "interpolated": true, "trajectory_based": true}, {"bbox": [76, 82, 82, 88], "label": "小目标", "frame_id": "7", "frame_idx": 7, "interpolated": true, "trajectory_based": true}], "8": [{"bbox": [97, 79, 102, 84], "label": "drone", "frame_id": "8", "frame_idx": 8, "interpolated": true, "trajectory_based": true}, {"bbox": [73, 83, 79, 88], "label": "小目标", "frame_id": "8", "frame_idx": 8, "interpolated": true, "trajectory_based": true}], "9": [{"bbox": [97, 78, 102, 83], "label": "drone", "frame_id": "9", "frame_idx": 9, "interpolated": true, "trajectory_based": true}, {"bbox": [70, 83, 76, 88], "label": "小目标", "frame_id": "9", "frame_idx": 9, "interpolated": true, "trajectory_based": true}], "11": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "11", "frame_idx": 11, "interpolated": true, "trajectory_based": true}, {"bbox": [68, 84, 73, 89], "label": "小目标", "frame_id": "11", "frame_idx": 11, "interpolated": true, "extrapolated": true}], "12": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "12", "frame_idx": 12, "interpolated": true, "trajectory_based": true}, {"bbox": [68, 84, 73, 89], "label": "小目标", "frame_id": "12", "frame_idx": 12, "interpolated": true, "extrapolated": true}], "13": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "13", "frame_idx": 13, "interpolated": true, "trajectory_based": true}, {"bbox": [68, 84, 73, 89], "label": "小目标", "frame_id": "13", "frame_idx": 13, "interpolated": true, "extrapolated": true}], "14": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "14", "frame_idx": 14, "interpolated": true, "trajectory_based": true}, {"bbox": [68, 84, 73, 89], "label": "小目标", "frame_id": "14", "frame_idx": 14, "interpolated": true, "extrapolated": true}], "15": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "15", "frame_idx": 15, "interpolated": true, "trajectory_based": true}, {"bbox": [68, 84, 73, 89], "label": "小目标", "frame_id": "15", "frame_idx": 15, "interpolated": true, "extrapolated": true}], "16": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "16", "frame_idx": 16, "interpolated": true, "trajectory_based": true}, {"bbox": [68, 84, 73, 89], "label": "小目标", "frame_id": "16", "frame_idx": 16, "interpolated": true, "extrapolated": true}], "17": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "17", "frame_idx": 17, "interpolated": true, "trajectory_based": true}, {"bbox": [68, 84, 73, 89], "label": "小目标", "frame_id": "17", "frame_idx": 17, "interpolated": true, "extrapolated": true}], "18": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "18", "frame_idx": 18, "interpolated": true, "trajectory_based": true}, {"bbox": [68, 84, 73, 89], "label": "小目标", "frame_id": "18", "frame_idx": 18, "interpolated": true, "extrapolated": true}], "19": [{"bbox": [98, 78, 103, 83], "label": "drone", "frame_id": "19", "frame_idx": 19, "interpolated": true, "trajectory_based": true}, {"bbox": [68, 84, 73, 89], "label": "小目标", "frame_id": "19", "frame_idx": 19, "interpolated": true, "extrapolated": true}], "21": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "21", "frame_idx": 21, "interpolated": true, "trajectory_based": true}, {"bbox": [68, 84, 73, 89], "label": "小目标", "frame_id": "21", "frame_idx": 21, "interpolated": true, "extrapolated": true}], "22": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "22", "frame_idx": 22, "interpolated": true, "trajectory_based": true}, {"bbox": [68, 84, 73, 89], "label": "小目标", "frame_id": "22", "frame_idx": 22, "interpolated": true, "extrapolated": true}], "23": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "23", "frame_idx": 23, "interpolated": true, "trajectory_based": true}, {"bbox": [68, 84, 73, 89], "label": "小目标", "frame_id": "23", "frame_idx": 23, "interpolated": true, "extrapolated": true}], "24": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "24", "frame_idx": 24, "interpolated": true, "trajectory_based": true}, {"bbox": [68, 84, 73, 89], "label": "小目标", "frame_id": "24", "frame_idx": 24, "interpolated": true, "extrapolated": true}], "25": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "25", "frame_idx": 25, "interpolated": true, "trajectory_based": true}, {"bbox": [68, 84, 73, 89], "label": "小目标", "frame_id": "25", "frame_idx": 25, "interpolated": true, "extrapolated": true}], "26": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "26", "frame_idx": 26, "interpolated": true, "trajectory_based": true}, {"bbox": [68, 84, 73, 89], "label": "小目标", "frame_id": "26", "frame_idx": 26, "interpolated": true, "extrapolated": true}], "27": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "27", "frame_idx": 27, "interpolated": true, "trajectory_based": true}, {"bbox": [68, 84, 73, 89], "label": "小目标", "frame_id": "27", "frame_idx": 27, "interpolated": true, "extrapolated": true}], "28": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "28", "frame_idx": 28, "interpolated": true, "trajectory_based": true}, {"bbox": [68, 84, 73, 89], "label": "小目标", "frame_id": "28", "frame_idx": 28, "interpolated": true, "extrapolated": true}], "29": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "29", "frame_idx": 29, "interpolated": true, "trajectory_based": true}, {"bbox": [68, 84, 73, 89], "label": "小目标", "frame_id": "29", "frame_idx": 29, "interpolated": true, "extrapolated": true}], "31": [{"bbox": [97, 79, 103, 84], "label": "drone", "frame_id": "31", "frame_idx": 31, "interpolated": true, "trajectory_based": true}, {"bbox": [68, 84, 73, 89], "label": "小目标", "frame_id": "31", "frame_idx": 31, "interpolated": true, "extrapolated": true}], "32": [{"bbox": [97, 80, 103, 85], "label": "drone", "frame_id": "32", "frame_idx": 32, "interpolated": true, "trajectory_based": true}, {"bbox": [68, 84, 73, 89], "label": "小目标", "frame_id": "32", "frame_idx": 32, "interpolated": true, "extrapolated": true}], "33": [{"bbox": [97, 81, 103, 86], "label": "drone", "frame_id": "33", "frame_idx": 33, "interpolated": true, "trajectory_based": true}, {"bbox": [68, 84, 73, 89], "label": "小目标", "frame_id": "33", "frame_idx": 33, "interpolated": true, "extrapolated": true}], "34": [{"bbox": [97, 81, 103, 87], "label": "drone", "frame_id": "34", "frame_idx": 34, "interpolated": true, "trajectory_based": true}, {"bbox": [68, 84, 73, 89], "label": "小目标", "frame_id": "34", "frame_idx": 34, "interpolated": true, "extrapolated": true}], "35": [{"bbox": [97, 82, 103, 88], "label": "drone", "frame_id": "35", "frame_idx": 35, "interpolated": true, "trajectory_based": true}, {"bbox": [68, 84, 73, 89], "label": "小目标", "frame_id": "35", "frame_idx": 35, "interpolated": true, "extrapolated": true}], "36": [{"bbox": [96, 83, 103, 89], "label": "drone", "frame_id": "36", "frame_idx": 36, "interpolated": true, "trajectory_based": true}, {"bbox": [68, 84, 73, 89], "label": "小目标", "frame_id": "36", "frame_idx": 36, "interpolated": true, "extrapolated": true}], "37": [{"bbox": [96, 83, 103, 90], "label": "drone", "frame_id": "37", "frame_idx": 37, "interpolated": true, "trajectory_based": true}, {"bbox": [68, 84, 73, 89], "label": "小目标", "frame_id": "37", "frame_idx": 37, "interpolated": true, "extrapolated": true}], "38": [{"bbox": [96, 84, 103, 91], "label": "drone", "frame_id": "38", "frame_idx": 38, "interpolated": true, "trajectory_based": true}, {"bbox": [68, 84, 73, 89], "label": "小目标", "frame_id": "38", "frame_idx": 38, "interpolated": true, "extrapolated": true}], "39": [{"bbox": [96, 85, 103, 92], "label": "drone", "frame_id": "39", "frame_idx": 39, "interpolated": true, "trajectory_based": true}, {"bbox": [68, 84, 73, 89], "label": "小目标", "frame_id": "39", "frame_idx": 39, "interpolated": true, "extrapolated": true}], "41": [{"bbox": [96, 86, 103, 93], "label": "小目标", "frame_id": "41", "frame_idx": 41, "interpolated": true, "extrapolated": true}, {"bbox": [68, 84, 73, 89], "label": "小目标", "frame_id": "41", "frame_idx": 41, "interpolated": true, "extrapolated": true}], "42": [{"bbox": [96, 86, 103, 93], "label": "小目标", "frame_id": "42", "frame_idx": 42, "interpolated": true, "extrapolated": true}, {"bbox": [68, 84, 73, 89], "label": "小目标", "frame_id": "42", "frame_idx": 42, "interpolated": true, "extrapolated": true}], "43": [{"bbox": [96, 86, 103, 93], "label": "小目标", "frame_id": "43", "frame_idx": 43, "interpolated": true, "extrapolated": true}, {"bbox": [68, 84, 73, 89], "label": "小目标", "frame_id": "43", "frame_idx": 43, "interpolated": true, "extrapolated": true}], "44": [{"bbox": [96, 86, 103, 93], "label": "小目标", "frame_id": "44", "frame_idx": 44, "interpolated": true, "extrapolated": true}, {"bbox": [68, 84, 73, 89], "label": "小目标", "frame_id": "44", "frame_idx": 44, "interpolated": true, "extrapolated": true}], "45": [{"bbox": [96, 86, 103, 93], "label": "小目标", "frame_id": "45", "frame_idx": 45, "interpolated": true, "extrapolated": true}, {"bbox": [68, 84, 73, 89], "label": "小目标", "frame_id": "45", "frame_idx": 45, "interpolated": true, "extrapolated": true}], "46": [{"bbox": [96, 86, 103, 93], "label": "小目标", "frame_id": "46", "frame_idx": 46, "interpolated": true, "extrapolated": true}, {"bbox": [68, 84, 73, 89], "label": "小目标", "frame_id": "46", "frame_idx": 46, "interpolated": true, "extrapolated": true}], "47": [{"bbox": [96, 86, 103, 93], "label": "小目标", "frame_id": "47", "frame_idx": 47, "interpolated": true, "extrapolated": true}, {"bbox": [68, 84, 73, 89], "label": "小目标", "frame_id": "47", "frame_idx": 47, "interpolated": true, "extrapolated": true}], "48": [{"bbox": [96, 86, 103, 93], "label": "小目标", "frame_id": "48", "frame_idx": 48, "interpolated": true, "extrapolated": true}, {"bbox": [68, 84, 73, 89], "label": "小目标", "frame_id": "48", "frame_idx": 48, "interpolated": true, "extrapolated": true}], "49": [{"bbox": [96, 86, 103, 93], "label": "小目标", "frame_id": "49", "frame_idx": 49, "interpolated": true, "extrapolated": true}, {"bbox": [68, 84, 73, 89], "label": "小目标", "frame_id": "49", "frame_idx": 49, "interpolated": true, "extrapolated": true}]}}, "data07": {"sequence_id": "data07", "total_frames": 50, "key_frames": 5, "total_detections": 101, "direct_detections": 11, "interpolated_detections": 90, "metrics": {"precision": 0.0, "recall": 0.0, "f1": 0, "tp": 0, "fp": 101, "fn": 50}, "detections": {"0": [{"bbox": [98, 136, 103, 141], "label": "drone", "frame_id": "0", "frame_idx": 0}, {"bbox": [97, 80, 106, 89], "label": "小目标", "frame_idx": 0, "frame_id": "0"}], "10": [{"bbox": [98, 128, 103, 133], "label": "drone", "frame_id": "10", "frame_idx": 10}, {"bbox": [98, 70, 108, 80], "label": "小目标", "frame_idx": 10, "frame_id": "10"}], "20": [{"bbox": [98, 126, 103, 131], "label": "drone", "frame_id": "20", "frame_idx": 20}, {"bbox": [60, 87, 65, 92], "label": "小目标", "frame_idx": 20, "frame_id": "20"}], "30": [{"bbox": [98, 126, 103, 131], "label": "drone", "frame_id": "30", "frame_idx": 30}, {"bbox": [67, 89, 72, 94], "label": "drone", "frame_idx": 30, "frame_id": "30"}, {"bbox": [6, 70, 89, 151], "label": "小目标", "frame_idx": 30, "frame_id": "30"}], "40": [{"bbox": [98, 120, 103, 125], "label": "drone", "frame_id": "40", "frame_idx": 40}, {"bbox": [6, 8, 256, 256], "label": "小目标", "frame_idx": 40, "frame_id": "40"}], "1": [{"bbox": [98, 135, 103, 140], "label": "drone", "frame_id": "1", "frame_idx": 1, "interpolated": true, "trajectory_based": true}, {"bbox": [97, 79, 106, 88], "label": "小目标", "frame_id": "1", "frame_idx": 1, "interpolated": true, "trajectory_based": true}], "2": [{"bbox": [98, 134, 103, 139], "label": "drone", "frame_id": "2", "frame_idx": 2, "interpolated": true, "trajectory_based": true}, {"bbox": [97, 78, 106, 87], "label": "小目标", "frame_id": "2", "frame_idx": 2, "interpolated": true, "trajectory_based": true}], "3": [{"bbox": [98, 133, 103, 138], "label": "drone", "frame_id": "3", "frame_idx": 3, "interpolated": true, "trajectory_based": true}, {"bbox": [97, 77, 106, 86], "label": "小目标", "frame_id": "3", "frame_idx": 3, "interpolated": true, "trajectory_based": true}], "4": [{"bbox": [98, 132, 103, 137], "label": "drone", "frame_id": "4", "frame_idx": 4, "interpolated": true, "trajectory_based": true}, {"bbox": [97, 76, 106, 85], "label": "小目标", "frame_id": "4", "frame_idx": 4, "interpolated": true, "trajectory_based": true}], "5": [{"bbox": [98, 132, 103, 137], "label": "drone", "frame_id": "5", "frame_idx": 5, "interpolated": true, "trajectory_based": true}, {"bbox": [97, 75, 107, 84], "label": "小目标", "frame_id": "5", "frame_idx": 5, "interpolated": true, "trajectory_based": true}], "6": [{"bbox": [98, 131, 103, 136], "label": "drone", "frame_id": "6", "frame_idx": 6, "interpolated": true, "trajectory_based": true}, {"bbox": [97, 74, 107, 83], "label": "小目标", "frame_id": "6", "frame_idx": 6, "interpolated": true, "trajectory_based": true}], "7": [{"bbox": [98, 130, 103, 135], "label": "drone", "frame_id": "7", "frame_idx": 7, "interpolated": true, "trajectory_based": true}, {"bbox": [97, 73, 107, 82], "label": "小目标", "frame_id": "7", "frame_idx": 7, "interpolated": true, "trajectory_based": true}], "8": [{"bbox": [98, 129, 103, 134], "label": "drone", "frame_id": "8", "frame_idx": 8, "interpolated": true, "trajectory_based": true}, {"bbox": [97, 72, 107, 81], "label": "小目标", "frame_id": "8", "frame_idx": 8, "interpolated": true, "trajectory_based": true}], "9": [{"bbox": [98, 128, 103, 133], "label": "drone", "frame_id": "9", "frame_idx": 9, "interpolated": true, "trajectory_based": true}, {"bbox": [97, 71, 107, 80], "label": "小目标", "frame_id": "9", "frame_idx": 9, "interpolated": true, "trajectory_based": true}], "11": [{"bbox": [98, 127, 103, 132], "label": "drone", "frame_id": "11", "frame_idx": 11, "interpolated": true, "trajectory_based": true}, {"bbox": [94, 71, 103, 81], "label": "小目标", "frame_id": "11", "frame_idx": 11, "interpolated": true, "trajectory_based": true}], "12": [{"bbox": [98, 127, 103, 132], "label": "drone", "frame_id": "12", "frame_idx": 12, "interpolated": true, "trajectory_based": true}, {"bbox": [90, 73, 99, 82], "label": "小目标", "frame_id": "12", "frame_idx": 12, "interpolated": true, "trajectory_based": true}], "13": [{"bbox": [98, 127, 103, 132], "label": "drone", "frame_id": "13", "frame_idx": 13, "interpolated": true, "trajectory_based": true}, {"bbox": [86, 75, 95, 83], "label": "小目标", "frame_id": "13", "frame_idx": 13, "interpolated": true, "trajectory_based": true}], "14": [{"bbox": [98, 127, 103, 132], "label": "drone", "frame_id": "14", "frame_idx": 14, "interpolated": true, "trajectory_based": true}, {"bbox": [82, 76, 90, 84], "label": "小目标", "frame_id": "14", "frame_idx": 14, "interpolated": true, "trajectory_based": true}], "15": [{"bbox": [98, 127, 103, 132], "label": "drone", "frame_id": "15", "frame_idx": 15, "interpolated": true, "trajectory_based": true}, {"bbox": [79, 78, 86, 86], "label": "小目标", "frame_id": "15", "frame_idx": 15, "interpolated": true, "trajectory_based": true}], "16": [{"bbox": [98, 126, 103, 131], "label": "drone", "frame_id": "16", "frame_idx": 16, "interpolated": true, "trajectory_based": true}, {"bbox": [75, 80, 82, 87], "label": "小目标", "frame_id": "16", "frame_idx": 16, "interpolated": true, "trajectory_based": true}], "17": [{"bbox": [98, 126, 103, 131], "label": "drone", "frame_id": "17", "frame_idx": 17, "interpolated": true, "trajectory_based": true}, {"bbox": [71, 81, 77, 88], "label": "小目标", "frame_id": "17", "frame_idx": 17, "interpolated": true, "trajectory_based": true}], "18": [{"bbox": [98, 126, 103, 131], "label": "drone", "frame_id": "18", "frame_idx": 18, "interpolated": true, "trajectory_based": true}, {"bbox": [67, 83, 73, 89], "label": "小目标", "frame_id": "18", "frame_idx": 18, "interpolated": true, "trajectory_based": true}], "19": [{"bbox": [98, 126, 103, 131], "label": "drone", "frame_id": "19", "frame_idx": 19, "interpolated": true, "trajectory_based": true}, {"bbox": [63, 85, 69, 90], "label": "小目标", "frame_id": "19", "frame_idx": 19, "interpolated": true, "trajectory_based": true}], "21": [{"bbox": [98, 126, 103, 131], "label": "drone", "frame_id": "21", "frame_idx": 21, "interpolated": true, "trajectory_based": true}, {"bbox": [60, 87, 65, 92], "label": "小目标", "frame_id": "21", "frame_idx": 21, "interpolated": true, "trajectory_based": true}], "22": [{"bbox": [98, 126, 103, 131], "label": "drone", "frame_id": "22", "frame_idx": 22, "interpolated": true, "trajectory_based": true}, {"bbox": [61, 87, 66, 92], "label": "小目标", "frame_id": "22", "frame_idx": 22, "interpolated": true, "trajectory_based": true}], "23": [{"bbox": [98, 126, 103, 131], "label": "drone", "frame_id": "23", "frame_idx": 23, "interpolated": true, "trajectory_based": true}, {"bbox": [62, 87, 67, 92], "label": "小目标", "frame_id": "23", "frame_idx": 23, "interpolated": true, "trajectory_based": true}], "24": [{"bbox": [98, 126, 103, 131], "label": "drone", "frame_id": "24", "frame_idx": 24, "interpolated": true, "trajectory_based": true}, {"bbox": [62, 87, 67, 92], "label": "小目标", "frame_id": "24", "frame_idx": 24, "interpolated": true, "trajectory_based": true}], "25": [{"bbox": [98, 126, 103, 131], "label": "drone", "frame_id": "25", "frame_idx": 25, "interpolated": true, "trajectory_based": true}, {"bbox": [63, 88, 68, 93], "label": "小目标", "frame_id": "25", "frame_idx": 25, "interpolated": true, "trajectory_based": true}], "26": [{"bbox": [98, 126, 103, 131], "label": "drone", "frame_id": "26", "frame_idx": 26, "interpolated": true, "trajectory_based": true}, {"bbox": [64, 88, 69, 93], "label": "小目标", "frame_id": "26", "frame_idx": 26, "interpolated": true, "trajectory_based": true}], "27": [{"bbox": [98, 126, 103, 131], "label": "drone", "frame_id": "27", "frame_idx": 27, "interpolated": true, "trajectory_based": true}, {"bbox": [64, 88, 69, 93], "label": "小目标", "frame_id": "27", "frame_idx": 27, "interpolated": true, "trajectory_based": true}], "28": [{"bbox": [98, 126, 103, 131], "label": "drone", "frame_id": "28", "frame_idx": 28, "interpolated": true, "trajectory_based": true}, {"bbox": [65, 88, 70, 93], "label": "小目标", "frame_id": "28", "frame_idx": 28, "interpolated": true, "trajectory_based": true}], "29": [{"bbox": [98, 126, 103, 131], "label": "drone", "frame_id": "29", "frame_idx": 29, "interpolated": true, "trajectory_based": true}, {"bbox": [66, 88, 71, 93], "label": "小目标", "frame_id": "29", "frame_idx": 29, "interpolated": true, "trajectory_based": true}], "31": [{"bbox": [98, 125, 103, 130], "label": "drone", "frame_id": "31", "frame_idx": 31, "interpolated": true, "trajectory_based": true}, {"bbox": [67, 89, 72, 94], "label": "drone", "frame_id": "31", "frame_idx": 31, "interpolated": true, "extrapolated": true}], "32": [{"bbox": [98, 124, 103, 129], "label": "drone", "frame_id": "32", "frame_idx": 32, "interpolated": true, "trajectory_based": true}, {"bbox": [67, 89, 72, 94], "label": "drone", "frame_id": "32", "frame_idx": 32, "interpolated": true, "extrapolated": true}], "33": [{"bbox": [98, 124, 103, 129], "label": "drone", "frame_id": "33", "frame_idx": 33, "interpolated": true, "trajectory_based": true}, {"bbox": [67, 89, 72, 94], "label": "drone", "frame_id": "33", "frame_idx": 33, "interpolated": true, "extrapolated": true}], "34": [{"bbox": [98, 123, 103, 128], "label": "drone", "frame_id": "34", "frame_idx": 34, "interpolated": true, "trajectory_based": true}, {"bbox": [67, 89, 72, 94], "label": "drone", "frame_id": "34", "frame_idx": 34, "interpolated": true, "extrapolated": true}], "35": [{"bbox": [98, 123, 103, 128], "label": "drone", "frame_id": "35", "frame_idx": 35, "interpolated": true, "trajectory_based": true}, {"bbox": [67, 89, 72, 94], "label": "drone", "frame_id": "35", "frame_idx": 35, "interpolated": true, "extrapolated": true}], "36": [{"bbox": [98, 122, 103, 127], "label": "drone", "frame_id": "36", "frame_idx": 36, "interpolated": true, "trajectory_based": true}, {"bbox": [67, 89, 72, 94], "label": "drone", "frame_id": "36", "frame_idx": 36, "interpolated": true, "extrapolated": true}], "37": [{"bbox": [98, 121, 103, 126], "label": "drone", "frame_id": "37", "frame_idx": 37, "interpolated": true, "trajectory_based": true}, {"bbox": [67, 89, 72, 94], "label": "drone", "frame_id": "37", "frame_idx": 37, "interpolated": true, "extrapolated": true}], "38": [{"bbox": [98, 121, 103, 126], "label": "drone", "frame_id": "38", "frame_idx": 38, "interpolated": true, "trajectory_based": true}, {"bbox": [67, 89, 72, 94], "label": "drone", "frame_id": "38", "frame_idx": 38, "interpolated": true, "extrapolated": true}], "39": [{"bbox": [98, 120, 103, 125], "label": "drone", "frame_id": "39", "frame_idx": 39, "interpolated": true, "trajectory_based": true}, {"bbox": [67, 89, 72, 94], "label": "drone", "frame_id": "39", "frame_idx": 39, "interpolated": true, "extrapolated": true}], "41": [{"bbox": [98, 120, 103, 125], "label": "drone", "frame_id": "41", "frame_idx": 41, "interpolated": true, "extrapolated": true}, {"bbox": [67, 89, 72, 94], "label": "drone", "frame_id": "41", "frame_idx": 41, "interpolated": true, "extrapolated": true}], "42": [{"bbox": [98, 120, 103, 125], "label": "drone", "frame_id": "42", "frame_idx": 42, "interpolated": true, "extrapolated": true}, {"bbox": [67, 89, 72, 94], "label": "drone", "frame_id": "42", "frame_idx": 42, "interpolated": true, "extrapolated": true}], "43": [{"bbox": [98, 120, 103, 125], "label": "drone", "frame_id": "43", "frame_idx": 43, "interpolated": true, "extrapolated": true}, {"bbox": [67, 89, 72, 94], "label": "drone", "frame_id": "43", "frame_idx": 43, "interpolated": true, "extrapolated": true}], "44": [{"bbox": [98, 120, 103, 125], "label": "drone", "frame_id": "44", "frame_idx": 44, "interpolated": true, "extrapolated": true}, {"bbox": [67, 89, 72, 94], "label": "drone", "frame_id": "44", "frame_idx": 44, "interpolated": true, "extrapolated": true}], "45": [{"bbox": [98, 120, 103, 125], "label": "drone", "frame_id": "45", "frame_idx": 45, "interpolated": true, "extrapolated": true}, {"bbox": [67, 89, 72, 94], "label": "drone", "frame_id": "45", "frame_idx": 45, "interpolated": true, "extrapolated": true}], "46": [{"bbox": [98, 120, 103, 125], "label": "drone", "frame_id": "46", "frame_idx": 46, "interpolated": true, "extrapolated": true}, {"bbox": [67, 89, 72, 94], "label": "drone", "frame_id": "46", "frame_idx": 46, "interpolated": true, "extrapolated": true}], "47": [{"bbox": [98, 120, 103, 125], "label": "drone", "frame_id": "47", "frame_idx": 47, "interpolated": true, "extrapolated": true}, {"bbox": [67, 89, 72, 94], "label": "drone", "frame_id": "47", "frame_idx": 47, "interpolated": true, "extrapolated": true}], "48": [{"bbox": [98, 120, 103, 125], "label": "drone", "frame_id": "48", "frame_idx": 48, "interpolated": true, "extrapolated": true}, {"bbox": [67, 89, 72, 94], "label": "drone", "frame_id": "48", "frame_idx": 48, "interpolated": true, "extrapolated": true}], "49": [{"bbox": [98, 120, 103, 125], "label": "drone", "frame_id": "49", "frame_idx": 49, "interpolated": true, "extrapolated": true}, {"bbox": [67, 89, 72, 94], "label": "drone", "frame_id": "49", "frame_idx": 49, "interpolated": true, "extrapolated": true}]}}, "data19": {"sequence_id": "data19", "total_frames": 75, "key_frames": 8, "total_detections": 150, "direct_detections": 16, "interpolated_detections": 134, "metrics": {"precision": 0.0, "recall": 0, "f1": 0, "tp": 0, "fp": 150, "fn": 0}, "detections": {"0": [{"bbox": [238, 192, 252, 206], "label": "drone", "frame_idx": 0, "frame_id": "0"}, {"bbox": [209, 187, 254, 236], "label": "小目标", "frame_idx": 0, "frame_id": "0"}], "10": [{"bbox": [265, 189, 276, 200], "label": "小目标", "frame_idx": 10, "frame_id": "10"}, {"bbox": [243, 189, 276, 222], "label": "drone", "frame_idx": 10, "frame_id": "10"}], "20": [{"bbox": [252, 187, 292, 227], "label": "drone", "frame_id": "20", "frame_idx": 20}, {"bbox": [250, 194, 261, 205], "label": "小目标", "frame_idx": 20, "frame_id": "20"}], "30": [{"bbox": [247, 189, 290, 230], "label": "drone", "frame_id": "30", "frame_idx": 30}, {"bbox": [189, 173, 246, 228], "label": "drone", "frame_idx": 30, "frame_id": "30"}, {"bbox": [246, 190, 258, 202], "label": "小目标", "frame_idx": 30, "frame_id": "30"}], "40": [{"bbox": [237, 189, 277, 229], "label": "drone", "frame_id": "40", "frame_idx": 40}, {"bbox": [209, 187, 254, 236], "label": "小目标", "frame_idx": 40, "frame_id": "40"}], "50": [{"bbox": [238, 193, 250, 205], "label": "drone", "frame_idx": 50, "frame_id": "50"}, {"bbox": [209, 187, 254, 236], "label": "小目标", "frame_idx": 50, "frame_id": "50"}], "60": [{"bbox": [255, 188, 271, 204], "label": "小目标", "frame_idx": 60, "frame_id": "60"}], "70": [{"bbox": [284, 188, 306, 210], "label": "drone", "frame_id": "70", "frame_idx": 70}, {"bbox": [259, 189, 274, 203], "label": "小目标", "frame_idx": 70, "frame_id": "70"}], "1": [{"bbox": [238, 191, 254, 207], "label": "drone", "frame_id": "1", "frame_idx": 1, "interpolated": true, "trajectory_based": true}, {"bbox": [214, 187, 256, 232], "label": "小目标", "frame_id": "1", "frame_idx": 1, "interpolated": true, "trajectory_based": true}], "2": [{"bbox": [239, 191, 256, 209], "label": "drone", "frame_id": "2", "frame_idx": 2, "interpolated": true, "trajectory_based": true}, {"bbox": [220, 187, 258, 228], "label": "小目标", "frame_id": "2", "frame_idx": 2, "interpolated": true, "trajectory_based": true}], "3": [{"bbox": [239, 191, 259, 210], "label": "drone", "frame_id": "3", "frame_idx": 3, "interpolated": true, "trajectory_based": true}, {"bbox": [225, 187, 260, 225], "label": "小目标", "frame_id": "3", "frame_idx": 3, "interpolated": true, "trajectory_based": true}], "4": [{"bbox": [240, 190, 261, 212], "label": "drone", "frame_id": "4", "frame_idx": 4, "interpolated": true, "trajectory_based": true}, {"bbox": [231, 187, 262, 221], "label": "小目标", "frame_id": "4", "frame_idx": 4, "interpolated": true, "trajectory_based": true}], "5": [{"bbox": [240, 190, 264, 214], "label": "drone", "frame_id": "5", "frame_idx": 5, "interpolated": true, "trajectory_based": true}, {"bbox": [237, 188, 265, 218], "label": "小目标", "frame_id": "5", "frame_idx": 5, "interpolated": true, "trajectory_based": true}], "6": [{"bbox": [241, 190, 266, 215], "label": "drone", "frame_id": "6", "frame_idx": 6, "interpolated": true, "trajectory_based": true}, {"bbox": [242, 188, 267, 214], "label": "小目标", "frame_id": "6", "frame_idx": 6, "interpolated": true, "trajectory_based": true}], "7": [{"bbox": [241, 189, 268, 217], "label": "drone", "frame_id": "7", "frame_idx": 7, "interpolated": true, "trajectory_based": true}, {"bbox": [248, 188, 269, 210], "label": "小目标", "frame_id": "7", "frame_idx": 7, "interpolated": true, "trajectory_based": true}], "8": [{"bbox": [242, 189, 271, 218], "label": "drone", "frame_id": "8", "frame_idx": 8, "interpolated": true, "trajectory_based": true}, {"bbox": [253, 188, 271, 207], "label": "小目标", "frame_id": "8", "frame_idx": 8, "interpolated": true, "trajectory_based": true}], "9": [{"bbox": [242, 189, 273, 220], "label": "drone", "frame_id": "9", "frame_idx": 9, "interpolated": true, "trajectory_based": true}, {"bbox": [259, 188, 273, 203], "label": "小目标", "frame_id": "9", "frame_idx": 9, "interpolated": true, "trajectory_based": true}], "11": [{"bbox": [243, 188, 277, 222], "label": "drone", "frame_id": "11", "frame_idx": 11, "interpolated": true, "trajectory_based": true}, {"bbox": [263, 189, 274, 200], "label": "小目标", "frame_id": "11", "frame_idx": 11, "interpolated": true, "trajectory_based": true}], "12": [{"bbox": [244, 188, 279, 223], "label": "drone", "frame_id": "12", "frame_idx": 12, "interpolated": true, "trajectory_based": true}, {"bbox": [262, 190, 273, 201], "label": "小目标", "frame_id": "12", "frame_idx": 12, "interpolated": true, "trajectory_based": true}], "13": [{"bbox": [245, 188, 280, 223], "label": "drone", "frame_id": "13", "frame_idx": 13, "interpolated": true, "trajectory_based": true}, {"bbox": [260, 190, 271, 201], "label": "小目标", "frame_id": "13", "frame_idx": 13, "interpolated": true, "trajectory_based": true}], "14": [{"bbox": [246, 188, 282, 224], "label": "drone", "frame_id": "14", "frame_idx": 14, "interpolated": true, "trajectory_based": true}, {"bbox": [259, 191, 270, 202], "label": "小目标", "frame_id": "14", "frame_idx": 14, "interpolated": true, "trajectory_based": true}], "15": [{"bbox": [247, 188, 284, 224], "label": "drone", "frame_id": "15", "frame_idx": 15, "interpolated": true, "trajectory_based": true}, {"bbox": [257, 191, 268, 202], "label": "小目标", "frame_id": "15", "frame_idx": 15, "interpolated": true, "trajectory_based": true}], "16": [{"bbox": [248, 187, 285, 225], "label": "drone", "frame_id": "16", "frame_idx": 16, "interpolated": true, "trajectory_based": true}, {"bbox": [256, 192, 267, 203], "label": "小目标", "frame_id": "16", "frame_idx": 16, "interpolated": true, "trajectory_based": true}], "17": [{"bbox": [249, 187, 287, 225], "label": "drone", "frame_id": "17", "frame_idx": 17, "interpolated": true, "trajectory_based": true}, {"bbox": [254, 192, 265, 203], "label": "小目标", "frame_id": "17", "frame_idx": 17, "interpolated": true, "trajectory_based": true}], "18": [{"bbox": [250, 187, 288, 226], "label": "drone", "frame_id": "18", "frame_idx": 18, "interpolated": true, "trajectory_based": true}, {"bbox": [253, 193, 264, 204], "label": "小目标", "frame_id": "18", "frame_idx": 18, "interpolated": true, "trajectory_based": true}], "19": [{"bbox": [251, 187, 290, 226], "label": "drone", "frame_id": "19", "frame_idx": 19, "interpolated": true, "trajectory_based": true}, {"bbox": [251, 193, 262, 204], "label": "小目标", "frame_id": "19", "frame_idx": 19, "interpolated": true, "trajectory_based": true}], "21": [{"bbox": [251, 187, 291, 227], "label": "drone", "frame_id": "21", "frame_idx": 21, "interpolated": true, "trajectory_based": true}, {"bbox": [249, 193, 260, 204], "label": "小目标", "frame_id": "21", "frame_idx": 21, "interpolated": true, "trajectory_based": true}], "22": [{"bbox": [251, 187, 291, 227], "label": "drone", "frame_id": "22", "frame_idx": 22, "interpolated": true, "trajectory_based": true}, {"bbox": [249, 193, 260, 204], "label": "小目标", "frame_id": "22", "frame_idx": 22, "interpolated": true, "trajectory_based": true}], "23": [{"bbox": [250, 187, 291, 227], "label": "drone", "frame_id": "23", "frame_idx": 23, "interpolated": true, "trajectory_based": true}, {"bbox": [248, 192, 260, 204], "label": "小目标", "frame_id": "23", "frame_idx": 23, "interpolated": true, "trajectory_based": true}], "24": [{"bbox": [250, 187, 291, 228], "label": "drone", "frame_id": "24", "frame_idx": 24, "interpolated": true, "trajectory_based": true}, {"bbox": [248, 192, 259, 203], "label": "小目标", "frame_id": "24", "frame_idx": 24, "interpolated": true, "trajectory_based": true}], "25": [{"bbox": [249, 188, 291, 228], "label": "drone", "frame_id": "25", "frame_idx": 25, "interpolated": true, "trajectory_based": true}, {"bbox": [248, 192, 259, 203], "label": "小目标", "frame_id": "25", "frame_idx": 25, "interpolated": true, "trajectory_based": true}], "26": [{"bbox": [249, 188, 290, 228], "label": "drone", "frame_id": "26", "frame_idx": 26, "interpolated": true, "trajectory_based": true}, {"bbox": [247, 191, 259, 203], "label": "小目标", "frame_id": "26", "frame_idx": 26, "interpolated": true, "trajectory_based": true}], "27": [{"bbox": [248, 188, 290, 229], "label": "drone", "frame_id": "27", "frame_idx": 27, "interpolated": true, "trajectory_based": true}, {"bbox": [247, 191, 258, 202], "label": "小目标", "frame_id": "27", "frame_idx": 27, "interpolated": true, "trajectory_based": true}], "28": [{"bbox": [248, 188, 290, 229], "label": "drone", "frame_id": "28", "frame_idx": 28, "interpolated": true, "trajectory_based": true}, {"bbox": [246, 190, 258, 202], "label": "小目标", "frame_id": "28", "frame_idx": 28, "interpolated": true, "trajectory_based": true}], "29": [{"bbox": [247, 188, 290, 229], "label": "drone", "frame_id": "29", "frame_idx": 29, "interpolated": true, "trajectory_based": true}, {"bbox": [246, 190, 258, 202], "label": "小目标", "frame_id": "29", "frame_idx": 29, "interpolated": true, "trajectory_based": true}], "31": [{"bbox": [246, 189, 288, 229], "label": "drone", "frame_id": "31", "frame_idx": 31, "interpolated": true, "trajectory_based": true}, {"bbox": [242, 189, 257, 205], "label": "小目标", "frame_id": "31", "frame_idx": 31, "interpolated": true, "trajectory_based": true}], "32": [{"bbox": [245, 189, 287, 229], "label": "drone", "frame_id": "32", "frame_idx": 32, "interpolated": true, "trajectory_based": true}, {"bbox": [238, 189, 257, 208], "label": "小目标", "frame_id": "32", "frame_idx": 32, "interpolated": true, "trajectory_based": true}], "33": [{"bbox": [244, 189, 286, 229], "label": "drone", "frame_id": "33", "frame_idx": 33, "interpolated": true, "trajectory_based": true}, {"bbox": [234, 189, 256, 212], "label": "小目标", "frame_id": "33", "frame_idx": 33, "interpolated": true, "trajectory_based": true}], "34": [{"bbox": [243, 189, 284, 229], "label": "drone", "frame_id": "34", "frame_idx": 34, "interpolated": true, "trajectory_based": true}, {"bbox": [231, 188, 256, 215], "label": "小目标", "frame_id": "34", "frame_idx": 34, "interpolated": true, "trajectory_based": true}], "35": [{"bbox": [242, 189, 283, 229], "label": "drone", "frame_id": "35", "frame_idx": 35, "interpolated": true, "trajectory_based": true}, {"bbox": [227, 188, 256, 219], "label": "小目标", "frame_id": "35", "frame_idx": 35, "interpolated": true, "trajectory_based": true}], "36": [{"bbox": [241, 189, 282, 229], "label": "drone", "frame_id": "36", "frame_idx": 36, "interpolated": true, "trajectory_based": true}, {"bbox": [223, 188, 255, 222], "label": "小目标", "frame_id": "36", "frame_idx": 36, "interpolated": true, "trajectory_based": true}], "37": [{"bbox": [240, 189, 280, 229], "label": "drone", "frame_id": "37", "frame_idx": 37, "interpolated": true, "trajectory_based": true}, {"bbox": [220, 187, 255, 225], "label": "小目标", "frame_id": "37", "frame_idx": 37, "interpolated": true, "trajectory_based": true}], "38": [{"bbox": [239, 189, 279, 229], "label": "drone", "frame_id": "38", "frame_idx": 38, "interpolated": true, "trajectory_based": true}, {"bbox": [216, 187, 254, 229], "label": "小目标", "frame_id": "38", "frame_idx": 38, "interpolated": true, "trajectory_based": true}], "39": [{"bbox": [238, 189, 278, 229], "label": "drone", "frame_id": "39", "frame_idx": 39, "interpolated": true, "trajectory_based": true}, {"bbox": [212, 187, 254, 232], "label": "小目标", "frame_id": "39", "frame_idx": 39, "interpolated": true, "trajectory_based": true}], "41": [{"bbox": [237, 189, 274, 226], "label": "drone", "frame_id": "41", "frame_idx": 41, "interpolated": true, "trajectory_based": true}, {"bbox": [209, 187, 254, 236], "label": "小目标", "frame_id": "41", "frame_idx": 41, "interpolated": true, "trajectory_based": true}], "42": [{"bbox": [237, 189, 271, 224], "label": "drone", "frame_id": "42", "frame_idx": 42, "interpolated": true, "trajectory_based": true}, {"bbox": [209, 187, 254, 236], "label": "小目标", "frame_id": "42", "frame_idx": 42, "interpolated": true, "trajectory_based": true}], "43": [{"bbox": [237, 190, 268, 221], "label": "drone", "frame_id": "43", "frame_idx": 43, "interpolated": true, "trajectory_based": true}, {"bbox": [209, 187, 254, 236], "label": "小目标", "frame_id": "43", "frame_idx": 43, "interpolated": true, "trajectory_based": true}], "44": [{"bbox": [237, 190, 266, 219], "label": "drone", "frame_id": "44", "frame_idx": 44, "interpolated": true, "trajectory_based": true}, {"bbox": [209, 187, 254, 236], "label": "小目标", "frame_id": "44", "frame_idx": 44, "interpolated": true, "trajectory_based": true}], "45": [{"bbox": [237, 191, 263, 217], "label": "drone", "frame_id": "45", "frame_idx": 45, "interpolated": true, "trajectory_based": true}, {"bbox": [209, 187, 254, 236], "label": "小目标", "frame_id": "45", "frame_idx": 45, "interpolated": true, "trajectory_based": true}], "46": [{"bbox": [237, 191, 260, 214], "label": "drone", "frame_id": "46", "frame_idx": 46, "interpolated": true, "trajectory_based": true}, {"bbox": [209, 187, 254, 236], "label": "小目标", "frame_id": "46", "frame_idx": 46, "interpolated": true, "trajectory_based": true}], "47": [{"bbox": [237, 191, 258, 212], "label": "drone", "frame_id": "47", "frame_idx": 47, "interpolated": true, "trajectory_based": true}, {"bbox": [209, 187, 254, 236], "label": "小目标", "frame_id": "47", "frame_idx": 47, "interpolated": true, "trajectory_based": true}], "48": [{"bbox": [237, 192, 255, 209], "label": "drone", "frame_id": "48", "frame_idx": 48, "interpolated": true, "trajectory_based": true}, {"bbox": [209, 187, 254, 236], "label": "小目标", "frame_id": "48", "frame_idx": 48, "interpolated": true, "trajectory_based": true}], "49": [{"bbox": [237, 192, 252, 207], "label": "drone", "frame_id": "49", "frame_idx": 49, "interpolated": true, "trajectory_based": true}, {"bbox": [209, 187, 254, 236], "label": "小目标", "frame_id": "49", "frame_idx": 49, "interpolated": true, "trajectory_based": true}], "51": [{"bbox": [239, 192, 252, 204], "label": "drone", "frame_id": "51", "frame_idx": 51, "interpolated": true, "trajectory_based": true}, {"bbox": [209, 187, 254, 236], "label": "小目标", "frame_id": "51", "frame_idx": 51, "interpolated": true, "extrapolated": true}], "52": [{"bbox": [241, 192, 254, 204], "label": "drone", "frame_id": "52", "frame_idx": 52, "interpolated": true, "trajectory_based": true}, {"bbox": [209, 187, 254, 236], "label": "小目标", "frame_id": "52", "frame_idx": 52, "interpolated": true, "extrapolated": true}], "53": [{"bbox": [243, 191, 256, 204], "label": "drone", "frame_id": "53", "frame_idx": 53, "interpolated": true, "trajectory_based": true}, {"bbox": [209, 187, 254, 236], "label": "小目标", "frame_id": "53", "frame_idx": 53, "interpolated": true, "extrapolated": true}], "54": [{"bbox": [244, 191, 258, 204], "label": "drone", "frame_id": "54", "frame_idx": 54, "interpolated": true, "trajectory_based": true}, {"bbox": [209, 187, 254, 236], "label": "小目标", "frame_id": "54", "frame_idx": 54, "interpolated": true, "extrapolated": true}], "55": [{"bbox": [246, 190, 260, 204], "label": "drone", "frame_id": "55", "frame_idx": 55, "interpolated": true, "trajectory_based": true}, {"bbox": [209, 187, 254, 236], "label": "小目标", "frame_id": "55", "frame_idx": 55, "interpolated": true, "extrapolated": true}], "56": [{"bbox": [248, 190, 262, 204], "label": "drone", "frame_id": "56", "frame_idx": 56, "interpolated": true, "trajectory_based": true}, {"bbox": [209, 187, 254, 236], "label": "小目标", "frame_id": "56", "frame_idx": 56, "interpolated": true, "extrapolated": true}], "57": [{"bbox": [249, 189, 264, 204], "label": "drone", "frame_id": "57", "frame_idx": 57, "interpolated": true, "trajectory_based": true}, {"bbox": [209, 187, 254, 236], "label": "小目标", "frame_id": "57", "frame_idx": 57, "interpolated": true, "extrapolated": true}], "58": [{"bbox": [251, 189, 266, 204], "label": "drone", "frame_id": "58", "frame_idx": 58, "interpolated": true, "trajectory_based": true}, {"bbox": [209, 187, 254, 236], "label": "小目标", "frame_id": "58", "frame_idx": 58, "interpolated": true, "extrapolated": true}], "59": [{"bbox": [253, 188, 268, 204], "label": "drone", "frame_id": "59", "frame_idx": 59, "interpolated": true, "trajectory_based": true}, {"bbox": [209, 187, 254, 236], "label": "小目标", "frame_id": "59", "frame_idx": 59, "interpolated": true, "extrapolated": true}], "61": [{"bbox": [255, 188, 271, 203], "label": "小目标", "frame_id": "61", "frame_idx": 61, "interpolated": true, "trajectory_based": true}, {"bbox": [209, 187, 254, 236], "label": "小目标", "frame_id": "61", "frame_idx": 61, "interpolated": true, "extrapolated": true}], "62": [{"bbox": [255, 188, 271, 203], "label": "小目标", "frame_id": "62", "frame_idx": 62, "interpolated": true, "trajectory_based": true}, {"bbox": [209, 187, 254, 236], "label": "小目标", "frame_id": "62", "frame_idx": 62, "interpolated": true, "extrapolated": true}], "63": [{"bbox": [256, 188, 271, 203], "label": "小目标", "frame_id": "63", "frame_idx": 63, "interpolated": true, "trajectory_based": true}, {"bbox": [209, 187, 254, 236], "label": "小目标", "frame_id": "63", "frame_idx": 63, "interpolated": true, "extrapolated": true}], "64": [{"bbox": [256, 188, 272, 203], "label": "小目标", "frame_id": "64", "frame_idx": 64, "interpolated": true, "trajectory_based": true}, {"bbox": [209, 187, 254, 236], "label": "小目标", "frame_id": "64", "frame_idx": 64, "interpolated": true, "extrapolated": true}], "65": [{"bbox": [257, 188, 272, 203], "label": "小目标", "frame_id": "65", "frame_idx": 65, "interpolated": true, "trajectory_based": true}, {"bbox": [209, 187, 254, 236], "label": "小目标", "frame_id": "65", "frame_idx": 65, "interpolated": true, "extrapolated": true}], "66": [{"bbox": [257, 188, 272, 203], "label": "小目标", "frame_id": "66", "frame_idx": 66, "interpolated": true, "trajectory_based": true}, {"bbox": [209, 187, 254, 236], "label": "小目标", "frame_id": "66", "frame_idx": 66, "interpolated": true, "extrapolated": true}], "67": [{"bbox": [257, 188, 273, 203], "label": "小目标", "frame_id": "67", "frame_idx": 67, "interpolated": true, "trajectory_based": true}, {"bbox": [209, 187, 254, 236], "label": "小目标", "frame_id": "67", "frame_idx": 67, "interpolated": true, "extrapolated": true}], "68": [{"bbox": [258, 188, 273, 203], "label": "小目标", "frame_id": "68", "frame_idx": 68, "interpolated": true, "trajectory_based": true}, {"bbox": [209, 187, 254, 236], "label": "小目标", "frame_id": "68", "frame_idx": 68, "interpolated": true, "extrapolated": true}], "69": [{"bbox": [258, 188, 273, 203], "label": "小目标", "frame_id": "69", "frame_idx": 69, "interpolated": true, "trajectory_based": true}, {"bbox": [209, 187, 254, 236], "label": "小目标", "frame_id": "69", "frame_idx": 69, "interpolated": true, "extrapolated": true}], "71": [{"bbox": [259, 189, 274, 203], "label": "小目标", "frame_id": "71", "frame_idx": 71, "interpolated": true, "extrapolated": true}, {"bbox": [209, 187, 254, 236], "label": "小目标", "frame_id": "71", "frame_idx": 71, "interpolated": true, "extrapolated": true}], "72": [{"bbox": [259, 189, 274, 203], "label": "小目标", "frame_id": "72", "frame_idx": 72, "interpolated": true, "extrapolated": true}, {"bbox": [209, 187, 254, 236], "label": "小目标", "frame_id": "72", "frame_idx": 72, "interpolated": true, "extrapolated": true}], "73": [{"bbox": [259, 189, 274, 203], "label": "小目标", "frame_id": "73", "frame_idx": 73, "interpolated": true, "extrapolated": true}, {"bbox": [209, 187, 254, 236], "label": "小目标", "frame_id": "73", "frame_idx": 73, "interpolated": true, "extrapolated": true}], "74": [{"bbox": [259, 189, 274, 203], "label": "小目标", "frame_id": "74", "frame_idx": 74, "interpolated": true, "extrapolated": true}, {"bbox": [209, 187, 254, 236], "label": "小目标", "frame_id": "74", "frame_idx": 74, "interpolated": true, "extrapolated": true}]}}, "data23": {"sequence_id": "data23", "total_frames": 50, "key_frames": 5, "total_detections": 144, "direct_detections": 9, "interpolated_detections": 135, "metrics": {"precision": 0.0, "recall": 0, "f1": 0, "tp": 0, "fp": 144, "fn": 0}, "detections": {"0": [{"bbox": [478, 259, 483, 264], "label": "drone", "frame_id": "0", "frame_idx": 0}, {"bbox": [63, 94, 78, 109], "label": "小目标", "frame_idx": 0, "frame_id": "0"}], "10": [{"bbox": [64, 90, 78, 104], "label": "小目标", "frame_idx": 10, "frame_id": "10"}], "20": [{"bbox": [60, 84, 75, 99], "label": "小目标", "frame_idx": 20, "frame_id": "20"}], "30": [{"bbox": [597, 280, 602, 285], "label": "drone", "frame_id": "30", "frame_idx": 30}, {"bbox": [479, 280, 486, 287], "label": "小目标", "frame_idx": 30, "frame_id": "30"}], "40": [{"bbox": [597, 278, 607, 288], "label": "drone", "frame_id": "40", "frame_idx": 40}, {"bbox": [6, 7, 14, 8], "label": "drone", "frame_idx": 40, "frame_id": "40"}, {"bbox": [495, 276, 504, 285], "label": "小目标", "frame_idx": 40, "frame_id": "40"}], "1": [{"bbox": [63, 93, 78, 108], "label": "小目标", "frame_id": "1", "frame_idx": 1, "interpolated": true, "trajectory_based": true}, {"bbox": [597, 280, 602, 285], "label": "drone", "frame_id": "1", "frame_idx": 1, "interpolated": true, "extrapolated": true}, {"bbox": [479, 280, 486, 287], "label": "小目标", "frame_id": "1", "frame_idx": 1, "interpolated": true, "extrapolated": true}], "2": [{"bbox": [63, 93, 78, 108], "label": "小目标", "frame_id": "2", "frame_idx": 2, "interpolated": true, "trajectory_based": true}, {"bbox": [597, 280, 602, 285], "label": "drone", "frame_id": "2", "frame_idx": 2, "interpolated": true, "extrapolated": true}, {"bbox": [479, 280, 486, 287], "label": "小目标", "frame_id": "2", "frame_idx": 2, "interpolated": true, "extrapolated": true}], "3": [{"bbox": [63, 92, 78, 107], "label": "小目标", "frame_id": "3", "frame_idx": 3, "interpolated": true, "trajectory_based": true}, {"bbox": [597, 280, 602, 285], "label": "drone", "frame_id": "3", "frame_idx": 3, "interpolated": true, "extrapolated": true}, {"bbox": [479, 280, 486, 287], "label": "小目标", "frame_id": "3", "frame_idx": 3, "interpolated": true, "extrapolated": true}], "4": [{"bbox": [63, 92, 78, 107], "label": "小目标", "frame_id": "4", "frame_idx": 4, "interpolated": true, "trajectory_based": true}, {"bbox": [597, 280, 602, 285], "label": "drone", "frame_id": "4", "frame_idx": 4, "interpolated": true, "extrapolated": true}, {"bbox": [479, 280, 486, 287], "label": "小目标", "frame_id": "4", "frame_idx": 4, "interpolated": true, "extrapolated": true}], "5": [{"bbox": [63, 92, 78, 106], "label": "小目标", "frame_id": "5", "frame_idx": 5, "interpolated": true, "trajectory_based": true}, {"bbox": [597, 280, 602, 285], "label": "drone", "frame_id": "5", "frame_idx": 5, "interpolated": true, "extrapolated": true}, {"bbox": [479, 280, 486, 287], "label": "小目标", "frame_id": "5", "frame_idx": 5, "interpolated": true, "extrapolated": true}], "6": [{"bbox": [63, 91, 78, 106], "label": "小目标", "frame_id": "6", "frame_idx": 6, "interpolated": true, "trajectory_based": true}, {"bbox": [597, 280, 602, 285], "label": "drone", "frame_id": "6", "frame_idx": 6, "interpolated": true, "extrapolated": true}, {"bbox": [479, 280, 486, 287], "label": "小目标", "frame_id": "6", "frame_idx": 6, "interpolated": true, "extrapolated": true}], "7": [{"bbox": [63, 91, 78, 105], "label": "小目标", "frame_id": "7", "frame_idx": 7, "interpolated": true, "trajectory_based": true}, {"bbox": [597, 280, 602, 285], "label": "drone", "frame_id": "7", "frame_idx": 7, "interpolated": true, "extrapolated": true}, {"bbox": [479, 280, 486, 287], "label": "小目标", "frame_id": "7", "frame_idx": 7, "interpolated": true, "extrapolated": true}], "8": [{"bbox": [63, 90, 78, 105], "label": "小目标", "frame_id": "8", "frame_idx": 8, "interpolated": true, "trajectory_based": true}, {"bbox": [597, 280, 602, 285], "label": "drone", "frame_id": "8", "frame_idx": 8, "interpolated": true, "extrapolated": true}, {"bbox": [479, 280, 486, 287], "label": "小目标", "frame_id": "8", "frame_idx": 8, "interpolated": true, "extrapolated": true}], "9": [{"bbox": [63, 90, 78, 104], "label": "小目标", "frame_id": "9", "frame_idx": 9, "interpolated": true, "trajectory_based": true}, {"bbox": [597, 280, 602, 285], "label": "drone", "frame_id": "9", "frame_idx": 9, "interpolated": true, "extrapolated": true}, {"bbox": [479, 280, 486, 287], "label": "小目标", "frame_id": "9", "frame_idx": 9, "interpolated": true, "extrapolated": true}], "11": [{"bbox": [63, 89, 77, 103], "label": "小目标", "frame_id": "11", "frame_idx": 11, "interpolated": true, "trajectory_based": true}, {"bbox": [597, 280, 602, 285], "label": "drone", "frame_id": "11", "frame_idx": 11, "interpolated": true, "extrapolated": true}, {"bbox": [479, 280, 486, 287], "label": "小目标", "frame_id": "11", "frame_idx": 11, "interpolated": true, "extrapolated": true}], "12": [{"bbox": [63, 88, 77, 103], "label": "小目标", "frame_id": "12", "frame_idx": 12, "interpolated": true, "trajectory_based": true}, {"bbox": [597, 280, 602, 285], "label": "drone", "frame_id": "12", "frame_idx": 12, "interpolated": true, "extrapolated": true}, {"bbox": [479, 280, 486, 287], "label": "小目标", "frame_id": "12", "frame_idx": 12, "interpolated": true, "extrapolated": true}], "13": [{"bbox": [62, 88, 77, 102], "label": "小目标", "frame_id": "13", "frame_idx": 13, "interpolated": true, "trajectory_based": true}, {"bbox": [597, 280, 602, 285], "label": "drone", "frame_id": "13", "frame_idx": 13, "interpolated": true, "extrapolated": true}, {"bbox": [479, 280, 486, 287], "label": "小目标", "frame_id": "13", "frame_idx": 13, "interpolated": true, "extrapolated": true}], "14": [{"bbox": [62, 87, 76, 102], "label": "小目标", "frame_id": "14", "frame_idx": 14, "interpolated": true, "trajectory_based": true}, {"bbox": [597, 280, 602, 285], "label": "drone", "frame_id": "14", "frame_idx": 14, "interpolated": true, "extrapolated": true}, {"bbox": [479, 280, 486, 287], "label": "小目标", "frame_id": "14", "frame_idx": 14, "interpolated": true, "extrapolated": true}], "15": [{"bbox": [62, 87, 76, 101], "label": "小目标", "frame_id": "15", "frame_idx": 15, "interpolated": true, "trajectory_based": true}, {"bbox": [597, 280, 602, 285], "label": "drone", "frame_id": "15", "frame_idx": 15, "interpolated": true, "extrapolated": true}, {"bbox": [479, 280, 486, 287], "label": "小目标", "frame_id": "15", "frame_idx": 15, "interpolated": true, "extrapolated": true}], "16": [{"bbox": [61, 86, 76, 101], "label": "小目标", "frame_id": "16", "frame_idx": 16, "interpolated": true, "trajectory_based": true}, {"bbox": [597, 280, 602, 285], "label": "drone", "frame_id": "16", "frame_idx": 16, "interpolated": true, "extrapolated": true}, {"bbox": [479, 280, 486, 287], "label": "小目标", "frame_id": "16", "frame_idx": 16, "interpolated": true, "extrapolated": true}], "17": [{"bbox": [61, 85, 75, 100], "label": "小目标", "frame_id": "17", "frame_idx": 17, "interpolated": true, "trajectory_based": true}, {"bbox": [597, 280, 602, 285], "label": "drone", "frame_id": "17", "frame_idx": 17, "interpolated": true, "extrapolated": true}, {"bbox": [479, 280, 486, 287], "label": "小目标", "frame_id": "17", "frame_idx": 17, "interpolated": true, "extrapolated": true}], "18": [{"bbox": [60, 85, 75, 100], "label": "小目标", "frame_id": "18", "frame_idx": 18, "interpolated": true, "trajectory_based": true}, {"bbox": [597, 280, 602, 285], "label": "drone", "frame_id": "18", "frame_idx": 18, "interpolated": true, "extrapolated": true}, {"bbox": [479, 280, 486, 287], "label": "小目标", "frame_id": "18", "frame_idx": 18, "interpolated": true, "extrapolated": true}], "19": [{"bbox": [60, 84, 75, 99], "label": "小目标", "frame_id": "19", "frame_idx": 19, "interpolated": true, "trajectory_based": true}, {"bbox": [597, 280, 602, 285], "label": "drone", "frame_id": "19", "frame_idx": 19, "interpolated": true, "extrapolated": true}, {"bbox": [479, 280, 486, 287], "label": "小目标", "frame_id": "19", "frame_idx": 19, "interpolated": true, "extrapolated": true}], "21": [{"bbox": [60, 84, 75, 99], "label": "小目标", "frame_id": "21", "frame_idx": 21, "interpolated": true, "extrapolated": true}, {"bbox": [597, 280, 602, 285], "label": "drone", "frame_id": "21", "frame_idx": 21, "interpolated": true, "extrapolated": true}, {"bbox": [479, 280, 486, 287], "label": "小目标", "frame_id": "21", "frame_idx": 21, "interpolated": true, "extrapolated": true}], "22": [{"bbox": [60, 84, 75, 99], "label": "小目标", "frame_id": "22", "frame_idx": 22, "interpolated": true, "extrapolated": true}, {"bbox": [597, 280, 602, 285], "label": "drone", "frame_id": "22", "frame_idx": 22, "interpolated": true, "extrapolated": true}, {"bbox": [479, 280, 486, 287], "label": "小目标", "frame_id": "22", "frame_idx": 22, "interpolated": true, "extrapolated": true}], "23": [{"bbox": [60, 84, 75, 99], "label": "小目标", "frame_id": "23", "frame_idx": 23, "interpolated": true, "extrapolated": true}, {"bbox": [597, 280, 602, 285], "label": "drone", "frame_id": "23", "frame_idx": 23, "interpolated": true, "extrapolated": true}, {"bbox": [479, 280, 486, 287], "label": "小目标", "frame_id": "23", "frame_idx": 23, "interpolated": true, "extrapolated": true}], "24": [{"bbox": [60, 84, 75, 99], "label": "小目标", "frame_id": "24", "frame_idx": 24, "interpolated": true, "extrapolated": true}, {"bbox": [597, 280, 602, 285], "label": "drone", "frame_id": "24", "frame_idx": 24, "interpolated": true, "extrapolated": true}, {"bbox": [479, 280, 486, 287], "label": "小目标", "frame_id": "24", "frame_idx": 24, "interpolated": true, "extrapolated": true}], "25": [{"bbox": [60, 84, 75, 99], "label": "小目标", "frame_id": "25", "frame_idx": 25, "interpolated": true, "extrapolated": true}, {"bbox": [597, 280, 602, 285], "label": "drone", "frame_id": "25", "frame_idx": 25, "interpolated": true, "extrapolated": true}, {"bbox": [479, 280, 486, 287], "label": "小目标", "frame_id": "25", "frame_idx": 25, "interpolated": true, "extrapolated": true}], "26": [{"bbox": [60, 84, 75, 99], "label": "小目标", "frame_id": "26", "frame_idx": 26, "interpolated": true, "extrapolated": true}, {"bbox": [597, 280, 602, 285], "label": "drone", "frame_id": "26", "frame_idx": 26, "interpolated": true, "extrapolated": true}, {"bbox": [479, 280, 486, 287], "label": "小目标", "frame_id": "26", "frame_idx": 26, "interpolated": true, "extrapolated": true}], "27": [{"bbox": [60, 84, 75, 99], "label": "小目标", "frame_id": "27", "frame_idx": 27, "interpolated": true, "extrapolated": true}, {"bbox": [597, 280, 602, 285], "label": "drone", "frame_id": "27", "frame_idx": 27, "interpolated": true, "extrapolated": true}, {"bbox": [479, 280, 486, 287], "label": "小目标", "frame_id": "27", "frame_idx": 27, "interpolated": true, "extrapolated": true}], "28": [{"bbox": [60, 84, 75, 99], "label": "小目标", "frame_id": "28", "frame_idx": 28, "interpolated": true, "extrapolated": true}, {"bbox": [597, 280, 602, 285], "label": "drone", "frame_id": "28", "frame_idx": 28, "interpolated": true, "extrapolated": true}, {"bbox": [479, 280, 486, 287], "label": "小目标", "frame_id": "28", "frame_idx": 28, "interpolated": true, "extrapolated": true}], "29": [{"bbox": [60, 84, 75, 99], "label": "小目标", "frame_id": "29", "frame_idx": 29, "interpolated": true, "extrapolated": true}, {"bbox": [597, 280, 602, 285], "label": "drone", "frame_id": "29", "frame_idx": 29, "interpolated": true, "extrapolated": true}, {"bbox": [479, 280, 486, 287], "label": "小目标", "frame_id": "29", "frame_idx": 29, "interpolated": true, "extrapolated": true}], "31": [{"bbox": [60, 84, 75, 99], "label": "小目标", "frame_id": "31", "frame_idx": 31, "interpolated": true, "extrapolated": true}, {"bbox": [597, 279, 602, 285], "label": "drone", "frame_id": "31", "frame_idx": 31, "interpolated": true, "trajectory_based": true}, {"bbox": [480, 279, 487, 286], "label": "小目标", "frame_id": "31", "frame_idx": 31, "interpolated": true, "trajectory_based": true}], "32": [{"bbox": [60, 84, 75, 99], "label": "小目标", "frame_id": "32", "frame_idx": 32, "interpolated": true, "extrapolated": true}, {"bbox": [597, 279, 603, 285], "label": "drone", "frame_id": "32", "frame_idx": 32, "interpolated": true, "trajectory_based": true}, {"bbox": [482, 279, 489, 286], "label": "小目标", "frame_id": "32", "frame_idx": 32, "interpolated": true, "trajectory_based": true}], "33": [{"bbox": [60, 84, 75, 99], "label": "小目标", "frame_id": "33", "frame_idx": 33, "interpolated": true, "extrapolated": true}, {"bbox": [597, 279, 603, 285], "label": "drone", "frame_id": "33", "frame_idx": 33, "interpolated": true, "trajectory_based": true}, {"bbox": [483, 278, 491, 286], "label": "小目标", "frame_id": "33", "frame_idx": 33, "interpolated": true, "trajectory_based": true}], "34": [{"bbox": [60, 84, 75, 99], "label": "小目标", "frame_id": "34", "frame_idx": 34, "interpolated": true, "extrapolated": true}, {"bbox": [597, 279, 604, 286], "label": "drone", "frame_id": "34", "frame_idx": 34, "interpolated": true, "trajectory_based": true}, {"bbox": [485, 278, 493, 286], "label": "小目标", "frame_id": "34", "frame_idx": 34, "interpolated": true, "trajectory_based": true}], "35": [{"bbox": [60, 84, 75, 99], "label": "小目标", "frame_id": "35", "frame_idx": 35, "interpolated": true, "extrapolated": true}, {"bbox": [597, 279, 604, 286], "label": "drone", "frame_id": "35", "frame_idx": 35, "interpolated": true, "trajectory_based": true}, {"bbox": [487, 278, 495, 286], "label": "小目标", "frame_id": "35", "frame_idx": 35, "interpolated": true, "trajectory_based": true}], "36": [{"bbox": [60, 84, 75, 99], "label": "小目标", "frame_id": "36", "frame_idx": 36, "interpolated": true, "extrapolated": true}, {"bbox": [597, 278, 605, 286], "label": "drone", "frame_id": "36", "frame_idx": 36, "interpolated": true, "trajectory_based": true}, {"bbox": [488, 277, 496, 285], "label": "小目标", "frame_id": "36", "frame_idx": 36, "interpolated": true, "trajectory_based": true}], "37": [{"bbox": [60, 84, 75, 99], "label": "小目标", "frame_id": "37", "frame_idx": 37, "interpolated": true, "extrapolated": true}, {"bbox": [597, 278, 605, 287], "label": "drone", "frame_id": "37", "frame_idx": 37, "interpolated": true, "trajectory_based": true}, {"bbox": [490, 277, 498, 285], "label": "小目标", "frame_id": "37", "frame_idx": 37, "interpolated": true, "trajectory_based": true}], "38": [{"bbox": [60, 84, 75, 99], "label": "小目标", "frame_id": "38", "frame_idx": 38, "interpolated": true, "extrapolated": true}, {"bbox": [597, 278, 606, 287], "label": "drone", "frame_id": "38", "frame_idx": 38, "interpolated": true, "trajectory_based": true}, {"bbox": [491, 276, 500, 285], "label": "小目标", "frame_id": "38", "frame_idx": 38, "interpolated": true, "trajectory_based": true}], "39": [{"bbox": [60, 84, 75, 99], "label": "小目标", "frame_id": "39", "frame_idx": 39, "interpolated": true, "extrapolated": true}, {"bbox": [597, 278, 606, 287], "label": "drone", "frame_id": "39", "frame_idx": 39, "interpolated": true, "trajectory_based": true}, {"bbox": [493, 276, 502, 285], "label": "小目标", "frame_id": "39", "frame_idx": 39, "interpolated": true, "trajectory_based": true}], "41": [{"bbox": [60, 84, 75, 99], "label": "小目标", "frame_id": "41", "frame_idx": 41, "interpolated": true, "extrapolated": true}, {"bbox": [597, 278, 607, 288], "label": "drone", "frame_id": "41", "frame_idx": 41, "interpolated": true, "extrapolated": true}, {"bbox": [495, 276, 504, 285], "label": "小目标", "frame_id": "41", "frame_idx": 41, "interpolated": true, "extrapolated": true}], "42": [{"bbox": [60, 84, 75, 99], "label": "小目标", "frame_id": "42", "frame_idx": 42, "interpolated": true, "extrapolated": true}, {"bbox": [597, 278, 607, 288], "label": "drone", "frame_id": "42", "frame_idx": 42, "interpolated": true, "extrapolated": true}, {"bbox": [495, 276, 504, 285], "label": "小目标", "frame_id": "42", "frame_idx": 42, "interpolated": true, "extrapolated": true}], "43": [{"bbox": [60, 84, 75, 99], "label": "小目标", "frame_id": "43", "frame_idx": 43, "interpolated": true, "extrapolated": true}, {"bbox": [597, 278, 607, 288], "label": "drone", "frame_id": "43", "frame_idx": 43, "interpolated": true, "extrapolated": true}, {"bbox": [495, 276, 504, 285], "label": "小目标", "frame_id": "43", "frame_idx": 43, "interpolated": true, "extrapolated": true}], "44": [{"bbox": [60, 84, 75, 99], "label": "小目标", "frame_id": "44", "frame_idx": 44, "interpolated": true, "extrapolated": true}, {"bbox": [597, 278, 607, 288], "label": "drone", "frame_id": "44", "frame_idx": 44, "interpolated": true, "extrapolated": true}, {"bbox": [495, 276, 504, 285], "label": "小目标", "frame_id": "44", "frame_idx": 44, "interpolated": true, "extrapolated": true}], "45": [{"bbox": [60, 84, 75, 99], "label": "小目标", "frame_id": "45", "frame_idx": 45, "interpolated": true, "extrapolated": true}, {"bbox": [597, 278, 607, 288], "label": "drone", "frame_id": "45", "frame_idx": 45, "interpolated": true, "extrapolated": true}, {"bbox": [495, 276, 504, 285], "label": "小目标", "frame_id": "45", "frame_idx": 45, "interpolated": true, "extrapolated": true}], "46": [{"bbox": [60, 84, 75, 99], "label": "小目标", "frame_id": "46", "frame_idx": 46, "interpolated": true, "extrapolated": true}, {"bbox": [597, 278, 607, 288], "label": "drone", "frame_id": "46", "frame_idx": 46, "interpolated": true, "extrapolated": true}, {"bbox": [495, 276, 504, 285], "label": "小目标", "frame_id": "46", "frame_idx": 46, "interpolated": true, "extrapolated": true}], "47": [{"bbox": [60, 84, 75, 99], "label": "小目标", "frame_id": "47", "frame_idx": 47, "interpolated": true, "extrapolated": true}, {"bbox": [597, 278, 607, 288], "label": "drone", "frame_id": "47", "frame_idx": 47, "interpolated": true, "extrapolated": true}, {"bbox": [495, 276, 504, 285], "label": "小目标", "frame_id": "47", "frame_idx": 47, "interpolated": true, "extrapolated": true}], "48": [{"bbox": [60, 84, 75, 99], "label": "小目标", "frame_id": "48", "frame_idx": 48, "interpolated": true, "extrapolated": true}, {"bbox": [597, 278, 607, 288], "label": "drone", "frame_id": "48", "frame_idx": 48, "interpolated": true, "extrapolated": true}, {"bbox": [495, 276, 504, 285], "label": "小目标", "frame_id": "48", "frame_idx": 48, "interpolated": true, "extrapolated": true}], "49": [{"bbox": [60, 84, 75, 99], "label": "小目标", "frame_id": "49", "frame_idx": 49, "interpolated": true, "extrapolated": true}, {"bbox": [597, 278, 607, 288], "label": "drone", "frame_id": "49", "frame_idx": 49, "interpolated": true, "extrapolated": true}, {"bbox": [495, 276, 504, 285], "label": "小目标", "frame_id": "49", "frame_idx": 49, "interpolated": true, "extrapolated": true}]}}, "data26": {"sequence_id": "data26", "total_frames": 75, "key_frames": 8, "total_detections": 353, "direct_detections": 18, "interpolated_detections": 335, "metrics": {"precision": 0.0, "recall": 0, "f1": 0, "tp": 0, "fp": 353, "fn": 0}, "detections": {"0": [{"bbox": [478, 39, 483, 44], "label": "drone", "frame_id": "0", "frame_idx": 0}, {"bbox": [439, 67, 448, 76], "label": "drone", "frame_idx": 0, "frame_id": "0"}, {"bbox": [450, 76, 459, 85], "label": "小目标", "frame_idx": 0, "frame_id": "0"}], "10": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "10", "frame_idx": 10}, {"bbox": [446, 378, 451, 383], "label": "drone", "frame_idx": 10, "frame_id": "10"}, {"bbox": [446, 350, 457, 361], "label": "小目标", "frame_idx": 10, "frame_id": "10"}], "20": [{"bbox": [416, 64, 421, 69], "label": "小目标", "frame_idx": 20, "frame_id": "20"}], "30": [{"bbox": [447, 63, 452, 68], "label": "drone", "frame_id": "30", "frame_idx": 30}, {"bbox": [417, 63, 422, 68], "label": "drone", "frame_idx": 30, "frame_id": "30"}], "40": [{"bbox": [447, 63, 452, 68], "label": "drone", "frame_id": "40", "frame_idx": 40}, {"bbox": [446, 378, 451, 383], "label": "drone", "frame_idx": 40, "frame_id": "40"}, {"bbox": [418, 65, 424, 70], "label": "小目标", "frame_idx": 40, "frame_id": "40"}], "50": [{"bbox": [447, 60, 452, 65], "label": "drone", "frame_id": "50", "frame_idx": 50}, {"bbox": [446, 379, 451, 384], "label": "drone", "frame_idx": 50, "frame_id": "50"}, {"bbox": [446, 370, 458, 382], "label": "小目标", "frame_idx": 50, "frame_id": "50"}], "60": [{"bbox": [416, 64, 421, 69], "label": "小目标", "frame_idx": 60, "frame_id": "60"}], "70": [{"bbox": [412, 62, 418, 69], "label": "drone", "frame_id": "70", "frame_idx": 70}, {"bbox": [409, 65, 417, 73], "label": "drone", "frame_idx": 70, "frame_id": "70"}], "1": [{"bbox": [474, 38, 479, 43], "label": "drone", "frame_id": "1", "frame_idx": 1, "interpolated": true, "trajectory_based": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "1", "frame_idx": 1, "interpolated": true, "extrapolated": true}, {"bbox": [447, 63, 452, 68], "label": "drone", "frame_id": "1", "frame_idx": 1, "interpolated": true, "extrapolated": true}, {"bbox": [446, 378, 451, 383], "label": "drone", "frame_id": "1", "frame_idx": 1, "interpolated": true, "extrapolated": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "1", "frame_idx": 1, "interpolated": true, "extrapolated": true}], "2": [{"bbox": [471, 38, 476, 43], "label": "drone", "frame_id": "2", "frame_idx": 2, "interpolated": true, "trajectory_based": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "2", "frame_idx": 2, "interpolated": true, "extrapolated": true}, {"bbox": [447, 63, 452, 68], "label": "drone", "frame_id": "2", "frame_idx": 2, "interpolated": true, "extrapolated": true}, {"bbox": [446, 378, 451, 383], "label": "drone", "frame_id": "2", "frame_idx": 2, "interpolated": true, "extrapolated": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "2", "frame_idx": 2, "interpolated": true, "extrapolated": true}], "3": [{"bbox": [468, 38, 473, 43], "label": "drone", "frame_id": "3", "frame_idx": 3, "interpolated": true, "trajectory_based": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "3", "frame_idx": 3, "interpolated": true, "extrapolated": true}, {"bbox": [447, 63, 452, 68], "label": "drone", "frame_id": "3", "frame_idx": 3, "interpolated": true, "extrapolated": true}, {"bbox": [446, 378, 451, 383], "label": "drone", "frame_id": "3", "frame_idx": 3, "interpolated": true, "extrapolated": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "3", "frame_idx": 3, "interpolated": true, "extrapolated": true}], "4": [{"bbox": [465, 38, 470, 43], "label": "drone", "frame_id": "4", "frame_idx": 4, "interpolated": true, "trajectory_based": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "4", "frame_idx": 4, "interpolated": true, "extrapolated": true}, {"bbox": [447, 63, 452, 68], "label": "drone", "frame_id": "4", "frame_idx": 4, "interpolated": true, "extrapolated": true}, {"bbox": [446, 378, 451, 383], "label": "drone", "frame_id": "4", "frame_idx": 4, "interpolated": true, "extrapolated": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "4", "frame_idx": 4, "interpolated": true, "extrapolated": true}], "5": [{"bbox": [462, 38, 467, 43], "label": "drone", "frame_id": "5", "frame_idx": 5, "interpolated": true, "trajectory_based": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "5", "frame_idx": 5, "interpolated": true, "extrapolated": true}, {"bbox": [447, 63, 452, 68], "label": "drone", "frame_id": "5", "frame_idx": 5, "interpolated": true, "extrapolated": true}, {"bbox": [446, 378, 451, 383], "label": "drone", "frame_id": "5", "frame_idx": 5, "interpolated": true, "extrapolated": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "5", "frame_idx": 5, "interpolated": true, "extrapolated": true}], "6": [{"bbox": [459, 38, 464, 43], "label": "drone", "frame_id": "6", "frame_idx": 6, "interpolated": true, "trajectory_based": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "6", "frame_idx": 6, "interpolated": true, "extrapolated": true}, {"bbox": [447, 63, 452, 68], "label": "drone", "frame_id": "6", "frame_idx": 6, "interpolated": true, "extrapolated": true}, {"bbox": [446, 378, 451, 383], "label": "drone", "frame_id": "6", "frame_idx": 6, "interpolated": true, "extrapolated": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "6", "frame_idx": 6, "interpolated": true, "extrapolated": true}], "7": [{"bbox": [456, 38, 461, 43], "label": "drone", "frame_id": "7", "frame_idx": 7, "interpolated": true, "trajectory_based": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "7", "frame_idx": 7, "interpolated": true, "extrapolated": true}, {"bbox": [447, 63, 452, 68], "label": "drone", "frame_id": "7", "frame_idx": 7, "interpolated": true, "extrapolated": true}, {"bbox": [446, 378, 451, 383], "label": "drone", "frame_id": "7", "frame_idx": 7, "interpolated": true, "extrapolated": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "7", "frame_idx": 7, "interpolated": true, "extrapolated": true}], "8": [{"bbox": [453, 38, 458, 43], "label": "drone", "frame_id": "8", "frame_idx": 8, "interpolated": true, "trajectory_based": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "8", "frame_idx": 8, "interpolated": true, "extrapolated": true}, {"bbox": [447, 63, 452, 68], "label": "drone", "frame_id": "8", "frame_idx": 8, "interpolated": true, "extrapolated": true}, {"bbox": [446, 378, 451, 383], "label": "drone", "frame_id": "8", "frame_idx": 8, "interpolated": true, "extrapolated": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "8", "frame_idx": 8, "interpolated": true, "extrapolated": true}], "9": [{"bbox": [450, 38, 455, 43], "label": "drone", "frame_id": "9", "frame_idx": 9, "interpolated": true, "trajectory_based": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "9", "frame_idx": 9, "interpolated": true, "extrapolated": true}, {"bbox": [447, 63, 452, 68], "label": "drone", "frame_id": "9", "frame_idx": 9, "interpolated": true, "extrapolated": true}, {"bbox": [446, 378, 451, 383], "label": "drone", "frame_id": "9", "frame_idx": 9, "interpolated": true, "extrapolated": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "9", "frame_idx": 9, "interpolated": true, "extrapolated": true}], "11": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "11", "frame_idx": 11, "interpolated": true, "extrapolated": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "11", "frame_idx": 11, "interpolated": true, "extrapolated": true}, {"bbox": [447, 63, 452, 68], "label": "drone", "frame_id": "11", "frame_idx": 11, "interpolated": true, "extrapolated": true}, {"bbox": [446, 378, 451, 383], "label": "drone", "frame_id": "11", "frame_idx": 11, "interpolated": true, "extrapolated": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "11", "frame_idx": 11, "interpolated": true, "extrapolated": true}], "12": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "12", "frame_idx": 12, "interpolated": true, "extrapolated": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "12", "frame_idx": 12, "interpolated": true, "extrapolated": true}, {"bbox": [447, 63, 452, 68], "label": "drone", "frame_id": "12", "frame_idx": 12, "interpolated": true, "extrapolated": true}, {"bbox": [446, 378, 451, 383], "label": "drone", "frame_id": "12", "frame_idx": 12, "interpolated": true, "extrapolated": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "12", "frame_idx": 12, "interpolated": true, "extrapolated": true}], "13": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "13", "frame_idx": 13, "interpolated": true, "extrapolated": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "13", "frame_idx": 13, "interpolated": true, "extrapolated": true}, {"bbox": [447, 63, 452, 68], "label": "drone", "frame_id": "13", "frame_idx": 13, "interpolated": true, "extrapolated": true}, {"bbox": [446, 378, 451, 383], "label": "drone", "frame_id": "13", "frame_idx": 13, "interpolated": true, "extrapolated": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "13", "frame_idx": 13, "interpolated": true, "extrapolated": true}], "14": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "14", "frame_idx": 14, "interpolated": true, "extrapolated": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "14", "frame_idx": 14, "interpolated": true, "extrapolated": true}, {"bbox": [447, 63, 452, 68], "label": "drone", "frame_id": "14", "frame_idx": 14, "interpolated": true, "extrapolated": true}, {"bbox": [446, 378, 451, 383], "label": "drone", "frame_id": "14", "frame_idx": 14, "interpolated": true, "extrapolated": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "14", "frame_idx": 14, "interpolated": true, "extrapolated": true}], "15": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "15", "frame_idx": 15, "interpolated": true, "extrapolated": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "15", "frame_idx": 15, "interpolated": true, "extrapolated": true}, {"bbox": [447, 63, 452, 68], "label": "drone", "frame_id": "15", "frame_idx": 15, "interpolated": true, "extrapolated": true}, {"bbox": [446, 378, 451, 383], "label": "drone", "frame_id": "15", "frame_idx": 15, "interpolated": true, "extrapolated": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "15", "frame_idx": 15, "interpolated": true, "extrapolated": true}], "16": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "16", "frame_idx": 16, "interpolated": true, "extrapolated": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "16", "frame_idx": 16, "interpolated": true, "extrapolated": true}, {"bbox": [447, 63, 452, 68], "label": "drone", "frame_id": "16", "frame_idx": 16, "interpolated": true, "extrapolated": true}, {"bbox": [446, 378, 451, 383], "label": "drone", "frame_id": "16", "frame_idx": 16, "interpolated": true, "extrapolated": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "16", "frame_idx": 16, "interpolated": true, "extrapolated": true}], "17": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "17", "frame_idx": 17, "interpolated": true, "extrapolated": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "17", "frame_idx": 17, "interpolated": true, "extrapolated": true}, {"bbox": [447, 63, 452, 68], "label": "drone", "frame_id": "17", "frame_idx": 17, "interpolated": true, "extrapolated": true}, {"bbox": [446, 378, 451, 383], "label": "drone", "frame_id": "17", "frame_idx": 17, "interpolated": true, "extrapolated": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "17", "frame_idx": 17, "interpolated": true, "extrapolated": true}], "18": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "18", "frame_idx": 18, "interpolated": true, "extrapolated": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "18", "frame_idx": 18, "interpolated": true, "extrapolated": true}, {"bbox": [447, 63, 452, 68], "label": "drone", "frame_id": "18", "frame_idx": 18, "interpolated": true, "extrapolated": true}, {"bbox": [446, 378, 451, 383], "label": "drone", "frame_id": "18", "frame_idx": 18, "interpolated": true, "extrapolated": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "18", "frame_idx": 18, "interpolated": true, "extrapolated": true}], "19": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "19", "frame_idx": 19, "interpolated": true, "extrapolated": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "19", "frame_idx": 19, "interpolated": true, "extrapolated": true}, {"bbox": [447, 63, 452, 68], "label": "drone", "frame_id": "19", "frame_idx": 19, "interpolated": true, "extrapolated": true}, {"bbox": [446, 378, 451, 383], "label": "drone", "frame_id": "19", "frame_idx": 19, "interpolated": true, "extrapolated": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "19", "frame_idx": 19, "interpolated": true, "extrapolated": true}], "21": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "21", "frame_idx": 21, "interpolated": true, "extrapolated": true}, {"bbox": [416, 63, 421, 68], "label": "小目标", "frame_id": "21", "frame_idx": 21, "interpolated": true, "trajectory_based": true}, {"bbox": [447, 63, 452, 68], "label": "drone", "frame_id": "21", "frame_idx": 21, "interpolated": true, "extrapolated": true}, {"bbox": [446, 378, 451, 383], "label": "drone", "frame_id": "21", "frame_idx": 21, "interpolated": true, "extrapolated": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "21", "frame_idx": 21, "interpolated": true, "extrapolated": true}], "22": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "22", "frame_idx": 22, "interpolated": true, "extrapolated": true}, {"bbox": [416, 63, 421, 68], "label": "小目标", "frame_id": "22", "frame_idx": 22, "interpolated": true, "trajectory_based": true}, {"bbox": [447, 63, 452, 68], "label": "drone", "frame_id": "22", "frame_idx": 22, "interpolated": true, "extrapolated": true}, {"bbox": [446, 378, 451, 383], "label": "drone", "frame_id": "22", "frame_idx": 22, "interpolated": true, "extrapolated": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "22", "frame_idx": 22, "interpolated": true, "extrapolated": true}], "23": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "23", "frame_idx": 23, "interpolated": true, "extrapolated": true}, {"bbox": [416, 63, 421, 68], "label": "小目标", "frame_id": "23", "frame_idx": 23, "interpolated": true, "trajectory_based": true}, {"bbox": [447, 63, 452, 68], "label": "drone", "frame_id": "23", "frame_idx": 23, "interpolated": true, "extrapolated": true}, {"bbox": [446, 378, 451, 383], "label": "drone", "frame_id": "23", "frame_idx": 23, "interpolated": true, "extrapolated": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "23", "frame_idx": 23, "interpolated": true, "extrapolated": true}], "24": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "24", "frame_idx": 24, "interpolated": true, "extrapolated": true}, {"bbox": [416, 63, 421, 68], "label": "小目标", "frame_id": "24", "frame_idx": 24, "interpolated": true, "trajectory_based": true}, {"bbox": [447, 63, 452, 68], "label": "drone", "frame_id": "24", "frame_idx": 24, "interpolated": true, "extrapolated": true}, {"bbox": [446, 378, 451, 383], "label": "drone", "frame_id": "24", "frame_idx": 24, "interpolated": true, "extrapolated": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "24", "frame_idx": 24, "interpolated": true, "extrapolated": true}], "25": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "25", "frame_idx": 25, "interpolated": true, "extrapolated": true}, {"bbox": [416, 63, 421, 68], "label": "小目标", "frame_id": "25", "frame_idx": 25, "interpolated": true, "trajectory_based": true}, {"bbox": [447, 63, 452, 68], "label": "drone", "frame_id": "25", "frame_idx": 25, "interpolated": true, "extrapolated": true}, {"bbox": [446, 378, 451, 383], "label": "drone", "frame_id": "25", "frame_idx": 25, "interpolated": true, "extrapolated": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "25", "frame_idx": 25, "interpolated": true, "extrapolated": true}], "26": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "26", "frame_idx": 26, "interpolated": true, "extrapolated": true}, {"bbox": [416, 63, 421, 68], "label": "小目标", "frame_id": "26", "frame_idx": 26, "interpolated": true, "trajectory_based": true}, {"bbox": [447, 63, 452, 68], "label": "drone", "frame_id": "26", "frame_idx": 26, "interpolated": true, "extrapolated": true}, {"bbox": [446, 378, 451, 383], "label": "drone", "frame_id": "26", "frame_idx": 26, "interpolated": true, "extrapolated": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "26", "frame_idx": 26, "interpolated": true, "extrapolated": true}], "27": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "27", "frame_idx": 27, "interpolated": true, "extrapolated": true}, {"bbox": [416, 63, 421, 68], "label": "小目标", "frame_id": "27", "frame_idx": 27, "interpolated": true, "trajectory_based": true}, {"bbox": [447, 63, 452, 68], "label": "drone", "frame_id": "27", "frame_idx": 27, "interpolated": true, "extrapolated": true}, {"bbox": [446, 378, 451, 383], "label": "drone", "frame_id": "27", "frame_idx": 27, "interpolated": true, "extrapolated": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "27", "frame_idx": 27, "interpolated": true, "extrapolated": true}], "28": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "28", "frame_idx": 28, "interpolated": true, "extrapolated": true}, {"bbox": [416, 63, 421, 68], "label": "小目标", "frame_id": "28", "frame_idx": 28, "interpolated": true, "trajectory_based": true}, {"bbox": [447, 63, 452, 68], "label": "drone", "frame_id": "28", "frame_idx": 28, "interpolated": true, "extrapolated": true}, {"bbox": [446, 378, 451, 383], "label": "drone", "frame_id": "28", "frame_idx": 28, "interpolated": true, "extrapolated": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "28", "frame_idx": 28, "interpolated": true, "extrapolated": true}], "29": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "29", "frame_idx": 29, "interpolated": true, "extrapolated": true}, {"bbox": [416, 63, 421, 68], "label": "小目标", "frame_id": "29", "frame_idx": 29, "interpolated": true, "trajectory_based": true}, {"bbox": [447, 63, 452, 68], "label": "drone", "frame_id": "29", "frame_idx": 29, "interpolated": true, "extrapolated": true}, {"bbox": [446, 378, 451, 383], "label": "drone", "frame_id": "29", "frame_idx": 29, "interpolated": true, "extrapolated": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "29", "frame_idx": 29, "interpolated": true, "extrapolated": true}], "31": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "31", "frame_idx": 31, "interpolated": true, "extrapolated": true}, {"bbox": [417, 63, 422, 68], "label": "drone", "frame_id": "31", "frame_idx": 31, "interpolated": true, "trajectory_based": true}, {"bbox": [447, 63, 452, 68], "label": "drone", "frame_id": "31", "frame_idx": 31, "interpolated": true, "trajectory_based": true}, {"bbox": [446, 378, 451, 383], "label": "drone", "frame_id": "31", "frame_idx": 31, "interpolated": true, "extrapolated": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "31", "frame_idx": 31, "interpolated": true, "extrapolated": true}], "32": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "32", "frame_idx": 32, "interpolated": true, "extrapolated": true}, {"bbox": [417, 63, 422, 68], "label": "drone", "frame_id": "32", "frame_idx": 32, "interpolated": true, "trajectory_based": true}, {"bbox": [447, 63, 452, 68], "label": "drone", "frame_id": "32", "frame_idx": 32, "interpolated": true, "trajectory_based": true}, {"bbox": [446, 378, 451, 383], "label": "drone", "frame_id": "32", "frame_idx": 32, "interpolated": true, "extrapolated": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "32", "frame_idx": 32, "interpolated": true, "extrapolated": true}], "33": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "33", "frame_idx": 33, "interpolated": true, "extrapolated": true}, {"bbox": [417, 63, 422, 68], "label": "drone", "frame_id": "33", "frame_idx": 33, "interpolated": true, "trajectory_based": true}, {"bbox": [447, 63, 452, 68], "label": "drone", "frame_id": "33", "frame_idx": 33, "interpolated": true, "trajectory_based": true}, {"bbox": [446, 378, 451, 383], "label": "drone", "frame_id": "33", "frame_idx": 33, "interpolated": true, "extrapolated": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "33", "frame_idx": 33, "interpolated": true, "extrapolated": true}], "34": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "34", "frame_idx": 34, "interpolated": true, "extrapolated": true}, {"bbox": [417, 63, 422, 68], "label": "drone", "frame_id": "34", "frame_idx": 34, "interpolated": true, "trajectory_based": true}, {"bbox": [447, 63, 452, 68], "label": "drone", "frame_id": "34", "frame_idx": 34, "interpolated": true, "trajectory_based": true}, {"bbox": [446, 378, 451, 383], "label": "drone", "frame_id": "34", "frame_idx": 34, "interpolated": true, "extrapolated": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "34", "frame_idx": 34, "interpolated": true, "extrapolated": true}], "35": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "35", "frame_idx": 35, "interpolated": true, "extrapolated": true}, {"bbox": [417, 64, 423, 69], "label": "drone", "frame_id": "35", "frame_idx": 35, "interpolated": true, "trajectory_based": true}, {"bbox": [447, 63, 452, 68], "label": "drone", "frame_id": "35", "frame_idx": 35, "interpolated": true, "trajectory_based": true}, {"bbox": [446, 378, 451, 383], "label": "drone", "frame_id": "35", "frame_idx": 35, "interpolated": true, "extrapolated": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "35", "frame_idx": 35, "interpolated": true, "extrapolated": true}], "36": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "36", "frame_idx": 36, "interpolated": true, "extrapolated": true}, {"bbox": [417, 64, 423, 69], "label": "drone", "frame_id": "36", "frame_idx": 36, "interpolated": true, "trajectory_based": true}, {"bbox": [447, 63, 452, 68], "label": "drone", "frame_id": "36", "frame_idx": 36, "interpolated": true, "trajectory_based": true}, {"bbox": [446, 378, 451, 383], "label": "drone", "frame_id": "36", "frame_idx": 36, "interpolated": true, "extrapolated": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "36", "frame_idx": 36, "interpolated": true, "extrapolated": true}], "37": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "37", "frame_idx": 37, "interpolated": true, "extrapolated": true}, {"bbox": [417, 64, 423, 69], "label": "drone", "frame_id": "37", "frame_idx": 37, "interpolated": true, "trajectory_based": true}, {"bbox": [447, 63, 452, 68], "label": "drone", "frame_id": "37", "frame_idx": 37, "interpolated": true, "trajectory_based": true}, {"bbox": [446, 378, 451, 383], "label": "drone", "frame_id": "37", "frame_idx": 37, "interpolated": true, "extrapolated": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "37", "frame_idx": 37, "interpolated": true, "extrapolated": true}], "38": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "38", "frame_idx": 38, "interpolated": true, "extrapolated": true}, {"bbox": [417, 64, 423, 69], "label": "drone", "frame_id": "38", "frame_idx": 38, "interpolated": true, "trajectory_based": true}, {"bbox": [447, 63, 452, 68], "label": "drone", "frame_id": "38", "frame_idx": 38, "interpolated": true, "trajectory_based": true}, {"bbox": [446, 378, 451, 383], "label": "drone", "frame_id": "38", "frame_idx": 38, "interpolated": true, "extrapolated": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "38", "frame_idx": 38, "interpolated": true, "extrapolated": true}], "39": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "39", "frame_idx": 39, "interpolated": true, "extrapolated": true}, {"bbox": [417, 64, 423, 69], "label": "drone", "frame_id": "39", "frame_idx": 39, "interpolated": true, "trajectory_based": true}, {"bbox": [447, 63, 452, 68], "label": "drone", "frame_id": "39", "frame_idx": 39, "interpolated": true, "trajectory_based": true}, {"bbox": [446, 378, 451, 383], "label": "drone", "frame_id": "39", "frame_idx": 39, "interpolated": true, "extrapolated": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "39", "frame_idx": 39, "interpolated": true, "extrapolated": true}], "41": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "41", "frame_idx": 41, "interpolated": true, "extrapolated": true}, {"bbox": [418, 65, 424, 70], "label": "小目标", "frame_id": "41", "frame_idx": 41, "interpolated": true, "extrapolated": true}, {"bbox": [447, 62, 452, 67], "label": "drone", "frame_id": "41", "frame_idx": 41, "interpolated": true, "trajectory_based": true}, {"bbox": [446, 378, 451, 383], "label": "drone", "frame_id": "41", "frame_idx": 41, "interpolated": true, "trajectory_based": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "41", "frame_idx": 41, "interpolated": true, "extrapolated": true}], "42": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "42", "frame_idx": 42, "interpolated": true, "extrapolated": true}, {"bbox": [418, 65, 424, 70], "label": "小目标", "frame_id": "42", "frame_idx": 42, "interpolated": true, "extrapolated": true}, {"bbox": [447, 62, 452, 67], "label": "drone", "frame_id": "42", "frame_idx": 42, "interpolated": true, "trajectory_based": true}, {"bbox": [446, 378, 451, 383], "label": "drone", "frame_id": "42", "frame_idx": 42, "interpolated": true, "trajectory_based": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "42", "frame_idx": 42, "interpolated": true, "extrapolated": true}], "43": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "43", "frame_idx": 43, "interpolated": true, "extrapolated": true}, {"bbox": [418, 65, 424, 70], "label": "小目标", "frame_id": "43", "frame_idx": 43, "interpolated": true, "extrapolated": true}, {"bbox": [447, 62, 452, 67], "label": "drone", "frame_id": "43", "frame_idx": 43, "interpolated": true, "trajectory_based": true}, {"bbox": [446, 378, 451, 383], "label": "drone", "frame_id": "43", "frame_idx": 43, "interpolated": true, "trajectory_based": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "43", "frame_idx": 43, "interpolated": true, "extrapolated": true}], "44": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "44", "frame_idx": 44, "interpolated": true, "extrapolated": true}, {"bbox": [418, 65, 424, 70], "label": "小目标", "frame_id": "44", "frame_idx": 44, "interpolated": true, "extrapolated": true}, {"bbox": [447, 61, 452, 66], "label": "drone", "frame_id": "44", "frame_idx": 44, "interpolated": true, "trajectory_based": true}, {"bbox": [446, 378, 451, 383], "label": "drone", "frame_id": "44", "frame_idx": 44, "interpolated": true, "trajectory_based": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "44", "frame_idx": 44, "interpolated": true, "extrapolated": true}], "45": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "45", "frame_idx": 45, "interpolated": true, "extrapolated": true}, {"bbox": [418, 65, 424, 70], "label": "小目标", "frame_id": "45", "frame_idx": 45, "interpolated": true, "extrapolated": true}, {"bbox": [447, 61, 452, 66], "label": "drone", "frame_id": "45", "frame_idx": 45, "interpolated": true, "trajectory_based": true}, {"bbox": [446, 378, 451, 383], "label": "drone", "frame_id": "45", "frame_idx": 45, "interpolated": true, "trajectory_based": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "45", "frame_idx": 45, "interpolated": true, "extrapolated": true}], "46": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "46", "frame_idx": 46, "interpolated": true, "extrapolated": true}, {"bbox": [418, 65, 424, 70], "label": "小目标", "frame_id": "46", "frame_idx": 46, "interpolated": true, "extrapolated": true}, {"bbox": [447, 61, 452, 66], "label": "drone", "frame_id": "46", "frame_idx": 46, "interpolated": true, "trajectory_based": true}, {"bbox": [446, 378, 451, 383], "label": "drone", "frame_id": "46", "frame_idx": 46, "interpolated": true, "trajectory_based": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "46", "frame_idx": 46, "interpolated": true, "extrapolated": true}], "47": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "47", "frame_idx": 47, "interpolated": true, "extrapolated": true}, {"bbox": [418, 65, 424, 70], "label": "小目标", "frame_id": "47", "frame_idx": 47, "interpolated": true, "extrapolated": true}, {"bbox": [447, 60, 452, 65], "label": "drone", "frame_id": "47", "frame_idx": 47, "interpolated": true, "trajectory_based": true}, {"bbox": [446, 378, 451, 383], "label": "drone", "frame_id": "47", "frame_idx": 47, "interpolated": true, "trajectory_based": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "47", "frame_idx": 47, "interpolated": true, "extrapolated": true}], "48": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "48", "frame_idx": 48, "interpolated": true, "extrapolated": true}, {"bbox": [418, 65, 424, 70], "label": "小目标", "frame_id": "48", "frame_idx": 48, "interpolated": true, "extrapolated": true}, {"bbox": [447, 60, 452, 65], "label": "drone", "frame_id": "48", "frame_idx": 48, "interpolated": true, "trajectory_based": true}, {"bbox": [446, 378, 451, 383], "label": "drone", "frame_id": "48", "frame_idx": 48, "interpolated": true, "trajectory_based": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "48", "frame_idx": 48, "interpolated": true, "extrapolated": true}], "49": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "49", "frame_idx": 49, "interpolated": true, "extrapolated": true}, {"bbox": [418, 65, 424, 70], "label": "小目标", "frame_id": "49", "frame_idx": 49, "interpolated": true, "extrapolated": true}, {"bbox": [447, 60, 452, 65], "label": "drone", "frame_id": "49", "frame_idx": 49, "interpolated": true, "trajectory_based": true}, {"bbox": [446, 378, 451, 383], "label": "drone", "frame_id": "49", "frame_idx": 49, "interpolated": true, "trajectory_based": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "49", "frame_idx": 49, "interpolated": true, "extrapolated": true}], "51": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "51", "frame_idx": 51, "interpolated": true, "extrapolated": true}, {"bbox": [418, 65, 424, 70], "label": "小目标", "frame_id": "51", "frame_idx": 51, "interpolated": true, "extrapolated": true}, {"bbox": [447, 60, 452, 65], "label": "drone", "frame_id": "51", "frame_idx": 51, "interpolated": true, "extrapolated": true}, {"bbox": [446, 379, 451, 384], "label": "drone", "frame_id": "51", "frame_idx": 51, "interpolated": true, "extrapolated": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "51", "frame_idx": 51, "interpolated": true, "extrapolated": true}], "52": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "52", "frame_idx": 52, "interpolated": true, "extrapolated": true}, {"bbox": [418, 65, 424, 70], "label": "小目标", "frame_id": "52", "frame_idx": 52, "interpolated": true, "extrapolated": true}, {"bbox": [447, 60, 452, 65], "label": "drone", "frame_id": "52", "frame_idx": 52, "interpolated": true, "extrapolated": true}, {"bbox": [446, 379, 451, 384], "label": "drone", "frame_id": "52", "frame_idx": 52, "interpolated": true, "extrapolated": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "52", "frame_idx": 52, "interpolated": true, "extrapolated": true}], "53": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "53", "frame_idx": 53, "interpolated": true, "extrapolated": true}, {"bbox": [418, 65, 424, 70], "label": "小目标", "frame_id": "53", "frame_idx": 53, "interpolated": true, "extrapolated": true}, {"bbox": [447, 60, 452, 65], "label": "drone", "frame_id": "53", "frame_idx": 53, "interpolated": true, "extrapolated": true}, {"bbox": [446, 379, 451, 384], "label": "drone", "frame_id": "53", "frame_idx": 53, "interpolated": true, "extrapolated": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "53", "frame_idx": 53, "interpolated": true, "extrapolated": true}], "54": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "54", "frame_idx": 54, "interpolated": true, "extrapolated": true}, {"bbox": [418, 65, 424, 70], "label": "小目标", "frame_id": "54", "frame_idx": 54, "interpolated": true, "extrapolated": true}, {"bbox": [447, 60, 452, 65], "label": "drone", "frame_id": "54", "frame_idx": 54, "interpolated": true, "extrapolated": true}, {"bbox": [446, 379, 451, 384], "label": "drone", "frame_id": "54", "frame_idx": 54, "interpolated": true, "extrapolated": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "54", "frame_idx": 54, "interpolated": true, "extrapolated": true}], "55": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "55", "frame_idx": 55, "interpolated": true, "extrapolated": true}, {"bbox": [418, 65, 424, 70], "label": "小目标", "frame_id": "55", "frame_idx": 55, "interpolated": true, "extrapolated": true}, {"bbox": [447, 60, 452, 65], "label": "drone", "frame_id": "55", "frame_idx": 55, "interpolated": true, "extrapolated": true}, {"bbox": [446, 379, 451, 384], "label": "drone", "frame_id": "55", "frame_idx": 55, "interpolated": true, "extrapolated": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "55", "frame_idx": 55, "interpolated": true, "extrapolated": true}], "56": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "56", "frame_idx": 56, "interpolated": true, "extrapolated": true}, {"bbox": [418, 65, 424, 70], "label": "小目标", "frame_id": "56", "frame_idx": 56, "interpolated": true, "extrapolated": true}, {"bbox": [447, 60, 452, 65], "label": "drone", "frame_id": "56", "frame_idx": 56, "interpolated": true, "extrapolated": true}, {"bbox": [446, 379, 451, 384], "label": "drone", "frame_id": "56", "frame_idx": 56, "interpolated": true, "extrapolated": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "56", "frame_idx": 56, "interpolated": true, "extrapolated": true}], "57": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "57", "frame_idx": 57, "interpolated": true, "extrapolated": true}, {"bbox": [418, 65, 424, 70], "label": "小目标", "frame_id": "57", "frame_idx": 57, "interpolated": true, "extrapolated": true}, {"bbox": [447, 60, 452, 65], "label": "drone", "frame_id": "57", "frame_idx": 57, "interpolated": true, "extrapolated": true}, {"bbox": [446, 379, 451, 384], "label": "drone", "frame_id": "57", "frame_idx": 57, "interpolated": true, "extrapolated": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "57", "frame_idx": 57, "interpolated": true, "extrapolated": true}], "58": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "58", "frame_idx": 58, "interpolated": true, "extrapolated": true}, {"bbox": [418, 65, 424, 70], "label": "小目标", "frame_id": "58", "frame_idx": 58, "interpolated": true, "extrapolated": true}, {"bbox": [447, 60, 452, 65], "label": "drone", "frame_id": "58", "frame_idx": 58, "interpolated": true, "extrapolated": true}, {"bbox": [446, 379, 451, 384], "label": "drone", "frame_id": "58", "frame_idx": 58, "interpolated": true, "extrapolated": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "58", "frame_idx": 58, "interpolated": true, "extrapolated": true}], "59": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "59", "frame_idx": 59, "interpolated": true, "extrapolated": true}, {"bbox": [418, 65, 424, 70], "label": "小目标", "frame_id": "59", "frame_idx": 59, "interpolated": true, "extrapolated": true}, {"bbox": [447, 60, 452, 65], "label": "drone", "frame_id": "59", "frame_idx": 59, "interpolated": true, "extrapolated": true}, {"bbox": [446, 379, 451, 384], "label": "drone", "frame_id": "59", "frame_idx": 59, "interpolated": true, "extrapolated": true}, {"bbox": [416, 64, 421, 69], "label": "小目标", "frame_id": "59", "frame_idx": 59, "interpolated": true, "extrapolated": true}], "61": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "61", "frame_idx": 61, "interpolated": true, "extrapolated": true}, {"bbox": [418, 65, 424, 70], "label": "小目标", "frame_id": "61", "frame_idx": 61, "interpolated": true, "extrapolated": true}, {"bbox": [447, 60, 452, 65], "label": "drone", "frame_id": "61", "frame_idx": 61, "interpolated": true, "extrapolated": true}, {"bbox": [446, 379, 451, 384], "label": "drone", "frame_id": "61", "frame_idx": 61, "interpolated": true, "extrapolated": true}, {"bbox": [415, 63, 420, 69], "label": "小目标", "frame_id": "61", "frame_idx": 61, "interpolated": true, "trajectory_based": true}], "62": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "62", "frame_idx": 62, "interpolated": true, "extrapolated": true}, {"bbox": [418, 65, 424, 70], "label": "小目标", "frame_id": "62", "frame_idx": 62, "interpolated": true, "extrapolated": true}, {"bbox": [447, 60, 452, 65], "label": "drone", "frame_id": "62", "frame_idx": 62, "interpolated": true, "extrapolated": true}, {"bbox": [446, 379, 451, 384], "label": "drone", "frame_id": "62", "frame_idx": 62, "interpolated": true, "extrapolated": true}, {"bbox": [415, 63, 420, 69], "label": "小目标", "frame_id": "62", "frame_idx": 62, "interpolated": true, "trajectory_based": true}], "63": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "63", "frame_idx": 63, "interpolated": true, "extrapolated": true}, {"bbox": [418, 65, 424, 70], "label": "小目标", "frame_id": "63", "frame_idx": 63, "interpolated": true, "extrapolated": true}, {"bbox": [447, 60, 452, 65], "label": "drone", "frame_id": "63", "frame_idx": 63, "interpolated": true, "extrapolated": true}, {"bbox": [446, 379, 451, 384], "label": "drone", "frame_id": "63", "frame_idx": 63, "interpolated": true, "extrapolated": true}, {"bbox": [414, 63, 420, 69], "label": "小目标", "frame_id": "63", "frame_idx": 63, "interpolated": true, "trajectory_based": true}], "64": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "64", "frame_idx": 64, "interpolated": true, "extrapolated": true}, {"bbox": [418, 65, 424, 70], "label": "小目标", "frame_id": "64", "frame_idx": 64, "interpolated": true, "extrapolated": true}, {"bbox": [447, 60, 452, 65], "label": "drone", "frame_id": "64", "frame_idx": 64, "interpolated": true, "extrapolated": true}, {"bbox": [446, 379, 451, 384], "label": "drone", "frame_id": "64", "frame_idx": 64, "interpolated": true, "extrapolated": true}, {"bbox": [414, 63, 419, 69], "label": "小目标", "frame_id": "64", "frame_idx": 64, "interpolated": true, "trajectory_based": true}], "65": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "65", "frame_idx": 65, "interpolated": true, "extrapolated": true}, {"bbox": [418, 65, 424, 70], "label": "小目标", "frame_id": "65", "frame_idx": 65, "interpolated": true, "extrapolated": true}, {"bbox": [447, 60, 452, 65], "label": "drone", "frame_id": "65", "frame_idx": 65, "interpolated": true, "extrapolated": true}, {"bbox": [446, 379, 451, 384], "label": "drone", "frame_id": "65", "frame_idx": 65, "interpolated": true, "extrapolated": true}, {"bbox": [414, 63, 419, 69], "label": "小目标", "frame_id": "65", "frame_idx": 65, "interpolated": true, "trajectory_based": true}], "66": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "66", "frame_idx": 66, "interpolated": true, "extrapolated": true}, {"bbox": [418, 65, 424, 70], "label": "小目标", "frame_id": "66", "frame_idx": 66, "interpolated": true, "extrapolated": true}, {"bbox": [447, 60, 452, 65], "label": "drone", "frame_id": "66", "frame_idx": 66, "interpolated": true, "extrapolated": true}, {"bbox": [446, 379, 451, 384], "label": "drone", "frame_id": "66", "frame_idx": 66, "interpolated": true, "extrapolated": true}, {"bbox": [413, 62, 419, 69], "label": "小目标", "frame_id": "66", "frame_idx": 66, "interpolated": true, "trajectory_based": true}], "67": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "67", "frame_idx": 67, "interpolated": true, "extrapolated": true}, {"bbox": [418, 65, 424, 70], "label": "小目标", "frame_id": "67", "frame_idx": 67, "interpolated": true, "extrapolated": true}, {"bbox": [447, 60, 452, 65], "label": "drone", "frame_id": "67", "frame_idx": 67, "interpolated": true, "extrapolated": true}, {"bbox": [446, 379, 451, 384], "label": "drone", "frame_id": "67", "frame_idx": 67, "interpolated": true, "extrapolated": true}, {"bbox": [413, 62, 418, 69], "label": "小目标", "frame_id": "67", "frame_idx": 67, "interpolated": true, "trajectory_based": true}], "68": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "68", "frame_idx": 68, "interpolated": true, "extrapolated": true}, {"bbox": [418, 65, 424, 70], "label": "小目标", "frame_id": "68", "frame_idx": 68, "interpolated": true, "extrapolated": true}, {"bbox": [447, 60, 452, 65], "label": "drone", "frame_id": "68", "frame_idx": 68, "interpolated": true, "extrapolated": true}, {"bbox": [446, 379, 451, 384], "label": "drone", "frame_id": "68", "frame_idx": 68, "interpolated": true, "extrapolated": true}, {"bbox": [412, 62, 418, 69], "label": "小目标", "frame_id": "68", "frame_idx": 68, "interpolated": true, "trajectory_based": true}], "69": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "69", "frame_idx": 69, "interpolated": true, "extrapolated": true}, {"bbox": [418, 65, 424, 70], "label": "小目标", "frame_id": "69", "frame_idx": 69, "interpolated": true, "extrapolated": true}, {"bbox": [447, 60, 452, 65], "label": "drone", "frame_id": "69", "frame_idx": 69, "interpolated": true, "extrapolated": true}, {"bbox": [446, 379, 451, 384], "label": "drone", "frame_id": "69", "frame_idx": 69, "interpolated": true, "extrapolated": true}, {"bbox": [412, 62, 418, 69], "label": "小目标", "frame_id": "69", "frame_idx": 69, "interpolated": true, "trajectory_based": true}], "71": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "71", "frame_idx": 71, "interpolated": true, "extrapolated": true}, {"bbox": [418, 65, 424, 70], "label": "小目标", "frame_id": "71", "frame_idx": 71, "interpolated": true, "extrapolated": true}, {"bbox": [447, 60, 452, 65], "label": "drone", "frame_id": "71", "frame_idx": 71, "interpolated": true, "extrapolated": true}, {"bbox": [446, 379, 451, 384], "label": "drone", "frame_id": "71", "frame_idx": 71, "interpolated": true, "extrapolated": true}, {"bbox": [412, 62, 418, 69], "label": "drone", "frame_id": "71", "frame_idx": 71, "interpolated": true, "extrapolated": true}], "72": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "72", "frame_idx": 72, "interpolated": true, "extrapolated": true}, {"bbox": [418, 65, 424, 70], "label": "小目标", "frame_id": "72", "frame_idx": 72, "interpolated": true, "extrapolated": true}, {"bbox": [447, 60, 452, 65], "label": "drone", "frame_id": "72", "frame_idx": 72, "interpolated": true, "extrapolated": true}, {"bbox": [446, 379, 451, 384], "label": "drone", "frame_id": "72", "frame_idx": 72, "interpolated": true, "extrapolated": true}, {"bbox": [412, 62, 418, 69], "label": "drone", "frame_id": "72", "frame_idx": 72, "interpolated": true, "extrapolated": true}], "73": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "73", "frame_idx": 73, "interpolated": true, "extrapolated": true}, {"bbox": [418, 65, 424, 70], "label": "小目标", "frame_id": "73", "frame_idx": 73, "interpolated": true, "extrapolated": true}, {"bbox": [447, 60, 452, 65], "label": "drone", "frame_id": "73", "frame_idx": 73, "interpolated": true, "extrapolated": true}, {"bbox": [446, 379, 451, 384], "label": "drone", "frame_id": "73", "frame_idx": 73, "interpolated": true, "extrapolated": true}, {"bbox": [412, 62, 418, 69], "label": "drone", "frame_id": "73", "frame_idx": 73, "interpolated": true, "extrapolated": true}], "74": [{"bbox": [447, 38, 452, 43], "label": "drone", "frame_id": "74", "frame_idx": 74, "interpolated": true, "extrapolated": true}, {"bbox": [418, 65, 424, 70], "label": "小目标", "frame_id": "74", "frame_idx": 74, "interpolated": true, "extrapolated": true}, {"bbox": [447, 60, 452, 65], "label": "drone", "frame_id": "74", "frame_idx": 74, "interpolated": true, "extrapolated": true}, {"bbox": [446, 379, 451, 384], "label": "drone", "frame_id": "74", "frame_idx": 74, "interpolated": true, "extrapolated": true}, {"bbox": [412, 62, 418, 69], "label": "drone", "frame_id": "74", "frame_idx": 74, "interpolated": true, "extrapolated": true}]}}}