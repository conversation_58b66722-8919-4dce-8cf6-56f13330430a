{"data01": {"sequence_id": "data01", "total_frames": 75, "key_frames": 8, "total_detections": 106, "direct_detections": 8, "interpolated_detections": 98, "metrics": {"precision": 0.2641509433962264, "recall": 0.37333333333333335, "f1": 0.30939226519337015, "tp": 28, "fp": 78, "fn": 47}, "detections": {"0": [{"bbox": [68, 106, 73, 111], "label": "drone", "frame_id": "0", "size": 25, "frame_idx": 0}], "10": [{"bbox": [62, 106, 67, 111], "label": "drone", "frame_id": "10", "size": 25, "frame_idx": 10}], "20": [{"bbox": [67, 106, 72, 111], "label": "drone", "frame_id": "20", "size": 25, "frame_idx": 20}], "30": [{"bbox": [68, 106, 73, 111], "label": "drone", "frame_id": "30", "size": 25, "frame_idx": 30}], "40": [{"bbox": [67, 106, 72, 111], "label": "drone", "frame_id": "40", "size": 25, "frame_idx": 40}], "50": [{"bbox": [59, 106, 64, 111], "label": "drone", "frame_id": "50", "size": 25, "frame_idx": 50}], "60": [{"bbox": [59, 106, 64, 111], "label": "drone", "frame_id": "60", "size": 25, "frame_idx": 60}], "70": [{"bbox": [62, 108, 67, 113], "label": "drone", "frame_id": "70", "size": 25, "frame_idx": 70}], "1": [{"bbox": [67, 106, 72, 111], "label": "drone", "frame_id": "1", "frame_idx": 1, "interpolated": true, "temporal_based": true}], "2": [{"bbox": [66, 106, 71, 111], "label": "drone", "frame_id": "2", "frame_idx": 2, "interpolated": true, "temporal_based": true}], "3": [{"bbox": [66, 106, 71, 111], "label": "drone", "frame_id": "3", "frame_idx": 3, "interpolated": true, "temporal_based": true}], "4": [{"bbox": [65, 106, 70, 111], "label": "drone", "frame_id": "4", "frame_idx": 4, "interpolated": true, "temporal_based": true}], "5": [{"bbox": [65, 106, 70, 111], "label": "drone", "frame_id": "5", "frame_idx": 5, "interpolated": true, "temporal_based": true}], "6": [{"bbox": [64, 106, 69, 111], "label": "drone", "frame_id": "6", "frame_idx": 6, "interpolated": true, "temporal_based": true}], "7": [{"bbox": [63, 106, 68, 111], "label": "drone", "frame_id": "7", "frame_idx": 7, "interpolated": true, "temporal_based": true}], "8": [{"bbox": [63, 106, 68, 111], "label": "drone", "frame_id": "8", "frame_idx": 8, "interpolated": true, "temporal_based": true}], "9": [{"bbox": [62, 106, 67, 111], "label": "drone", "frame_id": "9", "frame_idx": 9, "interpolated": true, "temporal_based": true}], "11": [{"bbox": [62, 106, 67, 111], "label": "drone", "frame_id": "11", "frame_idx": 11, "interpolated": true, "temporal_based": true}], "12": [{"bbox": [63, 106, 68, 111], "label": "drone", "frame_id": "12", "frame_idx": 12, "interpolated": true, "temporal_based": true}], "13": [{"bbox": [63, 106, 68, 111], "label": "drone", "frame_id": "13", "frame_idx": 13, "interpolated": true, "temporal_based": true}], "14": [{"bbox": [64, 106, 69, 111], "label": "drone", "frame_id": "14", "frame_idx": 14, "interpolated": true, "temporal_based": true}], "15": [{"bbox": [64, 106, 69, 111], "label": "drone", "frame_id": "15", "frame_idx": 15, "interpolated": true, "temporal_based": true}], "16": [{"bbox": [65, 106, 70, 111], "label": "drone", "frame_id": "16", "frame_idx": 16, "interpolated": true, "temporal_based": true}], "17": [{"bbox": [65, 106, 70, 111], "label": "drone", "frame_id": "17", "frame_idx": 17, "interpolated": true, "temporal_based": true}], "18": [{"bbox": [66, 106, 71, 111], "label": "drone", "frame_id": "18", "frame_idx": 18, "interpolated": true, "temporal_based": true}], "19": [{"bbox": [66, 106, 71, 111], "label": "drone", "frame_id": "19", "frame_idx": 19, "interpolated": true, "temporal_based": true}], "21": [{"bbox": [67, 106, 72, 111], "label": "drone", "frame_id": "21", "frame_idx": 21, "interpolated": true, "temporal_based": true}], "22": [{"bbox": [67, 106, 72, 111], "label": "drone", "frame_id": "22", "frame_idx": 22, "interpolated": true, "temporal_based": true}], "23": [{"bbox": [67, 106, 72, 111], "label": "drone", "frame_id": "23", "frame_idx": 23, "interpolated": true, "temporal_based": true}], "24": [{"bbox": [67, 106, 72, 111], "label": "drone", "frame_id": "24", "frame_idx": 24, "interpolated": true, "temporal_based": true}], "25": [{"bbox": [67, 106, 72, 111], "label": "drone", "frame_id": "25", "frame_idx": 25, "interpolated": true, "temporal_based": true}], "26": [{"bbox": [67, 106, 72, 111], "label": "drone", "frame_id": "26", "frame_idx": 26, "interpolated": true, "temporal_based": true}], "27": [{"bbox": [67, 106, 72, 111], "label": "drone", "frame_id": "27", "frame_idx": 27, "interpolated": true, "temporal_based": true}], "28": [{"bbox": [67, 106, 72, 111], "label": "drone", "frame_id": "28", "frame_idx": 28, "interpolated": true, "temporal_based": true}], "29": [{"bbox": [67, 106, 72, 111], "label": "drone", "frame_id": "29", "frame_idx": 29, "interpolated": true, "temporal_based": true}], "31": [{"bbox": [68, 106, 73, 111], "label": "drone", "frame_id": "31", "frame_idx": 31, "interpolated": true, "extrapolated": true}], "32": [{"bbox": [68, 106, 73, 111], "label": "drone", "frame_id": "32", "frame_idx": 32, "interpolated": true, "extrapolated": true}], "33": [{"bbox": [68, 106, 73, 111], "label": "drone", "frame_id": "33", "frame_idx": 33, "interpolated": true, "extrapolated": true}], "34": [{"bbox": [68, 106, 73, 111], "label": "drone", "frame_id": "34", "frame_idx": 34, "interpolated": true, "extrapolated": true}], "35": [{"bbox": [68, 106, 73, 111], "label": "drone", "frame_id": "35", "frame_idx": 35, "interpolated": true, "extrapolated": true}], "36": [{"bbox": [68, 106, 73, 111], "label": "drone", "frame_id": "36", "frame_idx": 36, "interpolated": true, "extrapolated": true}], "37": [{"bbox": [68, 106, 73, 111], "label": "drone", "frame_id": "37", "frame_idx": 37, "interpolated": true, "extrapolated": true}], "38": [{"bbox": [68, 106, 73, 111], "label": "drone", "frame_id": "38", "frame_idx": 38, "interpolated": true, "extrapolated": true}], "39": [{"bbox": [68, 106, 73, 111], "label": "drone", "frame_id": "39", "frame_idx": 39, "interpolated": true, "extrapolated": true}], "41": [{"bbox": [68, 106, 73, 111], "label": "drone", "frame_id": "41", "frame_idx": 41, "interpolated": true, "extrapolated": true}, {"bbox": [66, 106, 71, 111], "label": "drone", "frame_id": "41", "frame_idx": 41, "interpolated": true, "temporal_based": true}], "42": [{"bbox": [68, 106, 73, 111], "label": "drone", "frame_id": "42", "frame_idx": 42, "interpolated": true, "extrapolated": true}, {"bbox": [65, 106, 70, 111], "label": "drone", "frame_id": "42", "frame_idx": 42, "interpolated": true, "temporal_based": true}], "43": [{"bbox": [68, 106, 73, 111], "label": "drone", "frame_id": "43", "frame_idx": 43, "interpolated": true, "extrapolated": true}, {"bbox": [64, 106, 69, 111], "label": "drone", "frame_id": "43", "frame_idx": 43, "interpolated": true, "temporal_based": true}], "44": [{"bbox": [68, 106, 73, 111], "label": "drone", "frame_id": "44", "frame_idx": 44, "interpolated": true, "extrapolated": true}, {"bbox": [63, 106, 68, 111], "label": "drone", "frame_id": "44", "frame_idx": 44, "interpolated": true, "temporal_based": true}], "45": [{"bbox": [68, 106, 73, 111], "label": "drone", "frame_id": "45", "frame_idx": 45, "interpolated": true, "extrapolated": true}, {"bbox": [63, 106, 68, 111], "label": "drone", "frame_id": "45", "frame_idx": 45, "interpolated": true, "temporal_based": true}], "46": [{"bbox": [68, 106, 73, 111], "label": "drone", "frame_id": "46", "frame_idx": 46, "interpolated": true, "extrapolated": true}, {"bbox": [62, 106, 67, 111], "label": "drone", "frame_id": "46", "frame_idx": 46, "interpolated": true, "temporal_based": true}], "47": [{"bbox": [68, 106, 73, 111], "label": "drone", "frame_id": "47", "frame_idx": 47, "interpolated": true, "extrapolated": true}, {"bbox": [61, 106, 66, 111], "label": "drone", "frame_id": "47", "frame_idx": 47, "interpolated": true, "temporal_based": true}], "48": [{"bbox": [68, 106, 73, 111], "label": "drone", "frame_id": "48", "frame_idx": 48, "interpolated": true, "extrapolated": true}, {"bbox": [60, 106, 65, 111], "label": "drone", "frame_id": "48", "frame_idx": 48, "interpolated": true, "temporal_based": true}], "49": [{"bbox": [68, 106, 73, 111], "label": "drone", "frame_id": "49", "frame_idx": 49, "interpolated": true, "extrapolated": true}, {"bbox": [59, 106, 64, 111], "label": "drone", "frame_id": "49", "frame_idx": 49, "interpolated": true, "temporal_based": true}], "51": [{"bbox": [68, 106, 73, 111], "label": "drone", "frame_id": "51", "frame_idx": 51, "interpolated": true, "extrapolated": true}, {"bbox": [59, 106, 64, 111], "label": "drone", "frame_id": "51", "frame_idx": 51, "interpolated": true, "temporal_based": true}], "52": [{"bbox": [68, 106, 73, 111], "label": "drone", "frame_id": "52", "frame_idx": 52, "interpolated": true, "extrapolated": true}, {"bbox": [59, 106, 64, 111], "label": "drone", "frame_id": "52", "frame_idx": 52, "interpolated": true, "temporal_based": true}], "53": [{"bbox": [68, 106, 73, 111], "label": "drone", "frame_id": "53", "frame_idx": 53, "interpolated": true, "extrapolated": true}, {"bbox": [59, 106, 64, 111], "label": "drone", "frame_id": "53", "frame_idx": 53, "interpolated": true, "temporal_based": true}], "54": [{"bbox": [68, 106, 73, 111], "label": "drone", "frame_id": "54", "frame_idx": 54, "interpolated": true, "extrapolated": true}, {"bbox": [59, 106, 64, 111], "label": "drone", "frame_id": "54", "frame_idx": 54, "interpolated": true, "temporal_based": true}], "55": [{"bbox": [68, 106, 73, 111], "label": "drone", "frame_id": "55", "frame_idx": 55, "interpolated": true, "extrapolated": true}, {"bbox": [59, 106, 64, 111], "label": "drone", "frame_id": "55", "frame_idx": 55, "interpolated": true, "temporal_based": true}], "56": [{"bbox": [68, 106, 73, 111], "label": "drone", "frame_id": "56", "frame_idx": 56, "interpolated": true, "extrapolated": true}, {"bbox": [59, 106, 64, 111], "label": "drone", "frame_id": "56", "frame_idx": 56, "interpolated": true, "temporal_based": true}], "57": [{"bbox": [68, 106, 73, 111], "label": "drone", "frame_id": "57", "frame_idx": 57, "interpolated": true, "extrapolated": true}, {"bbox": [59, 106, 64, 111], "label": "drone", "frame_id": "57", "frame_idx": 57, "interpolated": true, "temporal_based": true}], "58": [{"bbox": [68, 106, 73, 111], "label": "drone", "frame_id": "58", "frame_idx": 58, "interpolated": true, "extrapolated": true}, {"bbox": [59, 106, 64, 111], "label": "drone", "frame_id": "58", "frame_idx": 58, "interpolated": true, "temporal_based": true}], "59": [{"bbox": [68, 106, 73, 111], "label": "drone", "frame_id": "59", "frame_idx": 59, "interpolated": true, "extrapolated": true}, {"bbox": [59, 106, 64, 111], "label": "drone", "frame_id": "59", "frame_idx": 59, "interpolated": true, "temporal_based": true}], "61": [{"bbox": [68, 106, 73, 111], "label": "drone", "frame_id": "61", "frame_idx": 61, "interpolated": true, "extrapolated": true}, {"bbox": [59, 106, 64, 111], "label": "drone", "frame_id": "61", "frame_idx": 61, "interpolated": true, "temporal_based": true}], "62": [{"bbox": [68, 106, 73, 111], "label": "drone", "frame_id": "62", "frame_idx": 62, "interpolated": true, "extrapolated": true}, {"bbox": [59, 106, 64, 111], "label": "drone", "frame_id": "62", "frame_idx": 62, "interpolated": true, "temporal_based": true}], "63": [{"bbox": [68, 106, 73, 111], "label": "drone", "frame_id": "63", "frame_idx": 63, "interpolated": true, "extrapolated": true}, {"bbox": [59, 106, 64, 111], "label": "drone", "frame_id": "63", "frame_idx": 63, "interpolated": true, "temporal_based": true}], "64": [{"bbox": [68, 106, 73, 111], "label": "drone", "frame_id": "64", "frame_idx": 64, "interpolated": true, "extrapolated": true}, {"bbox": [60, 106, 65, 111], "label": "drone", "frame_id": "64", "frame_idx": 64, "interpolated": true, "temporal_based": true}], "65": [{"bbox": [68, 106, 73, 111], "label": "drone", "frame_id": "65", "frame_idx": 65, "interpolated": true, "extrapolated": true}, {"bbox": [60, 107, 65, 112], "label": "drone", "frame_id": "65", "frame_idx": 65, "interpolated": true, "temporal_based": true}], "66": [{"bbox": [68, 106, 73, 111], "label": "drone", "frame_id": "66", "frame_idx": 66, "interpolated": true, "extrapolated": true}, {"bbox": [60, 107, 65, 112], "label": "drone", "frame_id": "66", "frame_idx": 66, "interpolated": true, "temporal_based": true}], "67": [{"bbox": [68, 106, 73, 111], "label": "drone", "frame_id": "67", "frame_idx": 67, "interpolated": true, "extrapolated": true}, {"bbox": [61, 107, 66, 112], "label": "drone", "frame_id": "67", "frame_idx": 67, "interpolated": true, "temporal_based": true}], "68": [{"bbox": [68, 106, 73, 111], "label": "drone", "frame_id": "68", "frame_idx": 68, "interpolated": true, "extrapolated": true}, {"bbox": [61, 107, 66, 112], "label": "drone", "frame_id": "68", "frame_idx": 68, "interpolated": true, "temporal_based": true}], "69": [{"bbox": [68, 106, 73, 111], "label": "drone", "frame_id": "69", "frame_idx": 69, "interpolated": true, "extrapolated": true}, {"bbox": [61, 107, 66, 112], "label": "drone", "frame_id": "69", "frame_idx": 69, "interpolated": true, "temporal_based": true}], "71": [{"bbox": [68, 106, 73, 111], "label": "drone", "frame_id": "71", "frame_idx": 71, "interpolated": true, "extrapolated": true}, {"bbox": [62, 108, 67, 113], "label": "drone", "frame_id": "71", "frame_idx": 71, "interpolated": true, "extrapolated": true}], "72": [{"bbox": [68, 106, 73, 111], "label": "drone", "frame_id": "72", "frame_idx": 72, "interpolated": true, "extrapolated": true}, {"bbox": [62, 108, 67, 113], "label": "drone", "frame_id": "72", "frame_idx": 72, "interpolated": true, "extrapolated": true}], "73": [{"bbox": [68, 106, 73, 111], "label": "drone", "frame_id": "73", "frame_idx": 73, "interpolated": true, "extrapolated": true}, {"bbox": [62, 108, 67, 113], "label": "drone", "frame_id": "73", "frame_idx": 73, "interpolated": true, "extrapolated": true}], "74": [{"bbox": [68, 106, 73, 111], "label": "drone", "frame_id": "74", "frame_idx": 74, "interpolated": true, "extrapolated": true}, {"bbox": [62, 108, 67, 113], "label": "drone", "frame_id": "74", "frame_idx": 74, "interpolated": true, "extrapolated": true}]}}, "data02": {"sequence_id": "data02", "total_frames": 50, "key_frames": 5, "total_detections": 50, "direct_detections": 5, "interpolated_detections": 45, "metrics": {"precision": 0.4, "recall": 0.4, "f1": 0.4000000000000001, "tp": 20, "fp": 30, "fn": 30}, "detections": {"0": [{"bbox": [97, 88, 102, 93], "label": "drone", "frame_id": "0", "size": 25, "frame_idx": 0}], "10": [{"bbox": [97, 89, 102, 94], "label": "drone", "frame_id": "10", "size": 25, "frame_idx": 10}], "20": [{"bbox": [97, 88, 102, 93], "label": "drone", "frame_id": "20", "size": 25, "frame_idx": 20}], "30": [{"bbox": [97, 89, 102, 94], "label": "drone", "frame_id": "30", "size": 25, "frame_idx": 30}], "40": [{"bbox": [97, 89, 102, 94], "label": "drone", "frame_id": "40", "size": 25, "frame_idx": 40}], "1": [{"bbox": [97, 88, 102, 93], "label": "drone", "frame_id": "1", "frame_idx": 1, "interpolated": true, "temporal_based": true}], "2": [{"bbox": [97, 88, 102, 93], "label": "drone", "frame_id": "2", "frame_idx": 2, "interpolated": true, "temporal_based": true}], "3": [{"bbox": [97, 88, 102, 93], "label": "drone", "frame_id": "3", "frame_idx": 3, "interpolated": true, "temporal_based": true}], "4": [{"bbox": [97, 88, 102, 93], "label": "drone", "frame_id": "4", "frame_idx": 4, "interpolated": true, "temporal_based": true}], "5": [{"bbox": [97, 88, 102, 93], "label": "drone", "frame_id": "5", "frame_idx": 5, "interpolated": true, "temporal_based": true}], "6": [{"bbox": [97, 88, 102, 93], "label": "drone", "frame_id": "6", "frame_idx": 6, "interpolated": true, "temporal_based": true}], "7": [{"bbox": [97, 88, 102, 93], "label": "drone", "frame_id": "7", "frame_idx": 7, "interpolated": true, "temporal_based": true}], "8": [{"bbox": [97, 88, 102, 93], "label": "drone", "frame_id": "8", "frame_idx": 8, "interpolated": true, "temporal_based": true}], "9": [{"bbox": [97, 88, 102, 93], "label": "drone", "frame_id": "9", "frame_idx": 9, "interpolated": true, "temporal_based": true}], "11": [{"bbox": [97, 88, 102, 93], "label": "drone", "frame_id": "11", "frame_idx": 11, "interpolated": true, "temporal_based": true}], "12": [{"bbox": [97, 88, 102, 93], "label": "drone", "frame_id": "12", "frame_idx": 12, "interpolated": true, "temporal_based": true}], "13": [{"bbox": [97, 88, 102, 93], "label": "drone", "frame_id": "13", "frame_idx": 13, "interpolated": true, "temporal_based": true}], "14": [{"bbox": [97, 88, 102, 93], "label": "drone", "frame_id": "14", "frame_idx": 14, "interpolated": true, "temporal_based": true}], "15": [{"bbox": [97, 88, 102, 93], "label": "drone", "frame_id": "15", "frame_idx": 15, "interpolated": true, "temporal_based": true}], "16": [{"bbox": [97, 88, 102, 93], "label": "drone", "frame_id": "16", "frame_idx": 16, "interpolated": true, "temporal_based": true}], "17": [{"bbox": [97, 88, 102, 93], "label": "drone", "frame_id": "17", "frame_idx": 17, "interpolated": true, "temporal_based": true}], "18": [{"bbox": [97, 88, 102, 93], "label": "drone", "frame_id": "18", "frame_idx": 18, "interpolated": true, "temporal_based": true}], "19": [{"bbox": [97, 88, 102, 93], "label": "drone", "frame_id": "19", "frame_idx": 19, "interpolated": true, "temporal_based": true}], "21": [{"bbox": [97, 88, 102, 93], "label": "drone", "frame_id": "21", "frame_idx": 21, "interpolated": true, "temporal_based": true}], "22": [{"bbox": [97, 88, 102, 93], "label": "drone", "frame_id": "22", "frame_idx": 22, "interpolated": true, "temporal_based": true}], "23": [{"bbox": [97, 88, 102, 93], "label": "drone", "frame_id": "23", "frame_idx": 23, "interpolated": true, "temporal_based": true}], "24": [{"bbox": [97, 88, 102, 93], "label": "drone", "frame_id": "24", "frame_idx": 24, "interpolated": true, "temporal_based": true}], "25": [{"bbox": [97, 88, 102, 93], "label": "drone", "frame_id": "25", "frame_idx": 25, "interpolated": true, "temporal_based": true}], "26": [{"bbox": [97, 88, 102, 93], "label": "drone", "frame_id": "26", "frame_idx": 26, "interpolated": true, "temporal_based": true}], "27": [{"bbox": [97, 88, 102, 93], "label": "drone", "frame_id": "27", "frame_idx": 27, "interpolated": true, "temporal_based": true}], "28": [{"bbox": [97, 88, 102, 93], "label": "drone", "frame_id": "28", "frame_idx": 28, "interpolated": true, "temporal_based": true}], "29": [{"bbox": [97, 88, 102, 93], "label": "drone", "frame_id": "29", "frame_idx": 29, "interpolated": true, "temporal_based": true}], "31": [{"bbox": [97, 89, 102, 94], "label": "drone", "frame_id": "31", "frame_idx": 31, "interpolated": true, "extrapolated": true}], "32": [{"bbox": [97, 89, 102, 94], "label": "drone", "frame_id": "32", "frame_idx": 32, "interpolated": true, "extrapolated": true}], "33": [{"bbox": [97, 89, 102, 94], "label": "drone", "frame_id": "33", "frame_idx": 33, "interpolated": true, "extrapolated": true}], "34": [{"bbox": [97, 89, 102, 94], "label": "drone", "frame_id": "34", "frame_idx": 34, "interpolated": true, "extrapolated": true}], "35": [{"bbox": [97, 89, 102, 94], "label": "drone", "frame_id": "35", "frame_idx": 35, "interpolated": true, "extrapolated": true}], "36": [{"bbox": [97, 89, 102, 94], "label": "drone", "frame_id": "36", "frame_idx": 36, "interpolated": true, "extrapolated": true}], "37": [{"bbox": [97, 89, 102, 94], "label": "drone", "frame_id": "37", "frame_idx": 37, "interpolated": true, "extrapolated": true}], "38": [{"bbox": [97, 89, 102, 94], "label": "drone", "frame_id": "38", "frame_idx": 38, "interpolated": true, "extrapolated": true}], "39": [{"bbox": [97, 89, 102, 94], "label": "drone", "frame_id": "39", "frame_idx": 39, "interpolated": true, "extrapolated": true}], "41": [{"bbox": [97, 89, 102, 94], "label": "drone", "frame_id": "41", "frame_idx": 41, "interpolated": true, "extrapolated": true}], "42": [{"bbox": [97, 89, 102, 94], "label": "drone", "frame_id": "42", "frame_idx": 42, "interpolated": true, "extrapolated": true}], "43": [{"bbox": [97, 89, 102, 94], "label": "drone", "frame_id": "43", "frame_idx": 43, "interpolated": true, "extrapolated": true}], "44": [{"bbox": [97, 89, 102, 94], "label": "drone", "frame_id": "44", "frame_idx": 44, "interpolated": true, "extrapolated": true}], "45": [{"bbox": [97, 89, 102, 94], "label": "drone", "frame_id": "45", "frame_idx": 45, "interpolated": true, "extrapolated": true}], "46": [{"bbox": [97, 89, 102, 94], "label": "drone", "frame_id": "46", "frame_idx": 46, "interpolated": true, "extrapolated": true}], "47": [{"bbox": [97, 89, 102, 94], "label": "drone", "frame_id": "47", "frame_idx": 47, "interpolated": true, "extrapolated": true}], "48": [{"bbox": [97, 89, 102, 94], "label": "drone", "frame_id": "48", "frame_idx": 48, "interpolated": true, "extrapolated": true}], "49": [{"bbox": [97, 89, 102, 94], "label": "drone", "frame_id": "49", "frame_idx": 49, "interpolated": true, "extrapolated": true}]}}, "data04": {"sequence_id": "data04", "total_frames": 50, "key_frames": 5, "total_detections": 50, "direct_detections": 5, "interpolated_detections": 45, "metrics": {"precision": 0.16, "recall": 0.16, "f1": 0.16, "tp": 8, "fp": 42, "fn": 42}, "detections": {"0": [{"bbox": [97, 78, 102, 83], "label": "drone", "frame_id": "0", "size": 25, "frame_idx": 0}], "10": [{"bbox": [101, 83, 106, 88], "label": "drone", "frame_id": "10", "size": 25, "frame_idx": 10}], "20": [{"bbox": [101, 83, 106, 88], "label": "drone", "frame_id": "20", "size": 25, "frame_idx": 20}], "30": [{"bbox": [101, 80, 106, 85], "label": "drone", "frame_id": "30", "size": 25, "frame_idx": 30}], "40": [{"bbox": [103, 83, 108, 88], "label": "drone", "frame_id": "40", "size": 25, "frame_idx": 40}], "1": [{"bbox": [97, 78, 102, 83], "label": "drone", "frame_id": "1", "frame_idx": 1, "interpolated": true, "temporal_based": true}], "2": [{"bbox": [97, 79, 102, 84], "label": "drone", "frame_id": "2", "frame_idx": 2, "interpolated": true, "temporal_based": true}], "3": [{"bbox": [98, 79, 103, 84], "label": "drone", "frame_id": "3", "frame_idx": 3, "interpolated": true, "temporal_based": true}], "4": [{"bbox": [98, 80, 103, 85], "label": "drone", "frame_id": "4", "frame_idx": 4, "interpolated": true, "temporal_based": true}], "5": [{"bbox": [99, 80, 104, 85], "label": "drone", "frame_id": "5", "frame_idx": 5, "interpolated": true, "temporal_based": true}], "6": [{"bbox": [99, 81, 104, 86], "label": "drone", "frame_id": "6", "frame_idx": 6, "interpolated": true, "temporal_based": true}], "7": [{"bbox": [99, 81, 104, 86], "label": "drone", "frame_id": "7", "frame_idx": 7, "interpolated": true, "temporal_based": true}], "8": [{"bbox": [100, 82, 105, 87], "label": "drone", "frame_id": "8", "frame_idx": 8, "interpolated": true, "temporal_based": true}], "9": [{"bbox": [100, 82, 105, 87], "label": "drone", "frame_id": "9", "frame_idx": 9, "interpolated": true, "temporal_based": true}], "11": [{"bbox": [101, 83, 106, 88], "label": "drone", "frame_id": "11", "frame_idx": 11, "interpolated": true, "temporal_based": true}], "12": [{"bbox": [101, 83, 106, 88], "label": "drone", "frame_id": "12", "frame_idx": 12, "interpolated": true, "temporal_based": true}], "13": [{"bbox": [101, 83, 106, 88], "label": "drone", "frame_id": "13", "frame_idx": 13, "interpolated": true, "temporal_based": true}], "14": [{"bbox": [101, 83, 106, 88], "label": "drone", "frame_id": "14", "frame_idx": 14, "interpolated": true, "temporal_based": true}], "15": [{"bbox": [101, 83, 106, 88], "label": "drone", "frame_id": "15", "frame_idx": 15, "interpolated": true, "temporal_based": true}], "16": [{"bbox": [101, 83, 106, 88], "label": "drone", "frame_id": "16", "frame_idx": 16, "interpolated": true, "temporal_based": true}], "17": [{"bbox": [101, 83, 106, 88], "label": "drone", "frame_id": "17", "frame_idx": 17, "interpolated": true, "temporal_based": true}], "18": [{"bbox": [101, 83, 106, 88], "label": "drone", "frame_id": "18", "frame_idx": 18, "interpolated": true, "temporal_based": true}], "19": [{"bbox": [101, 83, 106, 88], "label": "drone", "frame_id": "19", "frame_idx": 19, "interpolated": true, "temporal_based": true}], "21": [{"bbox": [101, 82, 106, 87], "label": "drone", "frame_id": "21", "frame_idx": 21, "interpolated": true, "temporal_based": true}], "22": [{"bbox": [101, 82, 106, 87], "label": "drone", "frame_id": "22", "frame_idx": 22, "interpolated": true, "temporal_based": true}], "23": [{"bbox": [101, 82, 106, 87], "label": "drone", "frame_id": "23", "frame_idx": 23, "interpolated": true, "temporal_based": true}], "24": [{"bbox": [101, 81, 106, 86], "label": "drone", "frame_id": "24", "frame_idx": 24, "interpolated": true, "temporal_based": true}], "25": [{"bbox": [101, 81, 106, 86], "label": "drone", "frame_id": "25", "frame_idx": 25, "interpolated": true, "temporal_based": true}], "26": [{"bbox": [101, 81, 106, 86], "label": "drone", "frame_id": "26", "frame_idx": 26, "interpolated": true, "temporal_based": true}], "27": [{"bbox": [101, 80, 106, 85], "label": "drone", "frame_id": "27", "frame_idx": 27, "interpolated": true, "temporal_based": true}], "28": [{"bbox": [101, 80, 106, 85], "label": "drone", "frame_id": "28", "frame_idx": 28, "interpolated": true, "temporal_based": true}], "29": [{"bbox": [101, 80, 106, 85], "label": "drone", "frame_id": "29", "frame_idx": 29, "interpolated": true, "temporal_based": true}], "31": [{"bbox": [101, 80, 106, 85], "label": "drone", "frame_id": "31", "frame_idx": 31, "interpolated": true, "extrapolated": true}], "32": [{"bbox": [101, 80, 106, 85], "label": "drone", "frame_id": "32", "frame_idx": 32, "interpolated": true, "extrapolated": true}], "33": [{"bbox": [101, 80, 106, 85], "label": "drone", "frame_id": "33", "frame_idx": 33, "interpolated": true, "extrapolated": true}], "34": [{"bbox": [101, 80, 106, 85], "label": "drone", "frame_id": "34", "frame_idx": 34, "interpolated": true, "extrapolated": true}], "35": [{"bbox": [101, 80, 106, 85], "label": "drone", "frame_id": "35", "frame_idx": 35, "interpolated": true, "extrapolated": true}], "36": [{"bbox": [101, 80, 106, 85], "label": "drone", "frame_id": "36", "frame_idx": 36, "interpolated": true, "extrapolated": true}], "37": [{"bbox": [101, 80, 106, 85], "label": "drone", "frame_id": "37", "frame_idx": 37, "interpolated": true, "extrapolated": true}], "38": [{"bbox": [101, 80, 106, 85], "label": "drone", "frame_id": "38", "frame_idx": 38, "interpolated": true, "extrapolated": true}], "39": [{"bbox": [101, 80, 106, 85], "label": "drone", "frame_id": "39", "frame_idx": 39, "interpolated": true, "extrapolated": true}], "41": [{"bbox": [101, 80, 106, 85], "label": "drone", "frame_id": "41", "frame_idx": 41, "interpolated": true, "extrapolated": true}], "42": [{"bbox": [101, 80, 106, 85], "label": "drone", "frame_id": "42", "frame_idx": 42, "interpolated": true, "extrapolated": true}], "43": [{"bbox": [101, 80, 106, 85], "label": "drone", "frame_id": "43", "frame_idx": 43, "interpolated": true, "extrapolated": true}], "44": [{"bbox": [101, 80, 106, 85], "label": "drone", "frame_id": "44", "frame_idx": 44, "interpolated": true, "extrapolated": true}], "45": [{"bbox": [101, 80, 106, 85], "label": "drone", "frame_id": "45", "frame_idx": 45, "interpolated": true, "extrapolated": true}], "46": [{"bbox": [101, 80, 106, 85], "label": "drone", "frame_id": "46", "frame_idx": 46, "interpolated": true, "extrapolated": true}], "47": [{"bbox": [101, 80, 106, 85], "label": "drone", "frame_id": "47", "frame_idx": 47, "interpolated": true, "extrapolated": true}], "48": [{"bbox": [101, 80, 106, 85], "label": "drone", "frame_id": "48", "frame_idx": 48, "interpolated": true, "extrapolated": true}], "49": [{"bbox": [101, 80, 106, 85], "label": "drone", "frame_id": "49", "frame_idx": 49, "interpolated": true, "extrapolated": true}]}}, "data06": {"sequence_id": "data06", "total_frames": 50, "key_frames": 5, "total_detections": 50, "direct_detections": 5, "interpolated_detections": 45, "metrics": {"precision": 0.54, "recall": 0.54, "f1": 0.54, "tp": 27, "fp": 23, "fn": 23}, "detections": {"0": [{"bbox": [92, 123, 97, 128], "label": "drone", "frame_id": "0", "size": 25, "frame_idx": 0}], "10": [{"bbox": [90, 121, 95, 126], "label": "drone", "frame_id": "10", "size": 25, "frame_idx": 10}], "20": [{"bbox": [90, 124, 95, 129], "label": "drone", "frame_id": "20", "size": 25, "frame_idx": 20}], "30": [{"bbox": [90, 123, 95, 128], "label": "drone", "frame_id": "30", "size": 25, "frame_idx": 30}], "40": [{"bbox": [91, 123, 96, 128], "label": "drone", "frame_id": "40", "size": 25, "frame_idx": 40}], "1": [{"bbox": [91, 122, 96, 127], "label": "drone", "frame_id": "1", "frame_idx": 1, "interpolated": true, "temporal_based": true}], "2": [{"bbox": [91, 122, 96, 127], "label": "drone", "frame_id": "2", "frame_idx": 2, "interpolated": true, "temporal_based": true}], "3": [{"bbox": [91, 122, 96, 127], "label": "drone", "frame_id": "3", "frame_idx": 3, "interpolated": true, "temporal_based": true}], "4": [{"bbox": [91, 122, 96, 127], "label": "drone", "frame_id": "4", "frame_idx": 4, "interpolated": true, "temporal_based": true}], "5": [{"bbox": [91, 122, 96, 127], "label": "drone", "frame_id": "5", "frame_idx": 5, "interpolated": true, "temporal_based": true}], "6": [{"bbox": [90, 121, 95, 126], "label": "drone", "frame_id": "6", "frame_idx": 6, "interpolated": true, "temporal_based": true}], "7": [{"bbox": [90, 121, 95, 126], "label": "drone", "frame_id": "7", "frame_idx": 7, "interpolated": true, "temporal_based": true}], "8": [{"bbox": [90, 121, 95, 126], "label": "drone", "frame_id": "8", "frame_idx": 8, "interpolated": true, "temporal_based": true}], "9": [{"bbox": [90, 121, 95, 126], "label": "drone", "frame_id": "9", "frame_idx": 9, "interpolated": true, "temporal_based": true}], "11": [{"bbox": [90, 121, 95, 126], "label": "drone", "frame_id": "11", "frame_idx": 11, "interpolated": true, "temporal_based": true}], "12": [{"bbox": [90, 121, 95, 126], "label": "drone", "frame_id": "12", "frame_idx": 12, "interpolated": true, "temporal_based": true}], "13": [{"bbox": [90, 121, 95, 126], "label": "drone", "frame_id": "13", "frame_idx": 13, "interpolated": true, "temporal_based": true}], "14": [{"bbox": [90, 122, 95, 127], "label": "drone", "frame_id": "14", "frame_idx": 14, "interpolated": true, "temporal_based": true}], "15": [{"bbox": [90, 122, 95, 127], "label": "drone", "frame_id": "15", "frame_idx": 15, "interpolated": true, "temporal_based": true}], "16": [{"bbox": [90, 122, 95, 127], "label": "drone", "frame_id": "16", "frame_idx": 16, "interpolated": true, "temporal_based": true}], "17": [{"bbox": [90, 123, 95, 128], "label": "drone", "frame_id": "17", "frame_idx": 17, "interpolated": true, "temporal_based": true}], "18": [{"bbox": [90, 123, 95, 128], "label": "drone", "frame_id": "18", "frame_idx": 18, "interpolated": true, "temporal_based": true}], "19": [{"bbox": [90, 123, 95, 128], "label": "drone", "frame_id": "19", "frame_idx": 19, "interpolated": true, "temporal_based": true}], "21": [{"bbox": [90, 123, 95, 128], "label": "drone", "frame_id": "21", "frame_idx": 21, "interpolated": true, "temporal_based": true}], "22": [{"bbox": [90, 123, 95, 128], "label": "drone", "frame_id": "22", "frame_idx": 22, "interpolated": true, "temporal_based": true}], "23": [{"bbox": [90, 123, 95, 128], "label": "drone", "frame_id": "23", "frame_idx": 23, "interpolated": true, "temporal_based": true}], "24": [{"bbox": [90, 123, 95, 128], "label": "drone", "frame_id": "24", "frame_idx": 24, "interpolated": true, "temporal_based": true}], "25": [{"bbox": [90, 123, 95, 128], "label": "drone", "frame_id": "25", "frame_idx": 25, "interpolated": true, "temporal_based": true}], "26": [{"bbox": [90, 123, 95, 128], "label": "drone", "frame_id": "26", "frame_idx": 26, "interpolated": true, "temporal_based": true}], "27": [{"bbox": [90, 123, 95, 128], "label": "drone", "frame_id": "27", "frame_idx": 27, "interpolated": true, "temporal_based": true}], "28": [{"bbox": [90, 123, 95, 128], "label": "drone", "frame_id": "28", "frame_idx": 28, "interpolated": true, "temporal_based": true}], "29": [{"bbox": [90, 123, 95, 128], "label": "drone", "frame_id": "29", "frame_idx": 29, "interpolated": true, "temporal_based": true}], "31": [{"bbox": [90, 123, 95, 128], "label": "drone", "frame_id": "31", "frame_idx": 31, "interpolated": true, "extrapolated": true}], "32": [{"bbox": [90, 123, 95, 128], "label": "drone", "frame_id": "32", "frame_idx": 32, "interpolated": true, "extrapolated": true}], "33": [{"bbox": [90, 123, 95, 128], "label": "drone", "frame_id": "33", "frame_idx": 33, "interpolated": true, "extrapolated": true}], "34": [{"bbox": [90, 123, 95, 128], "label": "drone", "frame_id": "34", "frame_idx": 34, "interpolated": true, "extrapolated": true}], "35": [{"bbox": [90, 123, 95, 128], "label": "drone", "frame_id": "35", "frame_idx": 35, "interpolated": true, "extrapolated": true}], "36": [{"bbox": [90, 123, 95, 128], "label": "drone", "frame_id": "36", "frame_idx": 36, "interpolated": true, "extrapolated": true}], "37": [{"bbox": [90, 123, 95, 128], "label": "drone", "frame_id": "37", "frame_idx": 37, "interpolated": true, "extrapolated": true}], "38": [{"bbox": [90, 123, 95, 128], "label": "drone", "frame_id": "38", "frame_idx": 38, "interpolated": true, "extrapolated": true}], "39": [{"bbox": [90, 123, 95, 128], "label": "drone", "frame_id": "39", "frame_idx": 39, "interpolated": true, "extrapolated": true}], "41": [{"bbox": [90, 123, 95, 128], "label": "drone", "frame_id": "41", "frame_idx": 41, "interpolated": true, "extrapolated": true}], "42": [{"bbox": [90, 123, 95, 128], "label": "drone", "frame_id": "42", "frame_idx": 42, "interpolated": true, "extrapolated": true}], "43": [{"bbox": [90, 123, 95, 128], "label": "drone", "frame_id": "43", "frame_idx": 43, "interpolated": true, "extrapolated": true}], "44": [{"bbox": [90, 123, 95, 128], "label": "drone", "frame_id": "44", "frame_idx": 44, "interpolated": true, "extrapolated": true}], "45": [{"bbox": [90, 123, 95, 128], "label": "drone", "frame_id": "45", "frame_idx": 45, "interpolated": true, "extrapolated": true}], "46": [{"bbox": [90, 123, 95, 128], "label": "drone", "frame_id": "46", "frame_idx": 46, "interpolated": true, "extrapolated": true}], "47": [{"bbox": [90, 123, 95, 128], "label": "drone", "frame_id": "47", "frame_idx": 47, "interpolated": true, "extrapolated": true}], "48": [{"bbox": [90, 123, 95, 128], "label": "drone", "frame_id": "48", "frame_idx": 48, "interpolated": true, "extrapolated": true}], "49": [{"bbox": [90, 123, 95, 128], "label": "drone", "frame_id": "49", "frame_idx": 49, "interpolated": true, "extrapolated": true}]}}, "data07": {"sequence_id": "data07", "total_frames": 50, "key_frames": 5, "total_detections": 77, "direct_detections": 5, "interpolated_detections": 72, "metrics": {"precision": 0.2857142857142857, "recall": 0.44, "f1": 0.34645669291338577, "tp": 22, "fp": 55, "fn": 28}, "detections": {"0": [{"bbox": [164, 137, 194, 168], "label": "drone", "frame_id": "0", "size": 930, "frame_idx": 0}], "10": [{"bbox": [149, 117, 180, 147], "label": "drone", "frame_id": "10", "size": 930, "frame_idx": 10}], "20": [{"bbox": [137, 68, 167, 99], "label": "drone", "frame_id": "20", "size": 930, "frame_idx": 20}], "30": [{"bbox": [137, 81, 168, 112], "label": "drone", "frame_id": "30", "size": 961, "frame_idx": 30}], "40": [{"bbox": [137, 81, 168, 112], "label": "drone", "frame_id": "40", "size": 961, "frame_idx": 40}], "1": [{"bbox": [162, 135, 192, 165], "label": "drone", "frame_id": "1", "frame_idx": 1, "interpolated": true, "temporal_based": true}], "2": [{"bbox": [161, 133, 191, 163], "label": "drone", "frame_id": "2", "frame_idx": 2, "interpolated": true, "temporal_based": true}], "3": [{"bbox": [159, 131, 189, 161], "label": "drone", "frame_id": "3", "frame_idx": 3, "interpolated": true, "temporal_based": true}], "4": [{"bbox": [158, 129, 188, 159], "label": "drone", "frame_id": "4", "frame_idx": 4, "interpolated": true, "temporal_based": true}], "5": [{"bbox": [156, 127, 187, 157], "label": "drone", "frame_id": "5", "frame_idx": 5, "interpolated": true, "temporal_based": true}], "6": [{"bbox": [155, 125, 185, 155], "label": "drone", "frame_id": "6", "frame_idx": 6, "interpolated": true, "temporal_based": true}], "7": [{"bbox": [153, 123, 184, 153], "label": "drone", "frame_id": "7", "frame_idx": 7, "interpolated": true, "temporal_based": true}], "8": [{"bbox": [152, 121, 182, 151], "label": "drone", "frame_id": "8", "frame_idx": 8, "interpolated": true, "temporal_based": true}], "9": [{"bbox": [150, 119, 181, 149], "label": "drone", "frame_id": "9", "frame_idx": 9, "interpolated": true, "temporal_based": true}], "11": [{"bbox": [149, 117, 180, 147], "label": "drone", "frame_id": "11", "frame_idx": 11, "interpolated": true, "extrapolated": true}], "12": [{"bbox": [149, 117, 180, 147], "label": "drone", "frame_id": "12", "frame_idx": 12, "interpolated": true, "extrapolated": true}], "13": [{"bbox": [149, 117, 180, 147], "label": "drone", "frame_id": "13", "frame_idx": 13, "interpolated": true, "extrapolated": true}], "14": [{"bbox": [149, 117, 180, 147], "label": "drone", "frame_id": "14", "frame_idx": 14, "interpolated": true, "extrapolated": true}], "15": [{"bbox": [149, 117, 180, 147], "label": "drone", "frame_id": "15", "frame_idx": 15, "interpolated": true, "extrapolated": true}], "16": [{"bbox": [149, 117, 180, 147], "label": "drone", "frame_id": "16", "frame_idx": 16, "interpolated": true, "extrapolated": true}], "17": [{"bbox": [149, 117, 180, 147], "label": "drone", "frame_id": "17", "frame_idx": 17, "interpolated": true, "extrapolated": true}], "18": [{"bbox": [149, 117, 180, 147], "label": "drone", "frame_id": "18", "frame_idx": 18, "interpolated": true, "extrapolated": true}], "19": [{"bbox": [149, 117, 180, 147], "label": "drone", "frame_id": "19", "frame_idx": 19, "interpolated": true, "extrapolated": true}], "21": [{"bbox": [149, 117, 180, 147], "label": "drone", "frame_id": "21", "frame_idx": 21, "interpolated": true, "extrapolated": true}, {"bbox": [137, 69, 167, 100], "label": "drone", "frame_id": "21", "frame_idx": 21, "interpolated": true, "temporal_based": true}], "22": [{"bbox": [149, 117, 180, 147], "label": "drone", "frame_id": "22", "frame_idx": 22, "interpolated": true, "extrapolated": true}, {"bbox": [137, 70, 167, 101], "label": "drone", "frame_id": "22", "frame_idx": 22, "interpolated": true, "temporal_based": true}], "23": [{"bbox": [149, 117, 180, 147], "label": "drone", "frame_id": "23", "frame_idx": 23, "interpolated": true, "extrapolated": true}, {"bbox": [137, 71, 167, 102], "label": "drone", "frame_id": "23", "frame_idx": 23, "interpolated": true, "temporal_based": true}], "24": [{"bbox": [149, 117, 180, 147], "label": "drone", "frame_id": "24", "frame_idx": 24, "interpolated": true, "extrapolated": true}, {"bbox": [137, 73, 167, 104], "label": "drone", "frame_id": "24", "frame_idx": 24, "interpolated": true, "temporal_based": true}], "25": [{"bbox": [149, 117, 180, 147], "label": "drone", "frame_id": "25", "frame_idx": 25, "interpolated": true, "extrapolated": true}, {"bbox": [137, 74, 167, 105], "label": "drone", "frame_id": "25", "frame_idx": 25, "interpolated": true, "temporal_based": true}], "26": [{"bbox": [149, 117, 180, 147], "label": "drone", "frame_id": "26", "frame_idx": 26, "interpolated": true, "extrapolated": true}, {"bbox": [137, 75, 167, 106], "label": "drone", "frame_id": "26", "frame_idx": 26, "interpolated": true, "temporal_based": true}], "27": [{"bbox": [149, 117, 180, 147], "label": "drone", "frame_id": "27", "frame_idx": 27, "interpolated": true, "extrapolated": true}, {"bbox": [137, 77, 167, 108], "label": "drone", "frame_id": "27", "frame_idx": 27, "interpolated": true, "temporal_based": true}], "28": [{"bbox": [149, 117, 180, 147], "label": "drone", "frame_id": "28", "frame_idx": 28, "interpolated": true, "extrapolated": true}, {"bbox": [137, 78, 167, 109], "label": "drone", "frame_id": "28", "frame_idx": 28, "interpolated": true, "temporal_based": true}], "29": [{"bbox": [149, 117, 180, 147], "label": "drone", "frame_id": "29", "frame_idx": 29, "interpolated": true, "extrapolated": true}, {"bbox": [137, 79, 167, 110], "label": "drone", "frame_id": "29", "frame_idx": 29, "interpolated": true, "temporal_based": true}], "31": [{"bbox": [149, 117, 180, 147], "label": "drone", "frame_id": "31", "frame_idx": 31, "interpolated": true, "extrapolated": true}, {"bbox": [137, 81, 168, 112], "label": "drone", "frame_id": "31", "frame_idx": 31, "interpolated": true, "temporal_based": true}], "32": [{"bbox": [149, 117, 180, 147], "label": "drone", "frame_id": "32", "frame_idx": 32, "interpolated": true, "extrapolated": true}, {"bbox": [137, 81, 168, 112], "label": "drone", "frame_id": "32", "frame_idx": 32, "interpolated": true, "temporal_based": true}], "33": [{"bbox": [149, 117, 180, 147], "label": "drone", "frame_id": "33", "frame_idx": 33, "interpolated": true, "extrapolated": true}, {"bbox": [137, 81, 168, 112], "label": "drone", "frame_id": "33", "frame_idx": 33, "interpolated": true, "temporal_based": true}], "34": [{"bbox": [149, 117, 180, 147], "label": "drone", "frame_id": "34", "frame_idx": 34, "interpolated": true, "extrapolated": true}, {"bbox": [137, 81, 168, 112], "label": "drone", "frame_id": "34", "frame_idx": 34, "interpolated": true, "temporal_based": true}], "35": [{"bbox": [149, 117, 180, 147], "label": "drone", "frame_id": "35", "frame_idx": 35, "interpolated": true, "extrapolated": true}, {"bbox": [137, 81, 168, 112], "label": "drone", "frame_id": "35", "frame_idx": 35, "interpolated": true, "temporal_based": true}], "36": [{"bbox": [149, 117, 180, 147], "label": "drone", "frame_id": "36", "frame_idx": 36, "interpolated": true, "extrapolated": true}, {"bbox": [137, 81, 168, 112], "label": "drone", "frame_id": "36", "frame_idx": 36, "interpolated": true, "temporal_based": true}], "37": [{"bbox": [149, 117, 180, 147], "label": "drone", "frame_id": "37", "frame_idx": 37, "interpolated": true, "extrapolated": true}, {"bbox": [137, 81, 168, 112], "label": "drone", "frame_id": "37", "frame_idx": 37, "interpolated": true, "temporal_based": true}], "38": [{"bbox": [149, 117, 180, 147], "label": "drone", "frame_id": "38", "frame_idx": 38, "interpolated": true, "extrapolated": true}, {"bbox": [137, 81, 168, 112], "label": "drone", "frame_id": "38", "frame_idx": 38, "interpolated": true, "temporal_based": true}], "39": [{"bbox": [149, 117, 180, 147], "label": "drone", "frame_id": "39", "frame_idx": 39, "interpolated": true, "extrapolated": true}, {"bbox": [137, 81, 168, 112], "label": "drone", "frame_id": "39", "frame_idx": 39, "interpolated": true, "temporal_based": true}], "41": [{"bbox": [149, 117, 180, 147], "label": "drone", "frame_id": "41", "frame_idx": 41, "interpolated": true, "extrapolated": true}, {"bbox": [137, 81, 168, 112], "label": "drone", "frame_id": "41", "frame_idx": 41, "interpolated": true, "extrapolated": true}], "42": [{"bbox": [149, 117, 180, 147], "label": "drone", "frame_id": "42", "frame_idx": 42, "interpolated": true, "extrapolated": true}, {"bbox": [137, 81, 168, 112], "label": "drone", "frame_id": "42", "frame_idx": 42, "interpolated": true, "extrapolated": true}], "43": [{"bbox": [149, 117, 180, 147], "label": "drone", "frame_id": "43", "frame_idx": 43, "interpolated": true, "extrapolated": true}, {"bbox": [137, 81, 168, 112], "label": "drone", "frame_id": "43", "frame_idx": 43, "interpolated": true, "extrapolated": true}], "44": [{"bbox": [149, 117, 180, 147], "label": "drone", "frame_id": "44", "frame_idx": 44, "interpolated": true, "extrapolated": true}, {"bbox": [137, 81, 168, 112], "label": "drone", "frame_id": "44", "frame_idx": 44, "interpolated": true, "extrapolated": true}], "45": [{"bbox": [149, 117, 180, 147], "label": "drone", "frame_id": "45", "frame_idx": 45, "interpolated": true, "extrapolated": true}, {"bbox": [137, 81, 168, 112], "label": "drone", "frame_id": "45", "frame_idx": 45, "interpolated": true, "extrapolated": true}], "46": [{"bbox": [149, 117, 180, 147], "label": "drone", "frame_id": "46", "frame_idx": 46, "interpolated": true, "extrapolated": true}, {"bbox": [137, 81, 168, 112], "label": "drone", "frame_id": "46", "frame_idx": 46, "interpolated": true, "extrapolated": true}], "47": [{"bbox": [149, 117, 180, 147], "label": "drone", "frame_id": "47", "frame_idx": 47, "interpolated": true, "extrapolated": true}, {"bbox": [137, 81, 168, 112], "label": "drone", "frame_id": "47", "frame_idx": 47, "interpolated": true, "extrapolated": true}], "48": [{"bbox": [149, 117, 180, 147], "label": "drone", "frame_id": "48", "frame_idx": 48, "interpolated": true, "extrapolated": true}, {"bbox": [137, 81, 168, 112], "label": "drone", "frame_id": "48", "frame_idx": 48, "interpolated": true, "extrapolated": true}], "49": [{"bbox": [149, 117, 180, 147], "label": "drone", "frame_id": "49", "frame_idx": 49, "interpolated": true, "extrapolated": true}, {"bbox": [137, 81, 168, 112], "label": "drone", "frame_id": "49", "frame_idx": 49, "interpolated": true, "extrapolated": true}]}}, "data19": {"sequence_id": "data19", "total_frames": 75, "key_frames": 8, "total_detections": 137, "direct_detections": 8, "interpolated_detections": 129, "metrics": {"precision": 0.0, "recall": 0, "f1": 0, "tp": 0, "fp": 137, "fn": 0}, "detections": {"0": [{"bbox": [308, 270, 321, 280], "label": "drone", "frame_id": "0", "size": 130, "frame_idx": 0}], "10": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "10", "size": 130, "frame_idx": 10}], "20": [{"bbox": [321, 270, 333, 280], "label": "drone", "frame_id": "20", "size": 120, "frame_idx": 20}], "30": [{"bbox": [307, 269, 320, 280], "label": "drone", "frame_id": "30", "size": 154, "frame_idx": 30}], "40": [{"bbox": [302, 270, 315, 280], "label": "drone", "frame_id": "40", "size": 130, "frame_idx": 40}], "50": [{"bbox": [307, 270, 320, 280], "label": "drone", "frame_id": "50", "size": 130, "frame_idx": 50}], "60": [{"bbox": [321, 270, 333, 280], "label": "drone", "frame_id": "60", "size": 120, "frame_idx": 60}], "70": [{"bbox": [337, 268, 350, 279], "label": "drone", "frame_id": "70", "size": 154, "frame_idx": 70}], "1": [{"bbox": [311, 270, 324, 280], "label": "drone", "frame_id": "1", "frame_idx": 1, "interpolated": true, "temporal_based": true}], "2": [{"bbox": [314, 270, 327, 280], "label": "drone", "frame_id": "2", "frame_idx": 2, "interpolated": true, "temporal_based": true}], "3": [{"bbox": [318, 270, 331, 280], "label": "drone", "frame_id": "3", "frame_idx": 3, "interpolated": true, "temporal_based": true}], "4": [{"bbox": [321, 270, 334, 280], "label": "drone", "frame_id": "4", "frame_idx": 4, "interpolated": true, "temporal_based": true}], "5": [{"bbox": [325, 270, 338, 280], "label": "drone", "frame_id": "5", "frame_idx": 5, "interpolated": true, "temporal_based": true}], "6": [{"bbox": [328, 270, 341, 280], "label": "drone", "frame_id": "6", "frame_idx": 6, "interpolated": true, "temporal_based": true}], "7": [{"bbox": [331, 270, 344, 280], "label": "drone", "frame_id": "7", "frame_idx": 7, "interpolated": true, "temporal_based": true}], "8": [{"bbox": [335, 270, 348, 280], "label": "drone", "frame_id": "8", "frame_idx": 8, "interpolated": true, "temporal_based": true}], "9": [{"bbox": [338, 270, 351, 280], "label": "drone", "frame_id": "9", "frame_idx": 9, "interpolated": true, "temporal_based": true}], "11": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "11", "frame_idx": 11, "interpolated": true, "extrapolated": true}], "12": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "12", "frame_idx": 12, "interpolated": true, "extrapolated": true}], "13": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "13", "frame_idx": 13, "interpolated": true, "extrapolated": true}], "14": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "14", "frame_idx": 14, "interpolated": true, "extrapolated": true}], "15": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "15", "frame_idx": 15, "interpolated": true, "extrapolated": true}], "16": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "16", "frame_idx": 16, "interpolated": true, "extrapolated": true}], "17": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "17", "frame_idx": 17, "interpolated": true, "extrapolated": true}], "18": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "18", "frame_idx": 18, "interpolated": true, "extrapolated": true}], "19": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "19", "frame_idx": 19, "interpolated": true, "extrapolated": true}], "21": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "21", "frame_idx": 21, "interpolated": true, "extrapolated": true}, {"bbox": [319, 269, 331, 280], "label": "drone", "frame_id": "21", "frame_idx": 21, "interpolated": true, "temporal_based": true}], "22": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "22", "frame_idx": 22, "interpolated": true, "extrapolated": true}, {"bbox": [318, 269, 330, 280], "label": "drone", "frame_id": "22", "frame_idx": 22, "interpolated": true, "temporal_based": true}], "23": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "23", "frame_idx": 23, "interpolated": true, "extrapolated": true}, {"bbox": [316, 269, 329, 280], "label": "drone", "frame_id": "23", "frame_idx": 23, "interpolated": true, "temporal_based": true}], "24": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "24", "frame_idx": 24, "interpolated": true, "extrapolated": true}, {"bbox": [315, 269, 327, 280], "label": "drone", "frame_id": "24", "frame_idx": 24, "interpolated": true, "temporal_based": true}], "25": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "25", "frame_idx": 25, "interpolated": true, "extrapolated": true}, {"bbox": [314, 269, 326, 280], "label": "drone", "frame_id": "25", "frame_idx": 25, "interpolated": true, "temporal_based": true}], "26": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "26", "frame_idx": 26, "interpolated": true, "extrapolated": true}, {"bbox": [312, 269, 325, 280], "label": "drone", "frame_id": "26", "frame_idx": 26, "interpolated": true, "temporal_based": true}], "27": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "27", "frame_idx": 27, "interpolated": true, "extrapolated": true}, {"bbox": [311, 269, 323, 280], "label": "drone", "frame_id": "27", "frame_idx": 27, "interpolated": true, "temporal_based": true}], "28": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "28", "frame_idx": 28, "interpolated": true, "extrapolated": true}, {"bbox": [309, 269, 322, 280], "label": "drone", "frame_id": "28", "frame_idx": 28, "interpolated": true, "temporal_based": true}], "29": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "29", "frame_idx": 29, "interpolated": true, "extrapolated": true}, {"bbox": [308, 269, 321, 280], "label": "drone", "frame_id": "29", "frame_idx": 29, "interpolated": true, "temporal_based": true}], "31": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "31", "frame_idx": 31, "interpolated": true, "extrapolated": true}, {"bbox": [306, 269, 319, 280], "label": "drone", "frame_id": "31", "frame_idx": 31, "interpolated": true, "temporal_based": true}], "32": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "32", "frame_idx": 32, "interpolated": true, "extrapolated": true}, {"bbox": [306, 269, 319, 280], "label": "drone", "frame_id": "32", "frame_idx": 32, "interpolated": true, "temporal_based": true}], "33": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "33", "frame_idx": 33, "interpolated": true, "extrapolated": true}, {"bbox": [305, 269, 318, 280], "label": "drone", "frame_id": "33", "frame_idx": 33, "interpolated": true, "temporal_based": true}], "34": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "34", "frame_idx": 34, "interpolated": true, "extrapolated": true}, {"bbox": [305, 269, 318, 280], "label": "drone", "frame_id": "34", "frame_idx": 34, "interpolated": true, "temporal_based": true}], "35": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "35", "frame_idx": 35, "interpolated": true, "extrapolated": true}, {"bbox": [304, 269, 317, 280], "label": "drone", "frame_id": "35", "frame_idx": 35, "interpolated": true, "temporal_based": true}], "36": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "36", "frame_idx": 36, "interpolated": true, "extrapolated": true}, {"bbox": [304, 269, 317, 280], "label": "drone", "frame_id": "36", "frame_idx": 36, "interpolated": true, "temporal_based": true}], "37": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "37", "frame_idx": 37, "interpolated": true, "extrapolated": true}, {"bbox": [303, 269, 316, 280], "label": "drone", "frame_id": "37", "frame_idx": 37, "interpolated": true, "temporal_based": true}], "38": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "38", "frame_idx": 38, "interpolated": true, "extrapolated": true}, {"bbox": [303, 269, 316, 280], "label": "drone", "frame_id": "38", "frame_idx": 38, "interpolated": true, "temporal_based": true}], "39": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "39", "frame_idx": 39, "interpolated": true, "extrapolated": true}, {"bbox": [302, 269, 315, 280], "label": "drone", "frame_id": "39", "frame_idx": 39, "interpolated": true, "temporal_based": true}], "41": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "41", "frame_idx": 41, "interpolated": true, "extrapolated": true}, {"bbox": [302, 270, 315, 280], "label": "drone", "frame_id": "41", "frame_idx": 41, "interpolated": true, "temporal_based": true}], "42": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "42", "frame_idx": 42, "interpolated": true, "extrapolated": true}, {"bbox": [303, 270, 316, 280], "label": "drone", "frame_id": "42", "frame_idx": 42, "interpolated": true, "temporal_based": true}], "43": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "43", "frame_idx": 43, "interpolated": true, "extrapolated": true}, {"bbox": [303, 270, 316, 280], "label": "drone", "frame_id": "43", "frame_idx": 43, "interpolated": true, "temporal_based": true}], "44": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "44", "frame_idx": 44, "interpolated": true, "extrapolated": true}, {"bbox": [304, 270, 317, 280], "label": "drone", "frame_id": "44", "frame_idx": 44, "interpolated": true, "temporal_based": true}], "45": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "45", "frame_idx": 45, "interpolated": true, "extrapolated": true}, {"bbox": [304, 270, 317, 280], "label": "drone", "frame_id": "45", "frame_idx": 45, "interpolated": true, "temporal_based": true}], "46": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "46", "frame_idx": 46, "interpolated": true, "extrapolated": true}, {"bbox": [305, 270, 318, 280], "label": "drone", "frame_id": "46", "frame_idx": 46, "interpolated": true, "temporal_based": true}], "47": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "47", "frame_idx": 47, "interpolated": true, "extrapolated": true}, {"bbox": [305, 270, 318, 280], "label": "drone", "frame_id": "47", "frame_idx": 47, "interpolated": true, "temporal_based": true}], "48": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "48", "frame_idx": 48, "interpolated": true, "extrapolated": true}, {"bbox": [306, 270, 319, 280], "label": "drone", "frame_id": "48", "frame_idx": 48, "interpolated": true, "temporal_based": true}], "49": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "49", "frame_idx": 49, "interpolated": true, "extrapolated": true}, {"bbox": [306, 270, 319, 280], "label": "drone", "frame_id": "49", "frame_idx": 49, "interpolated": true, "temporal_based": true}], "51": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "51", "frame_idx": 51, "interpolated": true, "extrapolated": true}, {"bbox": [307, 270, 320, 280], "label": "drone", "frame_id": "51", "frame_idx": 51, "interpolated": true, "extrapolated": true}], "52": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "52", "frame_idx": 52, "interpolated": true, "extrapolated": true}, {"bbox": [307, 270, 320, 280], "label": "drone", "frame_id": "52", "frame_idx": 52, "interpolated": true, "extrapolated": true}], "53": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "53", "frame_idx": 53, "interpolated": true, "extrapolated": true}, {"bbox": [307, 270, 320, 280], "label": "drone", "frame_id": "53", "frame_idx": 53, "interpolated": true, "extrapolated": true}], "54": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "54", "frame_idx": 54, "interpolated": true, "extrapolated": true}, {"bbox": [307, 270, 320, 280], "label": "drone", "frame_id": "54", "frame_idx": 54, "interpolated": true, "extrapolated": true}], "55": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "55", "frame_idx": 55, "interpolated": true, "extrapolated": true}, {"bbox": [307, 270, 320, 280], "label": "drone", "frame_id": "55", "frame_idx": 55, "interpolated": true, "extrapolated": true}], "56": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "56", "frame_idx": 56, "interpolated": true, "extrapolated": true}, {"bbox": [307, 270, 320, 280], "label": "drone", "frame_id": "56", "frame_idx": 56, "interpolated": true, "extrapolated": true}], "57": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "57", "frame_idx": 57, "interpolated": true, "extrapolated": true}, {"bbox": [307, 270, 320, 280], "label": "drone", "frame_id": "57", "frame_idx": 57, "interpolated": true, "extrapolated": true}], "58": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "58", "frame_idx": 58, "interpolated": true, "extrapolated": true}, {"bbox": [307, 270, 320, 280], "label": "drone", "frame_id": "58", "frame_idx": 58, "interpolated": true, "extrapolated": true}], "59": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "59", "frame_idx": 59, "interpolated": true, "extrapolated": true}, {"bbox": [307, 270, 320, 280], "label": "drone", "frame_id": "59", "frame_idx": 59, "interpolated": true, "extrapolated": true}], "61": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "61", "frame_idx": 61, "interpolated": true, "extrapolated": true}, {"bbox": [307, 270, 320, 280], "label": "drone", "frame_id": "61", "frame_idx": 61, "interpolated": true, "extrapolated": true}, {"bbox": [322, 269, 334, 279], "label": "drone", "frame_id": "61", "frame_idx": 61, "interpolated": true, "temporal_based": true}], "62": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "62", "frame_idx": 62, "interpolated": true, "extrapolated": true}, {"bbox": [307, 270, 320, 280], "label": "drone", "frame_id": "62", "frame_idx": 62, "interpolated": true, "extrapolated": true}, {"bbox": [324, 269, 336, 279], "label": "drone", "frame_id": "62", "frame_idx": 62, "interpolated": true, "temporal_based": true}], "63": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "63", "frame_idx": 63, "interpolated": true, "extrapolated": true}, {"bbox": [307, 270, 320, 280], "label": "drone", "frame_id": "63", "frame_idx": 63, "interpolated": true, "extrapolated": true}, {"bbox": [325, 269, 338, 279], "label": "drone", "frame_id": "63", "frame_idx": 63, "interpolated": true, "temporal_based": true}], "64": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "64", "frame_idx": 64, "interpolated": true, "extrapolated": true}, {"bbox": [307, 270, 320, 280], "label": "drone", "frame_id": "64", "frame_idx": 64, "interpolated": true, "extrapolated": true}, {"bbox": [327, 269, 339, 279], "label": "drone", "frame_id": "64", "frame_idx": 64, "interpolated": true, "temporal_based": true}], "65": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "65", "frame_idx": 65, "interpolated": true, "extrapolated": true}, {"bbox": [307, 270, 320, 280], "label": "drone", "frame_id": "65", "frame_idx": 65, "interpolated": true, "extrapolated": true}, {"bbox": [329, 269, 341, 279], "label": "drone", "frame_id": "65", "frame_idx": 65, "interpolated": true, "temporal_based": true}], "66": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "66", "frame_idx": 66, "interpolated": true, "extrapolated": true}, {"bbox": [307, 270, 320, 280], "label": "drone", "frame_id": "66", "frame_idx": 66, "interpolated": true, "extrapolated": true}, {"bbox": [330, 268, 343, 279], "label": "drone", "frame_id": "66", "frame_idx": 66, "interpolated": true, "temporal_based": true}], "67": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "67", "frame_idx": 67, "interpolated": true, "extrapolated": true}, {"bbox": [307, 270, 320, 280], "label": "drone", "frame_id": "67", "frame_idx": 67, "interpolated": true, "extrapolated": true}, {"bbox": [332, 268, 344, 279], "label": "drone", "frame_id": "67", "frame_idx": 67, "interpolated": true, "temporal_based": true}], "68": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "68", "frame_idx": 68, "interpolated": true, "extrapolated": true}, {"bbox": [307, 270, 320, 280], "label": "drone", "frame_id": "68", "frame_idx": 68, "interpolated": true, "extrapolated": true}, {"bbox": [333, 268, 346, 279], "label": "drone", "frame_id": "68", "frame_idx": 68, "interpolated": true, "temporal_based": true}], "69": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "69", "frame_idx": 69, "interpolated": true, "extrapolated": true}, {"bbox": [307, 270, 320, 280], "label": "drone", "frame_id": "69", "frame_idx": 69, "interpolated": true, "extrapolated": true}, {"bbox": [335, 268, 348, 279], "label": "drone", "frame_id": "69", "frame_idx": 69, "interpolated": true, "temporal_based": true}], "71": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "71", "frame_idx": 71, "interpolated": true, "extrapolated": true}, {"bbox": [307, 270, 320, 280], "label": "drone", "frame_id": "71", "frame_idx": 71, "interpolated": true, "extrapolated": true}, {"bbox": [337, 268, 350, 279], "label": "drone", "frame_id": "71", "frame_idx": 71, "interpolated": true, "extrapolated": true}], "72": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "72", "frame_idx": 72, "interpolated": true, "extrapolated": true}, {"bbox": [307, 270, 320, 280], "label": "drone", "frame_id": "72", "frame_idx": 72, "interpolated": true, "extrapolated": true}, {"bbox": [337, 268, 350, 279], "label": "drone", "frame_id": "72", "frame_idx": 72, "interpolated": true, "extrapolated": true}], "73": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "73", "frame_idx": 73, "interpolated": true, "extrapolated": true}, {"bbox": [307, 270, 320, 280], "label": "drone", "frame_id": "73", "frame_idx": 73, "interpolated": true, "extrapolated": true}, {"bbox": [337, 268, 350, 279], "label": "drone", "frame_id": "73", "frame_idx": 73, "interpolated": true, "extrapolated": true}], "74": [{"bbox": [342, 270, 355, 280], "label": "drone", "frame_id": "74", "frame_idx": 74, "interpolated": true, "extrapolated": true}, {"bbox": [307, 270, 320, 280], "label": "drone", "frame_id": "74", "frame_idx": 74, "interpolated": true, "extrapolated": true}, {"bbox": [337, 268, 350, 279], "label": "drone", "frame_id": "74", "frame_idx": 74, "interpolated": true, "extrapolated": true}]}}, "data23": {"sequence_id": "data23", "total_frames": 50, "key_frames": 5, "total_detections": 50, "direct_detections": 5, "interpolated_detections": 45, "metrics": {"precision": 0.0, "recall": 0, "f1": 0, "tp": 0, "fp": 50, "fn": 0}, "detections": {"0": [{"bbox": [348, 244, 356, 252], "label": "drone", "frame_id": "0", "size": 64, "frame_idx": 0}], "10": [{"bbox": [350, 244, 356, 251], "label": "drone", "frame_id": "10", "size": 42, "frame_idx": 10}], "20": [{"bbox": [348, 244, 356, 252], "label": "drone", "frame_id": "20", "size": 64, "frame_idx": 20}], "30": [{"bbox": [348, 244, 356, 252], "label": "drone", "frame_id": "30", "size": 64, "frame_idx": 30}], "40": [{"bbox": [348, 244, 356, 252], "label": "drone", "frame_id": "40", "size": 64, "frame_idx": 40}], "1": [{"bbox": [348, 244, 356, 251], "label": "drone", "frame_id": "1", "frame_idx": 1, "interpolated": true, "temporal_based": true}], "2": [{"bbox": [348, 244, 356, 251], "label": "drone", "frame_id": "2", "frame_idx": 2, "interpolated": true, "temporal_based": true}], "3": [{"bbox": [348, 244, 356, 251], "label": "drone", "frame_id": "3", "frame_idx": 3, "interpolated": true, "temporal_based": true}], "4": [{"bbox": [348, 244, 356, 251], "label": "drone", "frame_id": "4", "frame_idx": 4, "interpolated": true, "temporal_based": true}], "5": [{"bbox": [349, 244, 356, 251], "label": "drone", "frame_id": "5", "frame_idx": 5, "interpolated": true, "temporal_based": true}], "6": [{"bbox": [349, 244, 356, 251], "label": "drone", "frame_id": "6", "frame_idx": 6, "interpolated": true, "temporal_based": true}], "7": [{"bbox": [349, 244, 356, 251], "label": "drone", "frame_id": "7", "frame_idx": 7, "interpolated": true, "temporal_based": true}], "8": [{"bbox": [349, 244, 356, 251], "label": "drone", "frame_id": "8", "frame_idx": 8, "interpolated": true, "temporal_based": true}], "9": [{"bbox": [349, 244, 356, 251], "label": "drone", "frame_id": "9", "frame_idx": 9, "interpolated": true, "temporal_based": true}], "11": [{"bbox": [349, 244, 356, 251], "label": "drone", "frame_id": "11", "frame_idx": 11, "interpolated": true, "temporal_based": true}], "12": [{"bbox": [349, 244, 356, 251], "label": "drone", "frame_id": "12", "frame_idx": 12, "interpolated": true, "temporal_based": true}], "13": [{"bbox": [349, 244, 356, 251], "label": "drone", "frame_id": "13", "frame_idx": 13, "interpolated": true, "temporal_based": true}], "14": [{"bbox": [349, 244, 356, 251], "label": "drone", "frame_id": "14", "frame_idx": 14, "interpolated": true, "temporal_based": true}], "15": [{"bbox": [349, 244, 356, 251], "label": "drone", "frame_id": "15", "frame_idx": 15, "interpolated": true, "temporal_based": true}], "16": [{"bbox": [348, 244, 356, 251], "label": "drone", "frame_id": "16", "frame_idx": 16, "interpolated": true, "temporal_based": true}], "17": [{"bbox": [348, 244, 356, 251], "label": "drone", "frame_id": "17", "frame_idx": 17, "interpolated": true, "temporal_based": true}], "18": [{"bbox": [348, 244, 356, 251], "label": "drone", "frame_id": "18", "frame_idx": 18, "interpolated": true, "temporal_based": true}], "19": [{"bbox": [348, 244, 356, 251], "label": "drone", "frame_id": "19", "frame_idx": 19, "interpolated": true, "temporal_based": true}], "21": [{"bbox": [348, 244, 356, 252], "label": "drone", "frame_id": "21", "frame_idx": 21, "interpolated": true, "temporal_based": true}], "22": [{"bbox": [348, 244, 356, 252], "label": "drone", "frame_id": "22", "frame_idx": 22, "interpolated": true, "temporal_based": true}], "23": [{"bbox": [348, 244, 356, 252], "label": "drone", "frame_id": "23", "frame_idx": 23, "interpolated": true, "temporal_based": true}], "24": [{"bbox": [348, 244, 356, 252], "label": "drone", "frame_id": "24", "frame_idx": 24, "interpolated": true, "temporal_based": true}], "25": [{"bbox": [348, 244, 356, 252], "label": "drone", "frame_id": "25", "frame_idx": 25, "interpolated": true, "temporal_based": true}], "26": [{"bbox": [348, 244, 356, 252], "label": "drone", "frame_id": "26", "frame_idx": 26, "interpolated": true, "temporal_based": true}], "27": [{"bbox": [348, 244, 356, 252], "label": "drone", "frame_id": "27", "frame_idx": 27, "interpolated": true, "temporal_based": true}], "28": [{"bbox": [348, 244, 356, 252], "label": "drone", "frame_id": "28", "frame_idx": 28, "interpolated": true, "temporal_based": true}], "29": [{"bbox": [348, 244, 356, 252], "label": "drone", "frame_id": "29", "frame_idx": 29, "interpolated": true, "temporal_based": true}], "31": [{"bbox": [348, 244, 356, 252], "label": "drone", "frame_id": "31", "frame_idx": 31, "interpolated": true, "extrapolated": true}], "32": [{"bbox": [348, 244, 356, 252], "label": "drone", "frame_id": "32", "frame_idx": 32, "interpolated": true, "extrapolated": true}], "33": [{"bbox": [348, 244, 356, 252], "label": "drone", "frame_id": "33", "frame_idx": 33, "interpolated": true, "extrapolated": true}], "34": [{"bbox": [348, 244, 356, 252], "label": "drone", "frame_id": "34", "frame_idx": 34, "interpolated": true, "extrapolated": true}], "35": [{"bbox": [348, 244, 356, 252], "label": "drone", "frame_id": "35", "frame_idx": 35, "interpolated": true, "extrapolated": true}], "36": [{"bbox": [348, 244, 356, 252], "label": "drone", "frame_id": "36", "frame_idx": 36, "interpolated": true, "extrapolated": true}], "37": [{"bbox": [348, 244, 356, 252], "label": "drone", "frame_id": "37", "frame_idx": 37, "interpolated": true, "extrapolated": true}], "38": [{"bbox": [348, 244, 356, 252], "label": "drone", "frame_id": "38", "frame_idx": 38, "interpolated": true, "extrapolated": true}], "39": [{"bbox": [348, 244, 356, 252], "label": "drone", "frame_id": "39", "frame_idx": 39, "interpolated": true, "extrapolated": true}], "41": [{"bbox": [348, 244, 356, 252], "label": "drone", "frame_id": "41", "frame_idx": 41, "interpolated": true, "extrapolated": true}], "42": [{"bbox": [348, 244, 356, 252], "label": "drone", "frame_id": "42", "frame_idx": 42, "interpolated": true, "extrapolated": true}], "43": [{"bbox": [348, 244, 356, 252], "label": "drone", "frame_id": "43", "frame_idx": 43, "interpolated": true, "extrapolated": true}], "44": [{"bbox": [348, 244, 356, 252], "label": "drone", "frame_id": "44", "frame_idx": 44, "interpolated": true, "extrapolated": true}], "45": [{"bbox": [348, 244, 356, 252], "label": "drone", "frame_id": "45", "frame_idx": 45, "interpolated": true, "extrapolated": true}], "46": [{"bbox": [348, 244, 356, 252], "label": "drone", "frame_id": "46", "frame_idx": 46, "interpolated": true, "extrapolated": true}], "47": [{"bbox": [348, 244, 356, 252], "label": "drone", "frame_id": "47", "frame_idx": 47, "interpolated": true, "extrapolated": true}], "48": [{"bbox": [348, 244, 356, 252], "label": "drone", "frame_id": "48", "frame_idx": 48, "interpolated": true, "extrapolated": true}], "49": [{"bbox": [348, 244, 356, 252], "label": "drone", "frame_id": "49", "frame_idx": 49, "interpolated": true, "extrapolated": true}]}}, "data26": {"sequence_id": "data26", "total_frames": 75, "key_frames": 8, "total_detections": 70, "direct_detections": 8, "interpolated_detections": 62, "metrics": {"precision": 0.0, "recall": 0, "f1": 0, "tp": 0, "fp": 70, "fn": 0}, "detections": {"0": [{"bbox": [548, 83, 551, 86], "label": "drone", "frame_id": "0", "size": 9, "frame_idx": 0}], "10": [{"bbox": [548, 24, 551, 27], "label": "drone", "frame_id": "10", "size": 9, "frame_idx": 10}], "20": [{"bbox": [530, 80, 533, 83], "label": "drone", "frame_id": "20", "size": 9, "frame_idx": 20}], "30": [{"bbox": [530, 80, 533, 83], "label": "drone", "frame_id": "30", "size": 9, "frame_idx": 30}], "40": [{"bbox": [530, 80, 533, 83], "label": "drone", "frame_id": "40", "size": 9, "frame_idx": 40}], "50": [{"bbox": [530, 79, 533, 82], "label": "drone", "frame_id": "50", "size": 9, "frame_idx": 50}], "60": [{"bbox": [530, 80, 533, 83], "label": "drone", "frame_id": "60", "size": 9, "frame_idx": 60}], "70": [{"bbox": [530, 79, 533, 82], "label": "drone", "frame_id": "70", "size": 9, "frame_idx": 70}], "1": [], "2": [], "3": [], "4": [], "5": [], "6": [], "7": [], "8": [], "9": [], "11": [], "12": [], "13": [], "14": [], "15": [], "16": [], "17": [], "18": [], "19": [], "21": [{"bbox": [530, 80, 533, 83], "label": "drone", "frame_id": "21", "frame_idx": 21, "interpolated": true, "temporal_based": true}], "22": [{"bbox": [530, 80, 533, 83], "label": "drone", "frame_id": "22", "frame_idx": 22, "interpolated": true, "temporal_based": true}], "23": [{"bbox": [530, 80, 533, 83], "label": "drone", "frame_id": "23", "frame_idx": 23, "interpolated": true, "temporal_based": true}], "24": [{"bbox": [530, 80, 533, 83], "label": "drone", "frame_id": "24", "frame_idx": 24, "interpolated": true, "temporal_based": true}], "25": [{"bbox": [530, 80, 533, 83], "label": "drone", "frame_id": "25", "frame_idx": 25, "interpolated": true, "temporal_based": true}], "26": [{"bbox": [530, 80, 533, 83], "label": "drone", "frame_id": "26", "frame_idx": 26, "interpolated": true, "temporal_based": true}], "27": [{"bbox": [530, 80, 533, 83], "label": "drone", "frame_id": "27", "frame_idx": 27, "interpolated": true, "temporal_based": true}], "28": [{"bbox": [530, 80, 533, 83], "label": "drone", "frame_id": "28", "frame_idx": 28, "interpolated": true, "temporal_based": true}], "29": [{"bbox": [530, 80, 533, 83], "label": "drone", "frame_id": "29", "frame_idx": 29, "interpolated": true, "temporal_based": true}], "31": [{"bbox": [530, 80, 533, 83], "label": "drone", "frame_id": "31", "frame_idx": 31, "interpolated": true, "temporal_based": true}], "32": [{"bbox": [530, 80, 533, 83], "label": "drone", "frame_id": "32", "frame_idx": 32, "interpolated": true, "temporal_based": true}], "33": [{"bbox": [530, 80, 533, 83], "label": "drone", "frame_id": "33", "frame_idx": 33, "interpolated": true, "temporal_based": true}], "34": [{"bbox": [530, 80, 533, 83], "label": "drone", "frame_id": "34", "frame_idx": 34, "interpolated": true, "temporal_based": true}], "35": [{"bbox": [530, 80, 533, 83], "label": "drone", "frame_id": "35", "frame_idx": 35, "interpolated": true, "temporal_based": true}], "36": [{"bbox": [530, 80, 533, 83], "label": "drone", "frame_id": "36", "frame_idx": 36, "interpolated": true, "temporal_based": true}], "37": [{"bbox": [530, 80, 533, 83], "label": "drone", "frame_id": "37", "frame_idx": 37, "interpolated": true, "temporal_based": true}], "38": [{"bbox": [530, 80, 533, 83], "label": "drone", "frame_id": "38", "frame_idx": 38, "interpolated": true, "temporal_based": true}], "39": [{"bbox": [530, 80, 533, 83], "label": "drone", "frame_id": "39", "frame_idx": 39, "interpolated": true, "temporal_based": true}], "41": [{"bbox": [530, 79, 533, 82], "label": "drone", "frame_id": "41", "frame_idx": 41, "interpolated": true, "temporal_based": true}], "42": [{"bbox": [530, 79, 533, 82], "label": "drone", "frame_id": "42", "frame_idx": 42, "interpolated": true, "temporal_based": true}], "43": [{"bbox": [530, 79, 533, 82], "label": "drone", "frame_id": "43", "frame_idx": 43, "interpolated": true, "temporal_based": true}], "44": [{"bbox": [530, 79, 533, 82], "label": "drone", "frame_id": "44", "frame_idx": 44, "interpolated": true, "temporal_based": true}], "45": [{"bbox": [530, 79, 533, 82], "label": "drone", "frame_id": "45", "frame_idx": 45, "interpolated": true, "temporal_based": true}], "46": [{"bbox": [530, 79, 533, 82], "label": "drone", "frame_id": "46", "frame_idx": 46, "interpolated": true, "temporal_based": true}], "47": [{"bbox": [530, 79, 533, 82], "label": "drone", "frame_id": "47", "frame_idx": 47, "interpolated": true, "temporal_based": true}], "48": [{"bbox": [530, 79, 533, 82], "label": "drone", "frame_id": "48", "frame_idx": 48, "interpolated": true, "temporal_based": true}], "49": [{"bbox": [530, 79, 533, 82], "label": "drone", "frame_id": "49", "frame_idx": 49, "interpolated": true, "temporal_based": true}], "51": [{"bbox": [530, 79, 533, 82], "label": "drone", "frame_id": "51", "frame_idx": 51, "interpolated": true, "extrapolated": true}], "52": [{"bbox": [530, 79, 533, 82], "label": "drone", "frame_id": "52", "frame_idx": 52, "interpolated": true, "extrapolated": true}], "53": [{"bbox": [530, 79, 533, 82], "label": "drone", "frame_id": "53", "frame_idx": 53, "interpolated": true, "extrapolated": true}], "54": [{"bbox": [530, 79, 533, 82], "label": "drone", "frame_id": "54", "frame_idx": 54, "interpolated": true, "extrapolated": true}], "55": [{"bbox": [530, 79, 533, 82], "label": "drone", "frame_id": "55", "frame_idx": 55, "interpolated": true, "extrapolated": true}], "56": [{"bbox": [530, 79, 533, 82], "label": "drone", "frame_id": "56", "frame_idx": 56, "interpolated": true, "extrapolated": true}], "57": [{"bbox": [530, 79, 533, 82], "label": "drone", "frame_id": "57", "frame_idx": 57, "interpolated": true, "extrapolated": true}], "58": [{"bbox": [530, 79, 533, 82], "label": "drone", "frame_id": "58", "frame_idx": 58, "interpolated": true, "extrapolated": true}], "59": [{"bbox": [530, 79, 533, 82], "label": "drone", "frame_id": "59", "frame_idx": 59, "interpolated": true, "extrapolated": true}], "61": [{"bbox": [530, 79, 533, 82], "label": "drone", "frame_id": "61", "frame_idx": 61, "interpolated": true, "extrapolated": true}, {"bbox": [530, 79, 533, 82], "label": "drone", "frame_id": "61", "frame_idx": 61, "interpolated": true, "temporal_based": true}], "62": [{"bbox": [530, 79, 533, 82], "label": "drone", "frame_id": "62", "frame_idx": 62, "interpolated": true, "extrapolated": true}, {"bbox": [530, 79, 533, 82], "label": "drone", "frame_id": "62", "frame_idx": 62, "interpolated": true, "temporal_based": true}], "63": [{"bbox": [530, 79, 533, 82], "label": "drone", "frame_id": "63", "frame_idx": 63, "interpolated": true, "extrapolated": true}, {"bbox": [530, 79, 533, 82], "label": "drone", "frame_id": "63", "frame_idx": 63, "interpolated": true, "temporal_based": true}], "64": [{"bbox": [530, 79, 533, 82], "label": "drone", "frame_id": "64", "frame_idx": 64, "interpolated": true, "extrapolated": true}, {"bbox": [530, 79, 533, 82], "label": "drone", "frame_id": "64", "frame_idx": 64, "interpolated": true, "temporal_based": true}], "65": [{"bbox": [530, 79, 533, 82], "label": "drone", "frame_id": "65", "frame_idx": 65, "interpolated": true, "extrapolated": true}, {"bbox": [530, 79, 533, 82], "label": "drone", "frame_id": "65", "frame_idx": 65, "interpolated": true, "temporal_based": true}], "66": [{"bbox": [530, 79, 533, 82], "label": "drone", "frame_id": "66", "frame_idx": 66, "interpolated": true, "extrapolated": true}, {"bbox": [530, 79, 533, 82], "label": "drone", "frame_id": "66", "frame_idx": 66, "interpolated": true, "temporal_based": true}], "67": [{"bbox": [530, 79, 533, 82], "label": "drone", "frame_id": "67", "frame_idx": 67, "interpolated": true, "extrapolated": true}, {"bbox": [530, 79, 533, 82], "label": "drone", "frame_id": "67", "frame_idx": 67, "interpolated": true, "temporal_based": true}], "68": [{"bbox": [530, 79, 533, 82], "label": "drone", "frame_id": "68", "frame_idx": 68, "interpolated": true, "extrapolated": true}, {"bbox": [530, 79, 533, 82], "label": "drone", "frame_id": "68", "frame_idx": 68, "interpolated": true, "temporal_based": true}], "69": [{"bbox": [530, 79, 533, 82], "label": "drone", "frame_id": "69", "frame_idx": 69, "interpolated": true, "extrapolated": true}, {"bbox": [530, 79, 533, 82], "label": "drone", "frame_id": "69", "frame_idx": 69, "interpolated": true, "temporal_based": true}], "71": [{"bbox": [530, 79, 533, 82], "label": "drone", "frame_id": "71", "frame_idx": 71, "interpolated": true, "extrapolated": true}, {"bbox": [530, 79, 533, 82], "label": "drone", "frame_id": "71", "frame_idx": 71, "interpolated": true, "extrapolated": true}], "72": [{"bbox": [530, 79, 533, 82], "label": "drone", "frame_id": "72", "frame_idx": 72, "interpolated": true, "extrapolated": true}, {"bbox": [530, 79, 533, 82], "label": "drone", "frame_id": "72", "frame_idx": 72, "interpolated": true, "extrapolated": true}], "73": [{"bbox": [530, 79, 533, 82], "label": "drone", "frame_id": "73", "frame_idx": 73, "interpolated": true, "extrapolated": true}, {"bbox": [530, 79, 533, 82], "label": "drone", "frame_id": "73", "frame_idx": 73, "interpolated": true, "extrapolated": true}], "74": [{"bbox": [530, 79, 533, 82], "label": "drone", "frame_id": "74", "frame_idx": 74, "interpolated": true, "extrapolated": true}, {"bbox": [530, 79, 533, 82], "label": "drone", "frame_id": "74", "frame_idx": 74, "interpolated": true, "extrapolated": true}]}}}