#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础模型检测器
使用基础Qwen2.5-VL模型（不加载LoRA）进行检测
"""

import os
import sys
import json
import torch
import logging
from pathlib import Path
from typing import List, Dict, Any
from PIL import Image, ImageEnhance
from transformers import Qwen2_5_VLForConditionalGeneration, AutoProcessor
import re
import numpy as np

# 固定使用GPU卡2
os.environ['CUDA_VISIBLE_DEVICES'] = '2'

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BaseModelDetector:
    """基础模型检测器"""
    
    def __init__(self, base_model_path: str):
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        
        logger.info(f"使用设备: {self.device}")
        logger.info(f"基础模型: {base_model_path}")
        
        # 只加载基础模型，不加载LoRA
        self.processor = AutoProcessor.from_pretrained(base_model_path)
        self.model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
            base_model_path,
            torch_dtype=torch.float16,
            device_map="auto",
            trust_remote_code=True,
            low_cpu_mem_usage=True
        )
        
        logger.info("基础模型加载完成")
        
        # 序列信息
        self.sequences = {
            'data19': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
            'data23': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
            'data26': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'}
        }
        
        # 加载类别映射
        with open('dataset/class.json', 'r', encoding='utf-8') as f:
            self.class_map = json.load(f)
    
    def get_image_files(self, seq_dir: Path, seq_info: Dict) -> List[Path]:
        """获取图像文件列表"""
        image_files = list(seq_dir.glob(f"*{seq_info['format']}"))
        
        if seq_info['naming'] == 'complex':
            def enhanced_complex_sort_key(path):
                name = path.name
                patterns = [
                    r'(\d+)\.jpg$',
                    r'_(\d+)\.jpg$',
                    r'-(\d+)\.jpg$',
                    r'(\d+)_',
                    r'(\d+)-',
                    r'(\d+)',
                ]
                
                for pattern in patterns:
                    matches = re.findall(pattern, name)
                    if matches:
                        try:
                            return int(matches[-1])
                        except:
                            continue
                
                return hash(name) % 100000
            
            return sorted(image_files, key=enhanced_complex_sort_key)
        else:
            return sorted(image_files, key=lambda x: int(x.stem))
    
    def enhanced_json_parse(self, text: str) -> List[Dict]:
        """增强JSON解析"""
        try:
            # 清理文本
            clean_text = text.strip()
            
            # 移除markdown标记
            if clean_text.startswith('```json'):
                clean_text = clean_text[7:]
            elif clean_text.startswith('```'):
                clean_text = clean_text[3:]
            if clean_text.endswith('```'):
                clean_text = clean_text[:-3]
            
            clean_text = clean_text.strip()
            
            # 查找JSON数组
            json_start = clean_text.find('[')
            json_end = clean_text.rfind(']')
            
            if json_start != -1 and json_end != -1 and json_end > json_start:
                json_str = clean_text[json_start:json_end+1]
                
                try:
                    detections = json.loads(json_str)
                    if isinstance(detections, list):
                        # 处理不同的bbox字段名
                        normalized_detections = []
                        for det in detections:
                            if isinstance(det, dict):
                                # 处理不同的bbox字段名
                                bbox = None
                                if 'bbox' in det:
                                    bbox = det['bbox']
                                elif 'bbox_2d' in det:
                                    bbox = det['bbox_2d']
                                elif 'bounding_box' in det:
                                    bbox = det['bounding_box']
                                
                                if bbox and isinstance(bbox, list) and len(bbox) == 4:
                                    label = det.get('label', 'drone')
                                    if label == '弱小目标':
                                        label = 'drone'
                                    
                                    normalized_detections.append({
                                        'bbox': bbox,
                                        'label': label.lower()
                                    })
                        
                        return normalized_detections
                except:
                    pass
            
            # 正则表达式提取
            bbox_patterns = [
                r'"bbox(?:_2d)?"\s*:\s*\[(\d+),\s*(\d+),\s*(\d+),\s*(\d+)\]',
                r'\[(\d+),\s*(\d+),\s*(\d+),\s*(\d+)\]'
            ]
            
            for pattern in bbox_patterns:
                matches = re.findall(pattern, text)
                if matches:
                    detections = []
                    for match in matches:
                        try:
                            bbox_coords = [int(match[0]), int(match[1]), int(match[2]), int(match[3])]
                            if (bbox_coords[2] > bbox_coords[0] and bbox_coords[3] > bbox_coords[1] and
                                all(coord >= 0 for coord in bbox_coords)):
                                detections.append({
                                    "bbox": bbox_coords,
                                    "label": "drone"
                                })
                        except:
                            continue
                    
                    if detections:
                        return detections
            
            return []
            
        except Exception as e:
            logger.warning(f"JSON解析失败: {e}")
            return []
    
    def base_model_detection(self, image_path: str, frame_id: str, seq_id: str) -> List[Dict]:
        """基础模型检测"""
        try:
            seq_info = self.sequences[seq_id]
            
            # 简洁有效的提示词
            messages = [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "image",
                            "image": image_path
                        },
                        {
                            "type": "text",
                            "text": f"""检测这张红外图像中的弱小目标。

图像尺寸：{seq_info['size']}
目标特征：小尺寸目标，可能是drone, car, ship, bus, pedestrian, cyclist
检测要求：精确定位每个目标的边界框

输出JSON格式：
[{{"bbox":[x1,y1,x2,y2],"label":"类别"}}]

开始检测："""
                        }
                    ]
                }
            ]
            
            # 应用聊天模板
            text = self.processor.apply_chat_template(
                messages, tokenize=False, add_generation_prompt=True
            )
            
            # 处理图像
            image = Image.open(image_path).convert('RGB')
            
            # 适度的图像增强
            enhancer = ImageEnhance.Contrast(image)
            image = enhancer.enhance(1.3)
            enhancer = ImageEnhance.Sharpness(image)
            image = enhancer.enhance(1.2)
            
            image.thumbnail((640, 512), Image.Resampling.LANCZOS)
            
            # 处理输入
            inputs = self.processor(
                text=[text],
                images=[image],
                padding=True,
                return_tensors="pt"
            )
            
            # 移动到设备
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # 生成检测结果
            with torch.no_grad():
                outputs = self.model.generate(
                    **inputs,
                    max_new_tokens=512,
                    do_sample=False,  # 使用贪婪解码确保稳定性
                    temperature=1.0,
                    pad_token_id=self.processor.tokenizer.eos_token_id,
                )
            
            # 解码输出
            generated_text = self.processor.decode(
                outputs[0][inputs["input_ids"].shape[1]:], 
                skip_special_tokens=True
            )
            
            # 解析检测结果
            detections = self.enhanced_json_parse(generated_text)
            
            # 验证和过滤检测结果
            valid_detections = []
            for detection in detections:
                if isinstance(detection, dict) and 'bbox' in detection and 'label' in detection:
                    bbox = detection['bbox']
                    if isinstance(bbox, list) and len(bbox) == 4:
                        try:
                            # 确保坐标在合理范围内
                            img_w, img_h = seq_info['size']
                            bbox[0] = max(0, min(int(bbox[0]), img_w))
                            bbox[1] = max(0, min(int(bbox[1]), img_h))
                            bbox[2] = max(bbox[0], min(int(bbox[2]), img_w))
                            bbox[3] = max(bbox[1], min(int(bbox[3]), img_h))
                            
                            # 验证尺寸
                            width = bbox[2] - bbox[0]
                            height = bbox[3] - bbox[1]
                            
                            if (width > 0 and height > 0 and 
                                width <= 100 and height <= 100 and
                                width >= 2 and height >= 2):
                                
                                detection['bbox'] = bbox
                                detection['frame_idx'] = int(frame_id) if frame_id.isdigit() else 0
                                detection['frame_id'] = frame_id
                                valid_detections.append(detection)
                        except:
                            continue
            
            logger.info(f"帧 {frame_id} 基础模型检测: {len(valid_detections)} 个目标")
            return valid_detections
                
        except Exception as e:
            logger.error(f"检测帧 {frame_id} 时出错: {e}")
            return []
    
    def load_ground_truth(self, sequence_id: str, frame_indices: List[int]) -> Dict[int, List[Dict]]:
        """加载真实标注"""
        seq_info = self.sequences[sequence_id]
        labels_dir = Path(f"dataset/labels/{sequence_id}")
        ground_truth = {}
        
        seq_dir = Path(f"dataset/images/{sequence_id}")
        image_files = self.get_image_files(seq_dir, seq_info)
        
        for frame_idx in frame_indices:
            if frame_idx < len(image_files):
                img_file = image_files[frame_idx]
                label_file = labels_dir / f"{img_file.stem}.txt"
                
                annotations = []
                if label_file.exists():
                    try:
                        with open(label_file, 'r') as f:
                            for line in f:
                                parts = line.strip().split()
                                if len(parts) >= 5:
                                    class_id = int(parts[0])
                                    x_center = float(parts[1])
                                    y_center = float(parts[2])
                                    width = float(parts[3])
                                    height = float(parts[4])
                                    
                                    # 转换为绝对坐标
                                    img_width, img_height = seq_info['size']
                                    x1 = int((x_center - width/2) * img_width)
                                    y1 = int((y_center - height/2) * img_height)
                                    x2 = int((x_center + width/2) * img_width)
                                    y2 = int((y_center + height/2) * img_height)
                                    
                                    class_name = self.class_map.get(str(class_id), "unknown")
                                    
                                    annotations.append({
                                        "bbox": [x1, y1, x2, y2],
                                        "label": class_name,
                                        "frame_id": img_file.stem,
                                        "frame_idx": frame_idx
                                    })
                    except Exception as e:
                        logger.warning(f"加载标注文件失败 {label_file}: {e}")
                
                ground_truth[frame_idx] = annotations
            else:
                ground_truth[frame_idx] = []
        
        return ground_truth
    
    def calculate_metrics(self, predictions: Dict[int, List[Dict]], ground_truth: Dict[int, List[Dict]], iou_threshold: float = 0.5) -> Dict:
        """计算检测指标"""
        def calculate_iou(box1, box2):
            x1 = max(box1[0], box2[0])
            y1 = max(box1[1], box2[1])
            x2 = min(box1[2], box2[2])
            y2 = min(box1[3], box2[3])
            
            if x2 <= x1 or y2 <= y1:
                return 0.0
            
            intersection = (x2 - x1) * (y2 - y1)
            area1 = (box1[2] - box1[0]) * (box1[3] - box1[1])
            area2 = (box2[2] - box2[0]) * (box2[3] - box2[1])
            union = area1 + area2 - intersection
            
            return intersection / union if union > 0 else 0.0
        
        total_tp = 0
        total_fp = 0
        total_fn = 0
        
        for frame_idx in ground_truth:
            gt_boxes = ground_truth[frame_idx]
            pred_boxes = predictions.get(frame_idx, [])
            
            matched_gt = set()
            
            for pred in pred_boxes:
                if not isinstance(pred, dict) or 'bbox' not in pred:
                    continue
                    
                best_iou = 0
                best_gt_idx = -1
                
                for gt_idx, gt in enumerate(gt_boxes):
                    if gt_idx in matched_gt:
                        continue
                    
                    if not isinstance(gt, dict) or 'bbox' not in gt:
                        continue
                    
                    iou = calculate_iou(pred['bbox'], gt['bbox'])
                    if iou > best_iou and iou >= iou_threshold:
                        best_iou = iou
                        best_gt_idx = gt_idx
                
                if best_gt_idx >= 0:
                    total_tp += 1
                    matched_gt.add(best_gt_idx)
                else:
                    total_fp += 1
            
            total_fn += len(gt_boxes) - len(matched_gt)
        
        precision = total_tp / (total_tp + total_fp) if (total_tp + total_fp) > 0 else 0
        recall = total_tp / (total_tp + total_fn) if (total_tp + total_fn) > 0 else 0
        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
        
        return {
            "precision": precision,
            "recall": recall,
            "f1": f1,
            "tp": total_tp,
            "fp": total_fp,
            "fn": total_fn
        }
    
    def detect_complex_sequence(self, sequence_id: str, test_ratio: float = 0.05) -> Dict:
        """检测复杂序列"""
        logger.info(f"开始基础模型检测序列 {sequence_id}...")
        
        seq_info = self.sequences[sequence_id]
        seq_dir = Path(f"dataset/images/{sequence_id}")
        
        if not seq_dir.exists():
            logger.error(f"序列目录不存在: {seq_dir}")
            return {}
        
        image_files = self.get_image_files(seq_dir, seq_info)
        if not image_files:
            logger.error(f"序列 {sequence_id} 中没有找到图像文件")
            return {}
        
        # 计算测试帧数
        total_frames = len(image_files)
        test_frames = max(int(total_frames * test_ratio), 50)
        test_frames = min(test_frames, total_frames)
        
        logger.info(f"序列 {sequence_id}: 总帧数 {total_frames}, 测试帧数 {test_frames}")
        
        # 每隔10帧抽取
        frame_interval = 10
        key_frame_indices = list(range(0, test_frames, frame_interval))
        all_frame_indices = list(range(test_frames))
        
        logger.info(f"关键帧数: {len(key_frame_indices)} (每隔{frame_interval}帧)")
        
        # 基础模型检测
        logger.info(f"开始基础模型检测 ({len(key_frame_indices)} 关键帧)...")
        key_frame_detections = {}
        
        for frame_idx in key_frame_indices:
            if frame_idx < len(image_files):
                frame_file = image_files[frame_idx]
                frame_id = frame_file.stem
                
                detections = self.base_model_detection(str(frame_file), frame_id, sequence_id)
                key_frame_detections[frame_idx] = detections
        
        # 简单插值
        all_detections = {}
        for frame_idx in all_frame_indices:
            if frame_idx in key_frame_detections:
                all_detections[frame_idx] = key_frame_detections[frame_idx]
            else:
                # 复制最近的检测结果
                nearest_key = min(key_frame_detections.keys(), 
                                key=lambda x: abs(x - frame_idx), 
                                default=None)
                if nearest_key is not None:
                    all_detections[frame_idx] = [
                        {**det, "frame_idx": frame_idx, "frame_id": str(frame_idx), "interpolated": True}
                        for det in key_frame_detections[nearest_key]
                    ]
                else:
                    all_detections[frame_idx] = []
        
        # 加载真实标注
        logger.info("加载真实标注...")
        ground_truth = self.load_ground_truth(sequence_id, all_frame_indices)
        
        # 计算指标
        logger.info("计算检测指标...")
        metrics = self.calculate_metrics(all_detections, ground_truth)
        
        # 统计结果
        total_detections = sum(len(dets) for dets in all_detections.values())
        direct_detections = sum(len(dets) for frame_idx, dets in all_detections.items() 
                               if frame_idx in key_frame_detections)
        interpolated_detections = total_detections - direct_detections
        
        result = {
            "sequence_id": sequence_id,
            "total_frames": test_frames,
            "key_frames": len(key_frame_indices),
            "total_detections": total_detections,
            "direct_detections": direct_detections,
            "interpolated_detections": interpolated_detections,
            "metrics": metrics,
            "detections": all_detections
        }
        
        logger.info(f"序列 {sequence_id} 基础模型检测完成:")
        logger.info(f"  总检测数: {total_detections}")
        logger.info(f"  直接检测: {direct_detections}")
        logger.info(f"  插值检测: {interpolated_detections}")
        logger.info(f"  精确度: {metrics['precision']:.3f}")
        logger.info(f"  召回率: {metrics['recall']:.3f}")
        logger.info(f"  F1分数: {metrics['f1']:.3f}")
        
        return result

def main():
    """主函数"""
    print("=== 基础模型复杂序列检测系统 ===")
    print("🎯 目标序列: data19, data23, data26")
    print("📊 检测策略: 基础模型检测（不使用LoRA）")
    print("🔧 技术: 基础Qwen2.5-VL + 增强JSON解析")
    
    # 初始化检测器
    base_model_path = "qwen2.5-vl-7b"
    print(f"\n初始化基础模型检测器...")
    detector = BaseModelDetector(base_model_path)
    
    # 检测复杂序列
    sequences = ['data19', 'data23', 'data26']
    all_results = {}
    
    for sequence_id in sequences:
        print(f"\n{'='*60}")
        print(f"基础模型检测序列: {sequence_id}")
        print(f"{'='*60}")
        
        result = detector.detect_complex_sequence(sequence_id)
        all_results[sequence_id] = result
    
    # 保存结果
    os.makedirs("results", exist_ok=True)
    with open("results/base_model_complex_detection_results.json", 'w', encoding='utf-8') as f:
        json.dump(all_results, f, ensure_ascii=False, indent=2)
    
    print("\n🎉 基础模型复杂序列检测完成！")
    print("📊 检测结果已保存到 results/base_model_complex_detection_results.json")
    
    # 统计结果
    print("\n📈 基础模型检测统计:")
    for seq_id, result in all_results.items():
        if result:
            metrics = result['metrics']
            print(f"   {seq_id}: {result['total_detections']} 个检测 "
                  f"(直接: {result['direct_detections']}, 插值: {result['interpolated_detections']}) "
                  f"P: {metrics['precision']:.3f}, R: {metrics['recall']:.3f}, F1: {metrics['f1']:.3f}")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
