#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的深度微调训练器 - 使用所有8个序列的完整数据进行深度微调
"""

import os
import sys
import json
import torch
import logging
from pathlib import Path
from typing import List, Dict, Any
from PIL import Image
from torch.utils.data import Dataset, DataLoader
from transformers import (
    Qwen2_5_VLForConditionalGeneration,
    AutoProcessor,
    TrainingArguments,
    Trainer,
)
from peft import LoraConfig, get_peft_model, TaskType
from qwen_vl_utils import process_vision_info
import numpy as np

# 固定使用GPU卡2
os.environ['CUDA_VISIBLE_DEVICES'] = '2'

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def generate_complete_training_data():
    """生成完整的训练数据 - 所有8个序列，每隔10帧抽取到最后"""
    from video_sequence_trainer import generate_all_sequences_training_data
    
    logger.info("生成完整的训练数据...")
    
    # 生成完整训练数据，不限制帧数
    all_training_data = generate_all_sequences_training_data(max_frames=None)
    
    if not all_training_data:
        logger.error("完整训练数据生成失败")
        return False
    
    # 保存完整训练数据
    output_path = "data/complete_training_data.json"
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(all_training_data, f, ensure_ascii=False, indent=2)
    
    logger.info(f"完整训练数据保存到: {output_path}")
    logger.info(f"总样本数: {len(all_training_data)}")
    
    return True

class CompleteDataset(Dataset):
    """完整数据集 - 处理长视频序列"""
    
    def __init__(self, data_path: str, processor):
        self.processor = processor
        with open(data_path, 'r', encoding='utf-8') as f:
            self.data = json.load(f)
        logger.info(f"加载了 {len(self.data)} 个完整训练样本")
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        sample = self.data[idx]
        
        # 加载视频帧，限制最大帧数以控制内存
        frame_infos = sample['sequence_info']['video_frames']
        max_frames_per_sample = 20  # 每个样本最多20帧
        
        if len(frame_infos) > max_frames_per_sample:
            # 均匀采样
            step = len(frame_infos) // max_frames_per_sample
            frame_infos = frame_infos[::step][:max_frames_per_sample]
        
        video_frames = []
        
        for frame_info in frame_infos:
            try:
                image = Image.open(frame_info['file_path']).convert('RGB')
                # 统一调整图像大小
                image = image.resize((336, 336))  # Qwen2.5-VL推荐尺寸
                video_frames.append(image)
            except Exception as e:
                logger.warning(f"无法加载图像 {frame_info['file_path']}: {e}")
                continue
        
        if not video_frames:
            return None
        
        # 构建完整的消息
        conversations = sample['conversations']
        messages = []
        
        for conv in conversations:
            if conv['from'] == 'human':
                # 使用完整的提示词
                message = {
                    "role": "user",
                    "content": [
                        {"type": "video", "video": video_frames},
                        {"type": "text", "text": conv['value']}
                    ]
                }
            else:
                message = {
                    "role": "assistant", 
                    "content": conv['value']
                }
            messages.append(message)
        
        return {
            'messages': messages,
            'sequence_info': sample['sequence_info']
        }

def complete_collate_fn(batch, processor):
    """完整数据整理函数"""
    # 过滤None样本
    batch = [item for item in batch if item is not None]
    if not batch:
        return None

    # 只处理第一个样本，避免内存问题
    try:
        example = batch[0]
        messages = example["messages"]

        # 应用聊天模板
        text = processor.apply_chat_template(
            messages, tokenize=False, add_generation_prompt=True
        )

        # 限制文本长度
        if len(text) > 4096:
            text = text[:4096]

        # 处理视觉信息
        image_inputs, video_inputs = process_vision_info(messages)

        # 处理输入
        inputs = processor(
            text=[text],
            images=image_inputs,
            videos=video_inputs,
            padding=True,
            return_tensors="pt",
            max_length=1024,  # 增加最大长度
            truncation=True
        )

        # 检查输入是否有效
        if inputs["input_ids"].numel() == 0:
            logger.warning("生成的input_ids为空，跳过此样本")
            return None

        # 为训练添加labels
        inputs["labels"] = inputs["input_ids"].clone()

        return inputs

    except Exception as e:
        logger.warning(f"处理样本时出错: {e}")
        return None

class CompleteTrainer:
    """完整深度训练器"""
    
    def __init__(self, model_path: str):
        self.model_path = model_path
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        
        logger.info(f"使用设备: {self.device}")
        logger.info(f"模型路径: {model_path}")
        
        # 加载模型和处理器
        self.processor = AutoProcessor.from_pretrained(model_path)
        self.model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
            model_path,
            torch_dtype=torch.float16,
            device_map="auto",
            trust_remote_code=True,
            low_cpu_mem_usage=True,
            use_cache=False
        )
        
        logger.info("模型和处理器加载完成")
    
    def setup_lora(self):
        """设置完整的LoRA配置"""
        config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            r=32,  # 增加rank
            lora_alpha=64,  # 增加alpha
            target_modules=["q_proj", "v_proj", "k_proj", "o_proj"],  # 更多目标模块
            lora_dropout=0.05,
        )
        
        self.model = get_peft_model(self.model, config)
        self.model.print_trainable_parameters()
        logger.info("完整LoRA配置完成")
    
    def train(self, train_data_path: str, output_dir: str):
        """开始完整深度训练"""
        logger.info("开始完整深度训练...")
        
        # 创建数据集
        dataset = CompleteDataset(train_data_path, self.processor)
        
        # 创建数据加载器
        def collate_wrapper(batch):
            return complete_collate_fn(batch, self.processor)
        
        # 完整训练参数
        args = TrainingArguments(
            output_dir=output_dir,
            num_train_epochs=5,  # 增加训练轮数
            per_device_train_batch_size=1,
            gradient_accumulation_steps=8,  # 增加梯度累积
            learning_rate=2e-5,  # 适中的学习率
            fp16=True,
            save_steps=20,
            logging_steps=5,
            save_total_limit=3,
            remove_unused_columns=False,
            dataloader_drop_last=True,
            dataloader_num_workers=0,  # 避免多进程问题
            skip_memory_metrics=True,  # 跳过内存指标
            max_grad_norm=1.0,
            warmup_steps=20,  # 增加预热步数
            lr_scheduler_type="cosine",  # 使用余弦学习率调度
            weight_decay=0.01,  # 添加权重衰减
        )
        
        # 创建训练器
        trainer = Trainer(
            model=self.model,
            args=args,
            train_dataset=dataset,
            data_collator=collate_wrapper,
        )
        
        # 开始训练
        logger.info("开始深度微调训练，预计时间：30-60分钟...")
        trainer.train()
        
        # 保存模型
        trainer.save_model()
        self.processor.save_pretrained(output_dir)
        
        logger.info(f"完整深度训练完成，模型保存到: {output_dir}")
        return True

def main():
    """主函数"""
    print("=== 完整深度视频序列微调训练系统 ===")
    print("🎯 目标: 使用所有8个序列的完整数据进行深度微调")
    print("📊 策略: 每隔10帧抽取到最后，总计732帧")
    print("🔧 技术: 深度LoRA + 长时间训练")
    print("⏱️  预计时间: 30-60分钟")
    
    # 第1步：生成完整训练数据
    print("\n第1步：生成完整训练数据...")
    if not generate_complete_training_data():
        print("❌ 完整数据生成失败")
        return False
    
    # 第2步：初始化训练器
    print("\n第2步：初始化完整训练器...")
    model_path = "qwen2.5-vl-7b"
    trainer = CompleteTrainer(model_path)
    
    # 第3步：设置LoRA
    print("\n第3步：设置完整LoRA配置...")
    trainer.setup_lora()
    
    # 第4步：开始训练
    print("\n第4步：开始完整深度微调训练...")
    output_dir = "output/complete_video_sequence_lora"
    success = trainer.train("data/complete_training_data.json", output_dir)
    
    if success:
        print("\n🎉 完整深度微调训练完成！")
        print(f"📁 模型保存到: {output_dir}")
        print("📋 下一步: 使用完整模型进行检测")
    else:
        print("\n❌ 完整训练失败！")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
