#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
正确的LoRA微调训练器
修复之前的训练问题，使用正确的数据划分
"""

import os
import sys
import json
import torch
import logging
from pathlib import Path
from typing import List, Dict
from PIL import Image, ImageEnhance
import random

from transformers import (
    Qwen2_5_VLForConditionalGeneration,
    AutoProcessor,
    TrainingArguments,
    Trainer,
    DataCollatorWithPadding
)
from peft import LoraConfig, get_peft_model, TaskType
from torch.utils.data import Dataset

# 添加当前目录到路径
sys.path.append('src')
from correct_data_split_trainer import CorrectVLDataset

# 固定使用GPU卡2
os.environ['CUDA_VISIBLE_DEVICES'] = '2'

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CorrectLoRATrainer:
    """正确的LoRA微调训练器"""
    
    def __init__(self, base_model_path: str):
        self.base_model_path = base_model_path
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        
        logger.info(f"使用设备: {self.device}")
        logger.info(f"基础模型: {base_model_path}")
    
    def load_model_and_processor(self):
        """加载模型和处理器"""
        logger.info("加载基础模型和处理器...")
        
        # 加载处理器
        self.processor = AutoProcessor.from_pretrained(self.base_model_path)
        
        # 加载基础模型
        self.model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
            self.base_model_path,
            torch_dtype=torch.float16,
            device_map="auto",
            trust_remote_code=True,
            low_cpu_mem_usage=True
        )
        
        logger.info("基础模型加载完成")
    
    def setup_lora_config(self):
        """设置LoRA配置"""
        logger.info("设置LoRA配置...")
        
        # 更保守的LoRA配置，避免过拟合
        lora_config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            inference_mode=False,
            r=16,  # 降低rank避免过拟合
            lora_alpha=16,  # 降低alpha
            lora_dropout=0.1,  # 增加dropout
            target_modules=[
                "q_proj", "k_proj", "v_proj", "o_proj",
                "gate_proj", "up_proj", "down_proj"
            ],
            bias="none",
        )
        
        # 应用LoRA
        self.model = get_peft_model(self.model, lora_config)
        
        # 打印可训练参数
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        total_params = sum(p.numel() for p in self.model.parameters())
        
        logger.info(f"可训练参数: {trainable_params:,}")
        logger.info(f"总参数: {total_params:,}")
        logger.info(f"可训练比例: {100 * trainable_params / total_params:.2f}%")
    
    def load_training_data(self):
        """加载训练数据"""
        logger.info("加载训练数据...")
        
        # 检查数据文件是否存在
        train_file = "data/correct_train_data.json"
        test_file = "data/correct_test_data.json"
        
        if not os.path.exists(train_file) or not os.path.exists(test_file):
            logger.error("训练数据不存在，请先运行 correct_data_split_trainer.py")
            return None, None
        
        # 加载训练数据
        with open(train_file, 'r', encoding='utf-8') as f:
            train_data = json.load(f)
        
        with open(test_file, 'r', encoding='utf-8') as f:
            test_data = json.load(f)
        
        logger.info(f"训练数据: {len(train_data)} 样本")
        logger.info(f"测试数据: {len(test_data)} 样本")
        
        return train_data, test_data
    
    def create_datasets(self, train_data: List[Dict], test_data: List[Dict]):
        """创建数据集"""
        logger.info("创建数据集...")
        
        train_dataset = CorrectVLDataset(train_data, self.processor, is_training=True)
        test_dataset = CorrectVLDataset(test_data, self.processor, is_training=False)
        
        logger.info(f"训练数据集: {len(train_dataset)} 样本")
        logger.info(f"测试数据集: {len(test_dataset)} 样本")
        
        return train_dataset, test_dataset
    
    def setup_training_arguments(self, output_dir: str):
        """设置训练参数"""
        logger.info("设置训练参数...")
        
        training_args = TrainingArguments(
            output_dir=output_dir,
            num_train_epochs=3,  # 减少epoch避免过拟合
            per_device_train_batch_size=1,
            per_device_eval_batch_size=1,
            gradient_accumulation_steps=4,
            learning_rate=1e-4,  # 降低学习率
            weight_decay=0.01,
            logging_steps=10,
            eval_steps=50,
            save_steps=100,
            eval_strategy="steps",
            save_strategy="steps",
            load_best_model_at_end=True,
            metric_for_best_model="eval_loss",
            greater_is_better=False,
            warmup_steps=50,
            lr_scheduler_type="cosine",
            fp16=True,
            dataloader_pin_memory=False,
            remove_unused_columns=False,
            report_to=None,
            save_total_limit=2,
        )
        
        return training_args
    
    def train_model(self, output_dir: str = "output/correct_weak_target_lora", resume_from_checkpoint: str = None):
        """训练模型"""
        logger.info("开始正确的LoRA微调训练...")

        # 加载模型和处理器
        self.load_model_and_processor()

        # 设置LoRA
        self.setup_lora_config()

        # 加载数据
        train_data, test_data = self.load_training_data()
        if train_data is None:
            return False

        # 创建数据集
        train_dataset, test_dataset = self.create_datasets(train_data, test_data)

        # 设置训练参数
        training_args = self.setup_training_arguments(output_dir)

        # 创建训练器
        trainer = Trainer(
            model=self.model,
            args=training_args,
            train_dataset=train_dataset,
            eval_dataset=test_dataset,
            tokenizer=self.processor.tokenizer,
        )

        # 开始训练（支持从checkpoint继续）
        if resume_from_checkpoint:
            logger.info(f"从checkpoint继续训练: {resume_from_checkpoint}")
            trainer.train(resume_from_checkpoint=resume_from_checkpoint)
        else:
            logger.info("开始新的训练...")
            trainer.train()

        # 保存模型
        logger.info("保存模型...")
        trainer.save_model()

        # 保存处理器
        self.processor.save_pretrained(output_dir)

        logger.info(f"模型已保存到: {output_dir}")

        return True

def main():
    """主函数"""
    print("=== 正确的LoRA微调训练系统 ===")
    print("🎯 特点: 修复过拟合问题")
    print("📊 策略: 保守的LoRA配置")
    print("🔧 技术: 正确的训练流程")

    # 检查训练数据
    train_file = "data/correct_train_data.json"
    test_file = "data/correct_test_data.json"

    if not os.path.exists(train_file) or not os.path.exists(test_file):
        print("\n❌ 训练数据不存在")
        print("请先运行: python src/correct_data_split_trainer.py")
        return False

    print(f"\n✅ 找到训练数据:")
    print(f"   训练数据: {train_file}")
    print(f"   测试数据: {test_file}")

    # 检查是否有checkpoint可以继续
    output_dir = "output/correct_weak_target_lora"
    checkpoint_dir = None

    if os.path.exists(output_dir):
        checkpoints = [d for d in os.listdir(output_dir) if d.startswith('checkpoint-')]
        if checkpoints:
            # 找到最新的checkpoint
            latest_checkpoint = max(checkpoints, key=lambda x: int(x.split('-')[1]))
            checkpoint_dir = os.path.join(output_dir, latest_checkpoint)
            print(f"\n🔄 找到checkpoint: {checkpoint_dir}")
            print(f"   将从此处继续训练")
        else:
            print(f"\n🆕 开始新的训练")
    else:
        print(f"\n🆕 开始新的训练")

    # 创建训练器
    base_model_path = "qwen2.5-vl-7b"
    trainer = CorrectLoRATrainer(base_model_path)

    # 开始训练
    success = trainer.train_model(output_dir, resume_from_checkpoint=checkpoint_dir)

    if success:
        print(f"\n🎉 正确的LoRA微调训练完成！")
        print(f"📁 模型保存到: {output_dir}")
        print(f"💡 这是修复后的模型，应该不会输出乱码")
    else:
        print(f"\n❌ 训练失败！")

    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
