#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
正确的视频序列检测器 - 真正的视频序列检测
"""

import os
import sys
import json
import torch
import logging
from pathlib import Path
from typing import List, Dict, Any
from PIL import Image
from transformers import Qwen2_5_VLForConditionalGeneration, AutoProcessor
from peft import PeftModel
from qwen_vl_utils import process_vision_info
import re

# 固定使用GPU卡2
os.environ['CUDA_VISIBLE_DEVICES'] = '2'

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CorrectVideoSequenceDetector:
    """正确的视频序列检测器"""
    
    def __init__(self, base_model_path: str, lora_model_path: str):
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        
        logger.info(f"使用设备: {self.device}")
        logger.info(f"基础模型: {base_model_path}")
        logger.info(f"LoRA模型: {lora_model_path}")
        
        # 加载处理器
        self.processor = AutoProcessor.from_pretrained(base_model_path)
        
        # 加载基础模型
        self.model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
            base_model_path,
            torch_dtype=torch.float16,
            device_map="auto",
            trust_remote_code=True,
            low_cpu_mem_usage=True
        )
        
        # 加载LoRA微调模型
        logger.info("加载LoRA微调模型...")
        self.model = PeftModel.from_pretrained(self.model, lora_model_path)
        logger.info("LoRA模型加载完成")
        
        # 序列信息
        self.sequences = {
            'data01': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data02': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data04': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data06': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data07': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data19': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
            'data23': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
            'data26': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'}
        }
    
    def get_image_files(self, seq_dir: Path, seq_info: Dict) -> List[Path]:
        """获取图像文件列表"""
        if seq_info['naming'] == 'simple':
            # 简单命名：0.bmp, 1.bmp, ...
            image_files = list(seq_dir.glob(f"*{seq_info['format']}"))
            return sorted(image_files, key=lambda x: int(x.stem))
        else:
            # 复杂命名：img00001.jpg, img00002.jpg, ...
            image_files = list(seq_dir.glob(f"*{seq_info['format']}"))
            def complex_sort_key(path):
                numbers = re.findall(r'(\d+)', path.name)
                return int(numbers[-1]) if numbers else 0
            return sorted(image_files, key=complex_sort_key)
    
    def detect_video_sequence(self, video_frames: List[Image.Image], sequence_id: str) -> List[Dict]:
        """检测视频序列"""
        try:
            # 构建提示词
            prompt = f"这是一个包含{len(video_frames)}帧的红外视频序列（从{sequence_id}每隔10帧抽取组成）。请仔细检测序列中所有帧的弱小目标。目标类别包括：drone, car, ship, bus, pedestrian, cyclist。请以JSON数组格式输出检测结果，每个目标必须包含精确的边界框坐标、类别标签和帧ID。格式要求：[{{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}}]"
            
            # 构建消息
            messages = [
                {
                    "role": "user",
                    "content": [
                        {"type": "video", "video": video_frames},
                        {"type": "text", "text": prompt}
                    ]
                }
            ]
            
            # 应用聊天模板
            text = self.processor.apply_chat_template(
                messages, tokenize=False, add_generation_prompt=True
            )
            
            # 处理视觉信息
            image_inputs, video_inputs = process_vision_info(messages)
            
            # 处理输入
            inputs = self.processor(
                text=[text],
                images=image_inputs,
                videos=video_inputs,
                padding=True,
                return_tensors="pt"
            )
            
            # 移动到设备
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # 生成检测结果
            with torch.no_grad():
                outputs = self.model.generate(
                    **inputs,
                    max_new_tokens=2048,
                    do_sample=False,
                    temperature=0.1
                )
            
            # 解码输出
            generated_text = self.processor.decode(
                outputs[0][inputs["input_ids"].shape[1]:], 
                skip_special_tokens=True
            )
            
            # 解析JSON结果 - 处理markdown格式
            try:
                # 移除markdown代码块标记
                clean_text = generated_text.strip()
                if clean_text.startswith('```json'):
                    clean_text = clean_text[7:]
                if clean_text.endswith('```'):
                    clean_text = clean_text[:-3]
                clean_text = clean_text.strip()

                detections = json.loads(clean_text)
                if isinstance(detections, list):
                    logger.info(f"成功解析检测结果: {len(detections)} 个检测")
                    return detections
                else:
                    return []
            except json.JSONDecodeError as e:
                logger.warning(f"无法解析检测结果: {generated_text[:200]}...")
                logger.warning(f"JSON解析错误: {e}")
                return []
                
        except Exception as e:
            logger.error(f"检测视频序列时出错: {e}")
            return []
    
    def detect_sequence_with_video_segments(self, sequence_id: str, test_ratio: float = 0.05) -> List[Dict]:
        """使用视频片段检测序列"""
        logger.info(f"开始检测序列 {sequence_id}...")
        
        seq_info = self.sequences[sequence_id]
        seq_dir = Path(f"dataset/images/{sequence_id}")
        
        if not seq_dir.exists():
            logger.error(f"序列目录不存在: {seq_dir}")
            return []
        
        # 获取图像文件
        image_files = self.get_image_files(seq_dir, seq_info)
        if not image_files:
            logger.error(f"序列 {sequence_id} 中没有找到图像文件")
            return []
        
        # 计算测试帧数（前5%）
        total_frames = len(image_files)
        test_frames = max(int(total_frames * test_ratio), 50)
        test_frames = min(test_frames, total_frames)
        test_image_files = image_files[:test_frames]
        
        logger.info(f"序列 {sequence_id}: 总帧数 {total_frames}, 测试帧数 {test_frames}")
        
        # 每隔10帧抽取，组成视频序列
        frame_interval = 10
        sampled_indices = list(range(0, test_frames, frame_interval))
        
        logger.info(f"抽取帧数: {len(sampled_indices)} (每隔{frame_interval}帧)")
        
        # 将抽取的帧分成视频片段进行检测
        video_segment_length = 8  # 每个视频片段8帧
        all_detections = []
        
        for start_idx in range(0, len(sampled_indices), video_segment_length):
            end_idx = min(start_idx + video_segment_length, len(sampled_indices))
            segment_indices = sampled_indices[start_idx:end_idx]
            
            if len(segment_indices) < 4:  # 至少4帧才组成视频
                continue
            
            # 加载视频片段
            video_frames = []
            for idx in segment_indices:
                try:
                    image = Image.open(test_image_files[idx]).convert('RGB')
                    image = image.resize((336, 336))
                    video_frames.append(image)
                except Exception as e:
                    logger.warning(f"无法加载图像 {test_image_files[idx]}: {e}")
            
            if len(video_frames) >= 4:
                # 检测视频片段
                segment_detections = self.detect_video_sequence(video_frames, sequence_id)
                
                # 调整帧ID到原始索引
                for detection in segment_detections:
                    try:
                        frame_id = int(detection.get('frame_id', 0))
                        if frame_id < len(segment_indices):
                            original_frame_idx = segment_indices[frame_id]
                            detection['frame_id'] = str(original_frame_idx)
                            detection['frame_index'] = original_frame_idx
                            detection['sequence_id'] = sequence_id
                            detection['file_path'] = str(test_image_files[original_frame_idx])
                            detection['interpolated'] = False
                    except:
                        continue
                
                all_detections.extend(segment_detections)
        
        logger.info(f"序列 {sequence_id} 检测完成，总检测数: {len(all_detections)}")
        return all_detections

def main():
    """主函数"""
    print("=== 正确的视频序列检测与评估系统 ===")
    print("🎯 目标序列: data01, data02, data04, data06, data07, data19, data23, data26")
    print("📊 检测策略: 每隔10帧抽取组成视频序列进行检测")
    print("🎬 检测方式: 真正的视频序列检测 (不是单帧+插值)")
    print("📈 测试范围: 每个序列前5%数据")
    print("🤖 模型: 使用视频序列微调后的Qwen2.5-VL模型")
    
    # 检查模型是否存在
    base_model_path = "qwen2.5-vl-7b"
    lora_model_path = "output/correct_video_sequence_lora"
    
    if not os.path.exists(lora_model_path):
        print(f"\n❌ 未找到视频序列微调后的模型: {lora_model_path}")
        print("请先运行 python train.py 进行视频序列微调训练")
        return False
    
    print(f"\n✅ 找到视频序列微调后的模型: {lora_model_path}")
    
    # 初始化检测器
    print("\n初始化视频序列检测器...")
    detector = CorrectVideoSequenceDetector(base_model_path, lora_model_path)
    
    # 开始检测
    print("\n开始视频序列检测...")
    
    sequences = ['data01', 'data02', 'data04', 'data06', 'data07', 'data19', 'data23', 'data26']
    all_detections = {}
    
    for sequence_id in sequences:
        print(f"\n{'='*60}")
        print(f"检测序列: {sequence_id}")
        print(f"{'='*60}")
        
        detections = detector.detect_sequence_with_video_segments(sequence_id)
        all_detections[sequence_id] = detections
        
        print(f"序列 {sequence_id} 检测完成: {len(detections)} 个检测结果")
    
    # 保存检测结果
    import json
    os.makedirs("results", exist_ok=True)
    with open("results/correct_video_detection_results.json", 'w', encoding='utf-8') as f:
        json.dump(all_detections, f, ensure_ascii=False, indent=2)
    
    print("\n🎉 正确的视频序列检测完成！")
    print("📊 检测结果已保存到 results/correct_video_detection_results.json")
    
    # 统计检测结果
    print("\n📈 检测统计:")
    total_detections = 0
    for seq_id, detections in all_detections.items():
        count = len(detections)
        total_detections += count
        print(f"   {seq_id}: {count} 个检测")
    
    print(f"   总检测数: {total_detections}")
    print("\n💡 这是使用真正视频序列微调模型的检测结果")
    print("包括：视频序列训练 + 视频序列检测")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
