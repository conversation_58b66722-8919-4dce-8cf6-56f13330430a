#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
正确的视频序列微调训练器 - 真正的视频序列训练
"""

import os
import sys
import json
import torch
import logging
from pathlib import Path
from typing import List, Dict, Any
from PIL import Image
from torch.utils.data import Dataset, DataLoader
from transformers import (
    Qwen2_5_VLForConditionalGeneration,
    AutoProcessor,
    TrainingArguments,
    Trainer,
)
from peft import LoraConfig, get_peft_model, TaskType
from qwen_vl_utils import process_vision_info
import numpy as np

# 固定使用GPU卡2
os.environ['CUDA_VISIBLE_DEVICES'] = '2'

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_simple_naming_files(images_dir, format_ext):
    """获取简单命名文件"""
    image_files = list(images_dir.glob(f"*{format_ext}"))
    return sorted(image_files, key=lambda x: int(x.stem))

def get_complex_naming_files(images_dir, format_ext):
    """获取复杂命名文件"""
    image_files = list(images_dir.glob(f"*{format_ext}"))
    import re
    def complex_sort_key(path):
        numbers = re.findall(r'(\d+)', path.name)
        return int(numbers[-1]) if numbers else 0
    return sorted(image_files, key=complex_sort_key)

def load_frame_annotation(seq_id, frame_id, size):
    """加载帧标注"""
    labels_dir = Path(f"dataset/labels/{seq_id}")
    label_file = labels_dir / f"{frame_id}.txt"

    if not label_file.exists():
        return []

    # 加载类别映射
    with open('dataset/class.json', 'r', encoding='utf-8') as f:
        class_map = json.load(f)

    annotations = []
    try:
        with open(label_file, 'r') as f:
            for line in f:
                parts = line.strip().split()
                if len(parts) >= 5:
                    class_id = int(parts[0])
                    x_center = float(parts[1])
                    y_center = float(parts[2])
                    width = float(parts[3])
                    height = float(parts[4])

                    # 转换为绝对坐标
                    img_width, img_height = size
                    x1 = (x_center - width/2) * img_width
                    y1 = (y_center - height/2) * img_height
                    x2 = (x_center + width/2) * img_width
                    y2 = (y_center + height/2) * img_height

                    # 获取类别名称
                    class_name = class_map.get(str(class_id), "unknown")

                    annotations.append({
                        "bbox": [x1, y1, x2, y2],
                        "label": class_name,
                        "frame_id": frame_id
                    })
    except Exception as e:
        logger.warning(f"加载标注文件失败 {label_file}: {e}")

    return annotations

def generate_correct_video_training_data():
    """生成正确的视频序列训练数据"""
    logger.info("生成正确的视频序列训练数据...")

    # 序列信息
    sequences = {
        'data01': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
        'data02': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
        'data04': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
        'data06': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
        'data07': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
        'data19': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
        'data23': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
        'data26': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'}
    }
    
    all_video_samples = []

    for seq_id, seq_info in sequences.items():
        print(f"\n处理序列 {seq_id}...")

        # 获取序列信息
        images_dir = Path(f"dataset/images/{seq_id}")

        # 获取图像文件
        if seq_info['naming'] == 'simple':
            image_files = get_simple_naming_files(images_dir, seq_info['format'])
        else:
            image_files = get_complex_naming_files(images_dir, seq_info['format'])
        
        total_frames = len(image_files)
        print(f"  总帧数: {total_frames}")
        
        # 每隔10帧抽取1帧，组成视频序列
        frame_interval = 10
        sampled_indices = list(range(0, total_frames, frame_interval))
        
        print(f"  抽取帧数: {len(sampled_indices)} (每隔{frame_interval}帧)")
        
        # 将抽取的帧分成多个视频片段进行训练
        video_segment_length = 16  # 每个视频片段16帧
        
        for start_idx in range(0, len(sampled_indices), video_segment_length):
            end_idx = min(start_idx + video_segment_length, len(sampled_indices))
            segment_indices = sampled_indices[start_idx:end_idx]
            
            if len(segment_indices) < 4:  # 至少4帧才组成视频
                continue
            
            # 构建视频序列
            video_frames = []
            video_annotations = []
            
            for frame_idx in segment_indices:
                frame_file = image_files[frame_idx]
                frame_id = frame_file.stem
                
                # 加载该帧的标注
                annotations = load_frame_annotation(seq_id, frame_id, seq_info['size'])
                
                video_frames.append({
                    'frame_idx': frame_idx,
                    'frame_id': frame_id,
                    'file_path': str(frame_file)
                })
                
                # 为每个标注添加帧信息
                for ann in annotations:
                    ann['frame_id'] = frame_id
                    ann['frame_idx'] = frame_idx
                
                video_annotations.extend(annotations)
            
            if len(video_frames) >= 4:  # 确保有足够的帧
                # 创建视频序列训练样本
                video_sample = {
                    "conversations": [
                        {
                            "from": "human",
                            "value": f"这是一个包含{len(video_frames)}帧的红外视频序列（从{seq_id}每隔{frame_interval}帧抽取组成），图像尺寸为{seq_info['size']}。请仔细检测序列中所有帧的弱小目标。目标类别包括：drone, car, ship, bus, pedestrian, cyclist。请以JSON数组格式输出检测结果，每个目标必须包含精确的边界框坐标、类别标签和帧ID。格式要求：[{{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}}]"
                        },
                        {
                            "from": "gpt",
                            "value": json.dumps(video_annotations)
                        }
                    ],
                    "video_info": {
                        "sequence_id": seq_id,
                        "segment_start": start_idx,
                        "segment_end": end_idx,
                        "video_frames": video_frames,
                        "size": seq_info['size'],
                        "format": seq_info['format'],
                        "frame_count": len(video_frames)
                    }
                }
                
                all_video_samples.append(video_sample)
        
        print(f"  ✅ 生成视频片段: {len(all_video_samples)} 个")
    
    # 保存视频序列训练数据
    output_path = "data/correct_video_training_data.json"
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(all_video_samples, f, ensure_ascii=False, indent=2)
    
    logger.info(f"正确视频序列训练数据保存到: {output_path}")
    logger.info(f"总视频片段数: {len(all_video_samples)}")
    
    return True

class CorrectVideoDataset(Dataset):
    """正确的视频序列数据集"""
    
    def __init__(self, data_path: str, processor):
        self.processor = processor
        with open(data_path, 'r', encoding='utf-8') as f:
            self.data = json.load(f)
        logger.info(f"加载了 {len(self.data)} 个视频序列训练样本")
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        sample = self.data[idx]
        
        # 加载视频序列帧
        video_info = sample['video_info']
        video_frames = []
        
        # 限制视频长度避免内存问题
        max_frames = 8
        frame_list = video_info['video_frames'][:max_frames]
        
        for frame_info in frame_list:
            try:
                image = Image.open(frame_info['file_path']).convert('RGB')
                # 调整图像大小
                image = image.resize((336, 336))
                video_frames.append(image)
            except Exception as e:
                logger.warning(f"无法加载图像 {frame_info['file_path']}: {e}")
                continue
        
        if len(video_frames) < 4:
            return None
        
        # 构建消息
        conversations = sample['conversations']
        messages = [
            {
                "role": "user",
                "content": [
                    {"type": "video", "video": video_frames},
                    {"type": "text", "text": conversations[0]['value']}
                ]
            },
            {
                "role": "assistant", 
                "content": conversations[1]['value']
            }
        ]
        
        return {
            'messages': messages,
            'video_info': video_info
        }

def correct_video_collate_fn(batch, processor):
    """正确的视频序列数据整理函数"""
    # 过滤None样本
    batch = [item for item in batch if item is not None]
    if not batch:
        return None
    
    try:
        example = batch[0]
        messages = example["messages"]
        
        # 应用聊天模板
        text = processor.apply_chat_template(
            messages, tokenize=False, add_generation_prompt=True
        )
        
        # 处理视觉信息
        image_inputs, video_inputs = process_vision_info(messages)
        
        # 处理输入
        inputs = processor(
            text=[text],
            images=image_inputs,
            videos=video_inputs,
            padding=True,
            return_tensors="pt"
        )
        
        # 检查输入是否有效
        if inputs["input_ids"].numel() == 0:
            logger.warning("生成的input_ids为空，跳过此样本")
            return None
        
        # 为训练添加labels
        inputs["labels"] = inputs["input_ids"].clone()
        
        return inputs
        
    except Exception as e:
        logger.warning(f"处理样本时出错: {e}")
        return None

class CorrectVideoTrainer:
    """正确的视频序列训练器"""
    
    def __init__(self, model_path: str):
        self.model_path = model_path
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        
        logger.info(f"使用设备: {self.device}")
        logger.info(f"模型路径: {model_path}")
        
        # 加载模型和处理器
        self.processor = AutoProcessor.from_pretrained(model_path)
        self.model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
            model_path,
            torch_dtype=torch.float16,
            device_map="auto",
            trust_remote_code=True,
            low_cpu_mem_usage=True,
            use_cache=False
        )
        
        logger.info("模型和处理器加载完成")
    
    def setup_lora(self):
        """设置视频序列LoRA配置"""
        config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            r=16,
            lora_alpha=32,
            target_modules=["q_proj", "v_proj"],
            lora_dropout=0.1,
        )
        
        self.model = get_peft_model(self.model, config)
        self.model.print_trainable_parameters()
        logger.info("视频序列LoRA配置完成")
    
    def train(self, train_data_path: str, output_dir: str):
        """开始视频序列训练"""
        logger.info("开始视频序列训练...")
        
        # 创建数据集
        dataset = CorrectVideoDataset(train_data_path, self.processor)
        
        # 创建数据加载器
        def collate_wrapper(batch):
            return correct_video_collate_fn(batch, self.processor)
        
        # 视频序列训练参数
        args = TrainingArguments(
            output_dir=output_dir,
            num_train_epochs=5,  # 充分的训练轮数
            per_device_train_batch_size=1,
            gradient_accumulation_steps=4,
            learning_rate=2e-5,
            fp16=True,
            save_steps=20,
            logging_steps=5,
            save_total_limit=2,
            remove_unused_columns=False,
            dataloader_drop_last=True,
            dataloader_num_workers=0,
            skip_memory_metrics=True,
            max_grad_norm=1.0,
            warmup_steps=10,
            lr_scheduler_type="linear",
            weight_decay=0.01,
        )
        
        # 创建训练器
        trainer = Trainer(
            model=self.model,
            args=args,
            train_dataset=dataset,
            data_collator=collate_wrapper,
        )
        
        # 开始训练
        logger.info("开始视频序列微调训练，预计时间：20-40分钟...")
        trainer.train()
        
        # 保存模型
        trainer.save_model()
        self.processor.save_pretrained(output_dir)
        
        logger.info(f"视频序列训练完成，模型保存到: {output_dir}")
        return True

def main():
    """主函数"""
    print("=== 正确的视频序列微调训练系统 ===")
    print("🎯 目标: 使用真正的视频序列进行微调训练")
    print("📊 策略: 每隔10帧抽取组成视频序列，每个序列8帧")
    print("🎬 训练方式: 视频序列微调 (真正的视频输入)")
    print("🔧 技术: 视频序列LoRA + 深度训练")
    print("⏱️  预计时间: 20-40分钟")
    
    # 第1步：生成视频序列训练数据
    print("\n第1步：生成正确的视频序列训练数据...")
    if not generate_correct_video_training_data():
        print("❌ 视频序列数据生成失败")
        return False
    
    # 第2步：初始化训练器
    print("\n第2步：初始化视频序列训练器...")
    model_path = "qwen2.5-vl-7b"
    trainer = CorrectVideoTrainer(model_path)
    
    # 第3步：设置LoRA
    print("\n第3步：设置视频序列LoRA配置...")
    trainer.setup_lora()
    
    # 第4步：开始训练
    print("\n第4步：开始视频序列微调训练...")
    output_dir = "output/correct_video_sequence_lora"
    success = trainer.train("data/correct_video_training_data.json", output_dir)
    
    if success:
        print("\n🎉 正确的视频序列微调训练完成！")
        print(f"📁 模型保存到: {output_dir}")
        print("📋 下一步: 使用视频序列模型进行检测")
    else:
        print("\n❌ 视频序列训练失败！")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
