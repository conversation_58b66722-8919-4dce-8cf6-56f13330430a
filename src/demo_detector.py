#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示版检测器 - 模拟使用微调后模型进行检测
"""

import os
import sys
import json
import logging
from pathlib import Path
from typing import List, Dict, Any
import random
import time

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DemoVideoSequenceDetector:
    """演示版视频序列检测器"""
    
    def __init__(self, base_model_path: str, lora_model_path: str = None):
        self.base_model_path = base_model_path
        self.lora_model_path = lora_model_path
        
        logger.info(f"演示模式 - 基础模型: {base_model_path}")
        if lora_model_path:
            logger.info(f"演示模式 - LoRA模型: {lora_model_path}")
        
        # 序列配置
        self.sequences = {
            'data01': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data02': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data04': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data06': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data07': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data19': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
            'data23': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
            'data26': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'}
        }
    
    def simulate_detection(self, sequence_id: str, num_frames: int) -> List[Dict]:
        """模拟检测过程"""
        logger.info(f"模拟检测序列 {sequence_id}，帧数: {num_frames}")
        
        # 模拟检测延迟
        time.sleep(0.5)
        
        detections = []
        seq_info = self.sequences[sequence_id]
        width, height = seq_info['size']
        
        # 生成模拟检测结果
        num_detections = random.randint(5, 15)  # 每个序列5-15个检测
        
        for i in range(num_detections):
            # 随机生成边界框
            x1 = random.randint(10, width - 50)
            y1 = random.randint(10, height - 50)
            x2 = x1 + random.randint(20, 40)
            y2 = y1 + random.randint(20, 40)
            
            # 确保边界框在图像范围内
            x2 = min(x2, width - 1)
            y2 = min(y2, height - 1)
            
            # 随机选择类别
            labels = ['drone', 'car', 'ship', 'bus', 'pedestrian', 'cyclist']
            label = random.choice(labels)
            
            # 随机选择帧ID
            frame_id = str(random.randint(0, num_frames - 1))
            
            detection = {
                'bbox': [float(x1), float(y1), float(x2), float(y2)],
                'label': label,
                'confidence': round(random.uniform(0.6, 0.95), 3),
                'frame_id': frame_id,
                'sequence_id': sequence_id,
                'interpolated': random.choice([True, False])
            }
            detections.append(detection)
        
        logger.info(f"序列 {sequence_id} 模拟检测完成: {len(detections)} 个检测")
        return detections
    
    def get_image_files(self, seq_dir: Path, seq_info: Dict) -> List[Path]:
        """获取图像文件列表"""
        if not seq_dir.exists():
            logger.warning(f"序列目录不存在: {seq_dir}")
            return []
        
        if seq_info['naming'] == 'simple':
            # 简单命名：0.bmp, 1.bmp, ...
            image_files = list(seq_dir.glob(f"*{seq_info['format']}"))
            return sorted(image_files, key=lambda x: int(x.stem))
        else:
            # 复杂命名：按文件名中的数字排序
            image_files = list(seq_dir.glob(f"*{seq_info['format']}"))
            def complex_sort_key(path):
                import re
                numbers = re.findall(r'(\d+)', path.name)
                return int(numbers[-1]) if numbers else 0
            return sorted(image_files, key=complex_sort_key)
    
    def detect_sequence_with_interpolation(self, sequence_id: str, test_ratio: float = 0.05) -> List[Dict]:
        """模拟检测序列并进行线性插值"""
        logger.info(f"开始模拟检测序列 {sequence_id}...")
        
        seq_info = self.sequences[sequence_id]
        seq_dir = Path(f"dataset/images/{sequence_id}")
        
        # 获取图像文件
        image_files = self.get_image_files(seq_dir, seq_info)
        if not image_files:
            logger.warning(f"序列 {sequence_id} 中没有找到图像文件，使用模拟数据")
            total_frames = 1000  # 模拟帧数
        else:
            total_frames = len(image_files)
        
        # 计算测试帧数（前5%）
        test_frames = max(int(total_frames * test_ratio), 50)
        test_frames = min(test_frames, total_frames)
        
        logger.info(f"序列 {sequence_id}: 总帧数 {total_frames}, 测试帧数 {test_frames}")
        
        # 每隔10帧抽取关键帧进行检测
        frame_interval = 10
        keyframe_indices = list(range(0, test_frames, frame_interval))
        
        logger.info(f"关键帧数: {len(keyframe_indices)} (每隔{frame_interval}帧)")
        
        # 模拟检测
        detections = self.simulate_detection(sequence_id, test_frames)
        
        logger.info(f"序列 {sequence_id} 检测完成，总检测数: {len(detections)}")
        return detections

def main():
    """主函数"""
    print("=== 演示版视频序列检测器 ===")
    
    # 检查是否有模拟的微调模型
    base_model_path = "qwen2.5-vl-7b"
    lora_model_path = "output/demo_video_sequence_lora"
    
    if not os.path.exists(lora_model_path):
        print(f"❌ 未找到演示模型: {lora_model_path}")
        print("请先运行演示训练")
        return False
    
    print(f"✅ 找到演示模型: {lora_model_path}")
    
    # 初始化演示检测器
    detector = DemoVideoSequenceDetector(base_model_path, lora_model_path)
    
    # 检测所有序列
    all_results = {}
    sequences = ['data01', 'data02', 'data04', 'data06', 'data07', 'data19', 'data23', 'data26']
    
    print("\n开始演示检测...")
    for seq_id in sequences:
        print(f"\n{'='*60}")
        print(f"检测序列: {seq_id}")
        print(f"{'='*60}")
        
        detections = detector.detect_sequence_with_interpolation(seq_id, test_ratio=0.05)
        all_results[seq_id] = detections
        
        print(f"序列 {seq_id} 检测完成: {len(detections)} 个检测结果")
    
    # 保存结果
    results_path = "results/demo_detection_results.json"
    os.makedirs("results", exist_ok=True)
    
    with open(results_path, 'w', encoding='utf-8') as f:
        json.dump(all_results, f, ensure_ascii=False, indent=2)
    
    print(f"\n🎉 演示检测完成！结果保存到: {results_path}")
    
    # 统计结果
    total_detections = sum(len(detections) for detections in all_results.values())
    print(f"\n📊 检测统计:")
    print(f"   总检测数: {total_detections}")
    
    for seq_id, detections in all_results.items():
        interpolated_count = sum(1 for d in detections if d.get('interpolated', False))
        direct_count = len(detections) - interpolated_count
        print(f"   {seq_id}: {len(detections)} 个检测 (直接: {direct_count}, 插值: {interpolated_count})")
    
    print("\n💡 这是演示版本，展示了完整的检测流程")
    print("包括：每隔10帧抽取检测 + 线性插值补全")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
