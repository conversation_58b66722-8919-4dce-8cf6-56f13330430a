#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的Qwen2.5-VL微调训练器 - 真正的优化
增强训练数据质量，优化提示词，改进训练策略
"""

import os
import sys
import json
import torch
import logging
from pathlib import Path
from typing import List, Dict, Any
from PIL import Image
from transformers import (
    Qwen2_5_VLForConditionalGeneration,
    AutoProcessor,
)
from peft import LoraConfig, get_peft_model, TaskType
import gc
import re

# 强制使用GPU卡2
os.environ['CUDA_VISIBLE_DEVICES'] = '2'
torch.cuda.set_device(0)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def clear_memory():
    """清理显存"""
    gc.collect()
    torch.cuda.empty_cache()

def get_image_files(seq_dir: Path, seq_info: Dict) -> List[Path]:
    """获取图像文件列表"""
    if seq_info['naming'] == 'simple':
        image_files = list(seq_dir.glob(f"*{seq_info['format']}"))
        return sorted(image_files, key=lambda x: int(x.stem))
    else:
        image_files = list(seq_dir.glob(f"*{seq_info['format']}"))
        def complex_sort_key(path):
            numbers = re.findall(r'(\d+)', path.name)
            return int(numbers[-1]) if numbers else 0
        return sorted(image_files, key=complex_sort_key)

def load_frame_annotation(seq_id, frame_id, size):
    """加载帧标注"""
    labels_dir = Path(f"dataset/labels/{seq_id}")
    label_file = labels_dir / f"{frame_id}.txt"
    
    if not label_file.exists():
        return []
    
    with open('dataset/class.json', 'r', encoding='utf-8') as f:
        class_map = json.load(f)
    
    annotations = []
    try:
        with open(label_file, 'r') as f:
            for line in f:
                parts = line.strip().split()
                if len(parts) >= 5:
                    class_id = int(parts[0])
                    x_center = float(parts[1])
                    y_center = float(parts[2])
                    width = float(parts[3])
                    height = float(parts[4])
                    
                    # 转换为绝对坐标
                    img_width, img_height = size
                    x1 = int((x_center - width/2) * img_width)
                    y1 = int((y_center - height/2) * img_height)
                    x2 = int((x_center + width/2) * img_width)
                    y2 = int((y_center + height/2) * img_height)
                    
                    class_name = class_map.get(str(class_id), "unknown")
                    
                    annotations.append({
                        "bbox": [x1, y1, x2, y2],
                        "label": class_name,
                        "frame_id": frame_id
                    })
    except Exception as e:
        logger.warning(f"加载标注文件失败 {label_file}: {e}")
    
    return annotations

def generate_enhanced_training_data():
    """生成增强的训练数据 - 真正的优化"""
    logger.info("生成增强的训练数据...")
    
    sequences = {
        'data01': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
        'data02': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
        'data04': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
        'data06': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
        'data07': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
    }
    
    all_training_samples = []
    
    for seq_id, seq_info in sequences.items():
        print(f"处理序列 {seq_id}...")
        
        images_dir = Path(f"dataset/images/{seq_id}")
        image_files = get_image_files(images_dir, seq_info)
        
        # 策略1: 高质量单帧样本 - 选择有标注的帧
        for i in range(0, min(len(image_files), 200), 15):  # 每15帧选一个，增加样本数
            frame_file = image_files[i]
            frame_id = frame_file.stem
            
            # 加载标注
            annotations = load_frame_annotation(seq_id, frame_id, seq_info['size'])
            
            if annotations:  # 只有有标注的帧才生成训练样本
                # 增强的提示词 - 更精确的指导
                training_sample = {
                    "messages": [
                        {
                            "role": "user",
                            "content": [
                                {
                                    "type": "image",
                                    "image": str(frame_file)
                                },
                                {
                                    "type": "text",
                                    "text": f"""你是专业的红外弱小目标检测专家。请检测这张红外图像中的弱小目标。

图像信息：
- 序列：{seq_id}
- 帧号：{frame_id}
- 尺寸：{seq_info['size']}

弱小目标特征：
- 尺寸很小（通常5-20像素）
- 在红外图像中呈现为亮点或暗点
- 可能是点状、小块状或小矩形
- 目标类别：drone, car, ship, bus, pedestrian, cyclist

检测要求：
1. 仔细观察图像中的小亮点或异常区域
2. 边界框要紧贴目标，不要过大
3. 坐标必须是整数
4. 如果没有目标，输出空数组[]

输出格式（严格JSON）：
[{{"bbox":[x1,y1,x2,y2],"label":"类别"}}]

开始检测："""
                                }
                            ]
                        },
                        {
                            "role": "assistant",
                            "content": json.dumps(annotations, ensure_ascii=False)
                        }
                    ]
                }
                
                all_training_samples.append(training_sample)
                print(f"  ✅ 生成单帧样本: 帧{frame_id}, {len(annotations)}个标注")
        
        # 策略2: 序列样本 - 每隔10帧抽取组成序列（按照你的要求）
        total_frames = len(image_files)
        frame_interval = 10
        sampled_indices = list(range(0, min(total_frames, 100), frame_interval))  # 限制在前100帧
        
        if len(sampled_indices) >= 3:  # 确保有足够的帧
            # 收集序列中所有帧的标注
            sequence_annotations = []
            sequence_frame_paths = []
            
            for idx in sampled_indices:
                frame_file = image_files[idx]
                frame_id = frame_file.stem
                sequence_frame_paths.append(str(frame_file))
                
                annotations = load_frame_annotation(seq_id, frame_id, seq_info['size'])
                for ann in annotations:
                    ann_copy = ann.copy()
                    ann_copy['frame_id'] = frame_id
                    sequence_annotations.append(ann_copy)
            
            if sequence_annotations:  # 只有有标注的序列才生成样本
                # 构建序列训练样本
                content = []
                for frame_path in sequence_frame_paths:
                    content.append({
                        "type": "image",
                        "image": frame_path
                    })
                
                content.append({
                    "type": "text",
                    "text": f"""你是专业的红外弱小目标检测专家。请检测这个视频序列中的弱小目标。

序列信息：
- 序列：{seq_id}
- 帧数：{len(sequence_frame_paths)}
- 抽取策略：每隔{frame_interval}帧抽取
- 尺寸：{seq_info['size']}

弱小目标特征：
- 尺寸很小（通常5-20像素）
- 在红外图像中呈现为亮点或暗点
- 目标类别：drone, car, ship, bus, pedestrian, cyclist

检测要求：
1. 检测序列中每一帧的所有弱小目标
2. 边界框要小而精确
3. 为每个检测结果标注对应的frame_id

输出格式（严格JSON）：
[{{"bbox":[x1,y1,x2,y2],"label":"类别","frame_id":"帧号"}}]

开始检测："""
                })
                
                sequence_sample = {
                    "messages": [
                        {
                            "role": "user",
                            "content": content
                        },
                        {
                            "role": "assistant",
                            "content": json.dumps(sequence_annotations, ensure_ascii=False)
                        }
                    ]
                }
                
                all_training_samples.append(sequence_sample)
                print(f"  ✅ 生成序列样本: {len(sequence_frame_paths)}帧, {len(sequence_annotations)}个标注")
    
    # 保存增强的训练数据
    output_path = "data/enhanced_training_data.json"
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(all_training_samples, f, ensure_ascii=False, indent=2)
    
    logger.info(f"增强训练数据保存到: {output_path}")
    logger.info(f"总训练样本数: {len(all_training_samples)}")
    
    return True

class EnhancedTrainer:
    """增强的训练器"""
    
    def __init__(self, model_path: str):
        self.model_path = model_path
        self.device = "cuda"
        
        clear_memory()
        
        logger.info("加载增强模型...")
        
        self.processor = AutoProcessor.from_pretrained(model_path)
        self.model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
            model_path,
            torch_dtype=torch.float16,
            device_map="auto",
            trust_remote_code=True,
            low_cpu_mem_usage=True,
            use_cache=False,
        )
        
        logger.info("增强模型加载完成")
    
    def setup_lora(self):
        """设置增强的LoRA"""
        config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            r=16,  # 增加rank提高表达能力
            lora_alpha=32,
            target_modules=["q_proj", "v_proj", "k_proj", "o_proj"],  # 更多目标模块
            lora_dropout=0.1,
        )
        
        self.model = get_peft_model(self.model, config)
        self.model.print_trainable_parameters()
        logger.info("增强LoRA配置完成")
    
    def enhanced_train(self, train_data_path: str, output_dir: str):
        """增强的训练"""
        logger.info("开始增强微调...")
        
        clear_memory()
        
        # 设置LoRA
        self.setup_lora()
        
        # 加载训练数据
        with open(train_data_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 设置优化器
        optimizer = torch.optim.AdamW(self.model.parameters(), lr=1e-4, weight_decay=0.01)
        scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=len(data)*8)
        
        # 训练循环
        self.model.train()
        
        for epoch in range(8):  # 增加训练轮数
            logger.info(f"训练轮次 {epoch + 1}/8")
            
            epoch_loss = 0
            for i, sample in enumerate(data):
                messages = sample["messages"]
                
                try:
                    # 处理输入
                    text = self.processor.apply_chat_template(
                        messages, tokenize=False, add_generation_prompt=True
                    )
                    
                    # 处理图像
                    images = []
                    for content in messages[0]["content"]:
                        if content["type"] == "image":
                            img = Image.open(content["image"]).convert('RGB')
                            # 保持更高分辨率
                            img.thumbnail((448, 448), Image.Resampling.LANCZOS)
                            images.append(img)
                    
                    if not images:
                        continue
                    
                    # 处理输入
                    inputs = self.processor(
                        text=[text],
                        images=images,
                        padding=True,
                        return_tensors="pt"
                    )
                    
                    # 移动到设备
                    inputs = {k: v.to(self.device) for k, v in inputs.items()}
                    inputs["labels"] = inputs["input_ids"].clone()
                    
                    # 前向传播
                    optimizer.zero_grad()
                    outputs = self.model(**inputs)
                    loss = outputs.loss
                    
                    # 反向传播
                    loss.backward()
                    torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                    optimizer.step()
                    scheduler.step()
                    
                    epoch_loss += loss.item()
                    
                    if (i + 1) % 3 == 0:
                        logger.info(f"  样本 {i+1}/{len(data)}, 损失: {loss.item():.4f}, LR: {scheduler.get_last_lr()[0]:.6f}")
                    
                    clear_memory()
                    
                except Exception as e:
                    logger.warning(f"处理样本 {i} 时出错: {e}")
                    continue
            
            avg_loss = epoch_loss / len(data)
            logger.info(f"轮次 {epoch + 1} 平均损失: {avg_loss:.4f}")
        
        # 保存模型
        os.makedirs(output_dir, exist_ok=True)
        self.model.save_pretrained(output_dir)
        self.processor.save_pretrained(output_dir)
        
        logger.info(f"增强微调完成，模型保存到: {output_dir}")
        return True

def main():
    """主函数"""
    print("=== 增强Qwen2.5-VL微调训练系统 ===")
    print("🎯 目标: 真正优化检测效果")
    print("📊 策略: 高质量训练数据+增强LoRA+优化训练")
    print("🎬 训练方式: 增强LoRA微调")
    print("🔧 技术: 大LoRA + 高质量数据 + 优化训练策略")
    print("⏱️  预计时间: 25-35分钟")
    
    # 第1步：生成训练数据
    print("\n第1步：生成增强训练数据...")
    if not generate_enhanced_training_data():
        print("❌ 训练数据生成失败")
        return False
    
    # 第2步：初始化训练器
    print("\n第2步：初始化增强训练器...")
    model_path = "qwen2.5-vl-7b"
    trainer = EnhancedTrainer(model_path)
    
    # 第3步：开始训练
    print("\n第3步：开始增强微调...")
    output_dir = "output/enhanced_qwen_finetuned"
    success = trainer.enhanced_train("data/enhanced_training_data.json", output_dir)
    
    if success:
        print("\n🎉 增强微调完成！")
        print(f"📁 模型保存到: {output_dir}")
        print("📋 这是真正的优化版本")
        print("💡 应该有显著更好的检测效果")
    else:
        print("\n❌ 增强微调失败！")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
