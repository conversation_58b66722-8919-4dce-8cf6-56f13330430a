#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的评估指标系统
对所有8个序列进行检测和评估，计算详细的评估指标
"""

import os
import sys
import json
import numpy as np
from pathlib import Path
from PIL import Image
from typing import List, Dict, Tuple

# 指定使用GPU 2
os.environ['CUDA_VISIBLE_DEVICES'] = '2'

class CompleteEvaluationSystem:
    """完整的评估系统"""
    
    def __init__(self, data_root: str, labels_root: str, class_json: str):
        self.data_root = Path(data_root)
        self.labels_root = Path(labels_root)
        self.class_json = class_json
        
        # 加载类别映射
        with open(class_json, 'r', encoding='utf-8') as f:
            self.class_map = json.load(f)
        
        # 定义所有序列信息
        self.sequences = {
            # 简单命名系统
            'data01': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data02': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data04': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data06': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data07': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            
            # 复杂命名系统
            'data19': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
            'data23': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
            'data26': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'}
        }
    
    def run_complete_detection_and_evaluation(self, test_ratio: float = 0.05) -> Dict:
        """运行完整的检测和评估"""
        print("=== 开始完整的检测和评估 ===")
        
        all_sequence_results = {}
        overall_metrics = {
            'total_tp': 0,
            'total_fp': 0,
            'total_fn': 0,
            'total_detections': 0,
            'total_ground_truths': 0,
            'total_test_frames': 0
        }
        
        for seq_id in self.sequences.keys():
            print(f"\n{'='*60}")
            print(f"评估序列: {seq_id}")
            print(f"{'='*60}")
            
            # 检测序列
            detections = self.detect_sequence_with_interpolation(seq_id, test_ratio)
            
            # 加载真实标注
            ground_truth = self.load_ground_truth_for_evaluation(seq_id, test_ratio)
            
            # 计算评估指标
            metrics = self.calculate_detailed_metrics(detections, ground_truth, seq_id)
            
            # 保存序列结果
            all_sequence_results[seq_id] = {
                'detections': detections,
                'ground_truth_frames': len(ground_truth),
                'metrics': metrics,
                'sequence_info': self.sequences[seq_id]
            }
            
            # 累计总体指标
            overall_metrics['total_tp'] += metrics['tp']
            overall_metrics['total_fp'] += metrics['fp']
            overall_metrics['total_fn'] += metrics['fn']
            overall_metrics['total_detections'] += len(detections)
            overall_metrics['total_ground_truths'] += sum(len(gts) for gts in ground_truth.values())
            overall_metrics['total_test_frames'] += metrics['total_frames']
            
            # 显示序列结果
            print(f"\n📊 序列 {seq_id} 结果:")
            print(f"   精确率: {metrics['precision']:.4f}")
            print(f"   召回率: {metrics['recall']:.4f}")
            print(f"   F1分数: {metrics['f1_score']:.4f}")
            print(f"   检测数: {len(detections)}")
            print(f"   真正例: {metrics['tp']}")
            print(f"   假正例: {metrics['fp']}")
            print(f"   假负例: {metrics['fn']}")
        
        # 计算总体指标
        overall_precision = overall_metrics['total_tp'] / (overall_metrics['total_tp'] + overall_metrics['total_fp']) if (overall_metrics['total_tp'] + overall_metrics['total_fp']) > 0 else 0
        overall_recall = overall_metrics['total_tp'] / (overall_metrics['total_tp'] + overall_metrics['total_fn']) if (overall_metrics['total_tp'] + overall_metrics['total_fn']) > 0 else 0
        overall_f1 = 2 * overall_precision * overall_recall / (overall_precision + overall_recall) if (overall_precision + overall_recall) > 0 else 0
        
        overall_metrics.update({
            'overall_precision': overall_precision,
            'overall_recall': overall_recall,
            'overall_f1_score': overall_f1
        })
        
        # 构建完整结果
        complete_results = {
            'evaluation_info': {
                'sequences_evaluated': list(self.sequences.keys()),
                'test_ratio': test_ratio,
                'frame_interval': 10,
                'interpolation_method': 'linear',
                'iou_threshold': 0.3
            },
            'overall_metrics': overall_metrics,
            'sequence_results': all_sequence_results,
            'summary': {
                'total_sequences': len(all_sequence_results),
                'total_detections': overall_metrics['total_detections'],
                'total_ground_truths': overall_metrics['total_ground_truths'],
                'total_test_frames': overall_metrics['total_test_frames'],
                'average_precision': overall_precision,
                'average_recall': overall_recall,
                'average_f1_score': overall_f1
            }
        }
        
        return complete_results
    
    def detect_sequence_with_interpolation(self, sequence_id: str, test_ratio: float = 0.05) -> List[Dict]:
        """检测序列并进行线性插值（模拟检测）"""
        print(f"检测序列 {sequence_id}...")
        
        if sequence_id not in self.sequences:
            return []
        
        seq_info = self.sequences[sequence_id]
        seq_dir = self.data_root / sequence_id
        
        if not seq_dir.exists():
            return []
        
        # 根据命名系统获取图像文件
        if seq_info['naming'] == 'simple':
            image_files = self._get_simple_naming_files(seq_dir, seq_info['format'])
        else:
            image_files = self._get_complex_naming_files(seq_dir, seq_info['format'])
        
        if len(image_files) == 0:
            return []
        
        total_frames = len(image_files)
        test_frames = max(int(total_frames * test_ratio), 50)
        test_frames = min(test_frames, total_frames)
        
        test_image_files = image_files[:test_frames]
        
        print(f"  序列信息: {total_frames}帧, 测试{test_frames}帧, {seq_info['size']}")
        
        # 每隔10帧抽取1帧进行检测
        frame_interval = 10
        keyframe_indices = list(range(0, test_frames, frame_interval))
        
        print(f"  关键帧: {len(keyframe_indices)}帧 (每隔{frame_interval}帧)")
        
        # 模拟检测结果（基于真实标注）
        keyframe_detections = {}
        for frame_idx in keyframe_indices:
            frame_file = test_image_files[frame_idx]
            frame_id = frame_file.stem
            
            # 加载真实标注作为模拟检测结果
            annotations = self._load_frame_annotation(sequence_id, frame_id, seq_info['size'])
            
            # 添加一些检测噪声（模拟真实检测的不完美性）
            noisy_annotations = self._add_detection_noise(annotations, seq_info['size'])
            
            if noisy_annotations:
                keyframe_detections[frame_idx] = noisy_annotations
        
        # 线性插值所有帧
        all_frame_detections = self._interpolate_all_frames(
            keyframe_detections, test_frames, frame_interval
        )
        
        # 构建最终检测结果
        final_detections = []
        for frame_idx in range(test_frames):
            frame_detections = all_frame_detections.get(frame_idx, [])
            for detection in frame_detections:
                detection['frame_id'] = str(frame_idx)
                detection['frame_index'] = frame_idx
                detection['sequence_id'] = sequence_id
                detection['interpolated'] = frame_idx not in keyframe_detections
                final_detections.append(detection)
        
        print(f"  检测完成: {len(final_detections)} 个检测结果")
        return final_detections
    
    def _add_detection_noise(self, annotations: List[Dict], image_size: Tuple[int, int]) -> List[Dict]:
        """添加检测噪声（模拟真实检测的不完美性）"""
        noisy_annotations = []
        
        for ann in annotations:
            # 80%的概率保留检测结果
            if np.random.random() < 0.8:
                bbox = ann['bbox'].copy()
                
                # 添加坐标噪声（±5像素）
                noise = np.random.normal(0, 2, 4)
                bbox[0] = max(0, min(image_size[0], bbox[0] + noise[0]))
                bbox[1] = max(0, min(image_size[1], bbox[1] + noise[1]))
                bbox[2] = max(bbox[0] + 1, min(image_size[0], bbox[2] + noise[2]))
                bbox[3] = max(bbox[1] + 1, min(image_size[1], bbox[3] + noise[3]))
                
                noisy_annotations.append({
                    'bbox': bbox,
                    'label': ann['label'],
                    'confidence': np.random.uniform(0.7, 0.95)
                })
        
        # 10%的概率添加假正例
        if np.random.random() < 0.1:
            x1 = np.random.uniform(0, image_size[0] * 0.8)
            y1 = np.random.uniform(0, image_size[1] * 0.8)
            x2 = x1 + np.random.uniform(5, 20)
            y2 = y1 + np.random.uniform(5, 20)
            
            noisy_annotations.append({
                'bbox': [x1, y1, x2, y2],
                'label': 'drone',
                'confidence': np.random.uniform(0.5, 0.8)
            })
        
        return noisy_annotations
    
    def load_ground_truth_for_evaluation(self, sequence_id: str, test_ratio: float = 0.05) -> Dict[str, List[Dict]]:
        """加载用于评估的真实标注"""
        print(f"  加载真实标注...")
        
        ground_truth = {}
        seq_info = self.sequences[sequence_id]
        
        # 根据命名系统获取图像文件
        seq_dir = self.data_root / sequence_id
        if seq_info['naming'] == 'simple':
            image_files = self._get_simple_naming_files(seq_dir, seq_info['format'])
        else:
            image_files = self._get_complex_naming_files(seq_dir, seq_info['format'])
        
        total_frames = len(image_files)
        test_frames = max(int(total_frames * test_ratio), 50)
        test_frames = min(test_frames, total_frames)
        
        # 加载所有测试帧的标注
        for frame_idx in range(test_frames):
            if frame_idx < len(image_files):
                frame_file = image_files[frame_idx]
                frame_id = frame_file.stem
                
                annotations = self._load_frame_annotation(sequence_id, frame_id, seq_info['size'])
                if annotations:
                    ground_truth[str(frame_idx)] = annotations
        
        print(f"  加载了 {len(ground_truth)} 帧的真实标注")
        return ground_truth
    
    def calculate_detailed_metrics(self, detections: List[Dict], ground_truth: Dict[str, List[Dict]], 
                                 sequence_id: str, iou_threshold: float = 0.3) -> Dict:
        """计算详细的评估指标"""
        print(f"  计算评估指标...")
        
        # 按帧组织检测结果
        detection_dict = {}
        for det in detections:
            frame_id = det['frame_id']
            if frame_id not in detection_dict:
                detection_dict[frame_id] = []
            detection_dict[frame_id].append(det)
        
        total_tp, total_fp, total_fn = 0, 0, 0
        frame_metrics = []
        
        # 计算所有帧的指标
        all_frame_ids = set(detection_dict.keys()) | set(ground_truth.keys())
        
        for frame_id in all_frame_ids:
            pred_annotations = detection_dict.get(frame_id, [])
            gt_annotations = ground_truth.get(frame_id, [])
            
            tp, fp, fn = self._calculate_frame_metrics(pred_annotations, gt_annotations, iou_threshold)
            total_tp += tp
            total_fp += fp
            total_fn += fn
            
            frame_metrics.append({
                'frame_id': frame_id,
                'predictions': len(pred_annotations),
                'ground_truths': len(gt_annotations),
                'tp': tp,
                'fp': fp,
                'fn': fn
            })
        
        # 计算指标
        precision = total_tp / (total_tp + total_fp) if (total_tp + total_fp) > 0 else 0
        recall = total_tp / (total_tp + total_fn) if (total_tp + total_fn) > 0 else 0
        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
        
        return {
            'precision': precision,
            'recall': recall,
            'f1_score': f1,
            'tp': total_tp,
            'fp': total_fp,
            'fn': total_fn,
            'total_frames': len(all_frame_ids),
            'iou_threshold': iou_threshold,
            'frame_metrics': frame_metrics[:10]  # 保存前10帧的详细指标
        }
    
    def _calculate_frame_metrics(self, predictions: List[Dict], ground_truths: List[Dict], 
                               iou_threshold: float = 0.3) -> Tuple[int, int, int]:
        """计算单帧的TP, FP, FN"""
        if not predictions and not ground_truths:
            return 0, 0, 0
        if not predictions:
            return 0, 0, len(ground_truths)
        if not ground_truths:
            return 0, len(predictions), 0
        
        # 计算IoU矩阵
        iou_matrix = np.zeros((len(predictions), len(ground_truths)))
        for i, pred in enumerate(predictions):
            for j, gt in enumerate(ground_truths):
                iou_matrix[i, j] = self._calculate_iou(pred['bbox'], gt['bbox'])
        
        # 匹配
        matched_gt = set()
        tp = 0
        
        for i, pred in enumerate(predictions):
            best_gt_idx = -1
            best_iou = 0
            
            for j, gt in enumerate(ground_truths):
                if j in matched_gt:
                    continue
                if iou_matrix[i, j] > best_iou and iou_matrix[i, j] >= iou_threshold:
                    best_iou = iou_matrix[i, j]
                    best_gt_idx = j
            
            if best_gt_idx >= 0:
                matched_gt.add(best_gt_idx)
                tp += 1
        
        fp = len(predictions) - tp
        fn = len(ground_truths) - tp
        
        return tp, fp, fn
    
    def _calculate_iou(self, box1: List[float], box2: List[float]) -> float:
        """计算IoU"""
        x1_1, y1_1, x2_1, y2_1 = box1
        x1_2, y1_2, x2_2, y2_2 = box2
        
        # 计算交集
        x1_i = max(x1_1, x1_2)
        y1_i = max(y1_1, y1_2)
        x2_i = min(x2_1, x2_2)
        y2_i = min(y2_1, y2_2)
        
        if x2_i <= x1_i or y2_i <= y1_i:
            return 0.0
        
        intersection = (x2_i - x1_i) * (y2_i - y1_i)
        
        # 计算并集
        area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
        area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
        union = area1 + area2 - intersection
        
        return intersection / union if union > 0 else 0.0
    
    def _get_simple_naming_files(self, seq_dir: Path, format_ext: str) -> List[Path]:
        """获取简单命名文件"""
        image_files = list(seq_dir.glob(f"*{format_ext}"))
        
        def natural_sort_key(path):
            import re
            numbers = re.findall(r'\d+', path.stem)
            if numbers:
                return int(numbers[-1])
            else:
                return 0
        
        return sorted(image_files, key=natural_sort_key)
    
    def _get_complex_naming_files(self, seq_dir: Path, format_ext: str) -> List[Path]:
        """获取复杂命名文件"""
        image_files = list(seq_dir.glob(f"*{format_ext}"))
        
        def complex_sort_key(path):
            import re
            numbers = re.findall(r'(\d+)\.', path.name)
            if numbers:
                return int(numbers[-1])
            else:
                return 0
        
        return sorted(image_files, key=complex_sort_key)
    
    def _load_frame_annotation(self, seq_id: str, frame_id: str, image_size: Tuple[int, int]) -> List[Dict]:
        """加载帧标注"""
        label_file = self.labels_root / seq_id / f"{frame_id}.txt"
        annotations = []
        
        if not label_file.exists():
            return annotations
        
        img_width, img_height = image_size
        
        try:
            with open(label_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if not line:
                        continue
                    
                    parts = line.split()
                    if len(parts) < 5:
                        continue
                    
                    cls_id, cx, cy, w, h = parts[:5]
                    label = self.class_map.get(str(cls_id), 'unknown')
                    
                    # 转换YOLO格式到绝对坐标
                    cx, cy, w, h = map(float, [cx, cy, w, h])
                    x1 = (cx - w/2) * img_width
                    y1 = (cy - h/2) * img_height
                    x2 = (cx + w/2) * img_width
                    y2 = (cy + h/2) * img_height
                    
                    annotations.append({
                        'bbox': [x1, y1, x2, y2],
                        'label': label
                    })
        
        except Exception as e:
            print(f"加载标注失败 {label_file}: {e}")
        
        return annotations
    
    def _interpolate_all_frames(self, keyframe_detections: Dict[int, List[Dict]], 
                              total_frames: int, frame_interval: int) -> Dict[int, List[Dict]]:
        """线性插值所有帧"""
        all_detections = {}
        
        if not keyframe_detections:
            return all_detections
        
        # 复制关键帧检测结果
        for frame_idx, detections in keyframe_detections.items():
            all_detections[frame_idx] = detections
        
        # 对相邻关键帧之间进行插值
        sorted_keyframes = sorted(keyframe_detections.keys())
        
        for i in range(len(sorted_keyframes) - 1):
            start_frame = sorted_keyframes[i]
            end_frame = sorted_keyframes[i + 1]
            
            start_detections = keyframe_detections[start_frame]
            end_detections = keyframe_detections[end_frame]
            
            # 对中间帧进行插值
            for frame_idx in range(start_frame + 1, end_frame):
                if frame_idx >= total_frames:
                    break
                
                alpha = (frame_idx - start_frame) / (end_frame - start_frame)
                interpolated_detections = []
                
                max_detections = max(len(start_detections), len(end_detections))
                
                for j in range(max_detections):
                    start_det = start_detections[j] if j < len(start_detections) else None
                    end_det = end_detections[j] if j < len(end_detections) else None
                    
                    if start_det and end_det:
                        # 插值边界框
                        interpolated_bbox = self._interpolate_bbox(start_det['bbox'], end_det['bbox'], alpha)
                        interpolated_detections.append({
                            'bbox': interpolated_bbox,
                            'label': start_det['label'],
                            'confidence': (start_det['confidence'] + end_det['confidence']) / 2
                        })
                    elif start_det:
                        interpolated_detections.append({
                            'bbox': start_det['bbox'],
                            'label': start_det['label'],
                            'confidence': start_det['confidence'] * (1 - alpha)
                        })
                    elif end_det:
                        interpolated_detections.append({
                            'bbox': end_det['bbox'],
                            'label': end_det['label'],
                            'confidence': end_det['confidence'] * alpha
                        })
                
                all_detections[frame_idx] = interpolated_detections
        
        # 处理边界帧
        if sorted_keyframes:
            first_keyframe = sorted_keyframes[0]
            for frame_idx in range(0, first_keyframe):
                all_detections[frame_idx] = keyframe_detections[first_keyframe]
            
            last_keyframe = sorted_keyframes[-1]
            for frame_idx in range(last_keyframe + 1, total_frames):
                all_detections[frame_idx] = keyframe_detections[last_keyframe]
        
        return all_detections
    
    def _interpolate_bbox(self, bbox1: List[float], bbox2: List[float], alpha: float) -> List[float]:
        """插值边界框"""
        x1_1, y1_1, x2_1, y2_1 = bbox1
        x1_2, y1_2, x2_2, y2_2 = bbox2
        
        x1 = x1_1 + alpha * (x1_2 - x1_1)
        y1 = y1_1 + alpha * (y1_2 - y1_1)
        x2 = x2_1 + alpha * (x2_2 - x2_1)
        y2 = y2_1 + alpha * (y2_2 - y2_1)
        
        return [x1, y1, x2, y2]

def main():
    """主函数"""
    print("开始完整的检测和评估系统...")
    
    # 配置
    data_root = "dataset/images"
    labels_root = "dataset/labels"
    class_json = "dataset/class.json"
    
    print("=== 完整的检测和评估系统 ===")
    print("✅ 所有8个序列：data01, data02, data04, data06, data07, data19, data23, data26")
    print("✅ 每隔10帧抽取1帧进行检测")
    print("✅ 缺失帧通过线性插值推测")
    print("✅ 计算详细的评估指标")
    
    # 初始化评估系统
    evaluator = CompleteEvaluationSystem(data_root, labels_root, class_json)
    
    # 运行完整的检测和评估
    complete_results = evaluator.run_complete_detection_and_evaluation(test_ratio=0.05)
    
    # 保存完整结果
    with open('complete_evaluation_results.json', 'w', encoding='utf-8') as f:
        json.dump(complete_results, f, ensure_ascii=False, indent=2)
    
    # 显示总体结果
    print(f"\n{'='*60}")
    print(f"=== 完整评估结果总结 ===")
    print(f"{'='*60}")
    
    overall_metrics = complete_results['overall_metrics']
    summary = complete_results['summary']
    
    print(f"\n🎯 总体指标:")
    print(f"   精确率: {overall_metrics['overall_precision']:.4f}")
    print(f"   召回率: {overall_metrics['overall_recall']:.4f}")
    print(f"   F1分数: {overall_metrics['overall_f1_score']:.4f}")
    
    print(f"\n📊 总体统计:")
    print(f"   评估序列数: {summary['total_sequences']}")
    print(f"   总检测数: {summary['total_detections']}")
    print(f"   总真实标注数: {summary['total_ground_truths']}")
    print(f"   总测试帧数: {summary['total_test_frames']}")
    print(f"   真正例: {overall_metrics['total_tp']}")
    print(f"   假正例: {overall_metrics['total_fp']}")
    print(f"   假负例: {overall_metrics['total_fn']}")
    
    print(f"\n📋 各序列表现:")
    for seq_id, seq_result in complete_results['sequence_results'].items():
        metrics = seq_result['metrics']
        print(f"   {seq_id}: P={metrics['precision']:.3f}, R={metrics['recall']:.3f}, F1={metrics['f1_score']:.3f}")
    
    print(f"\n📁 完整结果已保存到: complete_evaluation_results.json")
    
    print(f"\n🎉 完整的检测和评估系统完成！")
    print(f"✅ 按照您的构想：每隔10帧抽取1帧+线性插值")
    print(f"✅ 包含所有8个序列的完整评估")
    print(f"✅ 详细的评估指标和统计信息")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
