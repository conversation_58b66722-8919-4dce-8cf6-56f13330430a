#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
帧级微调训练器 - 真正的732个独立帧样本训练
"""

import os
import sys
import json
import torch
import logging
from pathlib import Path
from typing import List, Dict, Any
from PIL import Image
from torch.utils.data import Dataset, DataLoader
from transformers import (
    Qwen2_5_VLForConditionalGeneration,
    AutoProcessor,
    TrainingArguments,
    Trainer,
)
from peft import LoraConfig, get_peft_model, TaskType
from qwen_vl_utils import process_vision_info
import numpy as np

# 固定使用GPU卡2
os.environ['CUDA_VISIBLE_DEVICES'] = '2'

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def generate_frame_level_training_data():
    """生成真正的帧级训练数据 - 每帧一个样本"""
    from video_sequence_trainer import VideoSequenceTrainer
    
    logger.info("生成帧级训练数据...")
    
    trainer = VideoSequenceTrainer("dataset/images", "dataset/labels", "dataset/class.json")
    sequences = ['data01', 'data02', 'data04', 'data06', 'data07', 'data19', 'data23', 'data26']
    
    all_frame_samples = []
    
    for seq_id in sequences:
        print(f"\n处理序列 {seq_id}...")
        
        # 获取序列信息
        seq_info = trainer.sequences[seq_id]
        images_dir = Path(f"dataset/images/{seq_id}")
        
        # 获取图像文件
        if seq_info['naming'] == 'simple':
            image_files = trainer.get_simple_naming_files(images_dir, seq_info['format'])
        else:
            image_files = trainer.get_complex_naming_files(images_dir, seq_info['format'])
        
        total_frames = len(image_files)
        print(f"  总帧数: {total_frames}")
        
        # 每隔10帧抽取1帧
        frame_interval = 10
        sampled_indices = list(range(0, total_frames, frame_interval))
        
        print(f"  抽取帧数: {len(sampled_indices)} (每隔{frame_interval}帧)")
        
        # 为每一帧创建独立的训练样本
        for i, frame_idx in enumerate(sampled_indices):
            frame_file = image_files[frame_idx]
            frame_id = frame_file.stem
            
            # 加载该帧的标注
            annotations = trainer._load_frame_annotation(seq_id, frame_id, seq_info['size'])
            
            if annotations:
                # 创建单帧训练样本
                frame_sample = {
                    "conversations": [
                        {
                            "from": "human",
                            "value": f"检测这张红外图像中的弱小目标。图像来自序列{seq_id}，帧ID为{frame_id}，尺寸为{seq_info['size']}。目标类别包括：drone, car, ship, bus, pedestrian, cyclist。请以JSON数组格式输出检测结果：[{{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"{frame_id}\"}}]"
                        },
                        {
                            "from": "gpt",
                            "value": json.dumps(annotations)
                        }
                    ],
                    "frame_info": {
                        "sequence_id": seq_id,
                        "frame_id": frame_id,
                        "frame_idx": frame_idx,
                        "file_path": str(frame_file),
                        "size": seq_info['size'],
                        "format": seq_info['format']
                    }
                }
                
                all_frame_samples.append(frame_sample)
        
        print(f"  ✅ 生成帧级样本: {len(sampled_indices)} 个")
    
    # 保存帧级训练数据
    output_path = "data/frame_level_training_data.json"
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(all_frame_samples, f, ensure_ascii=False, indent=2)
    
    logger.info(f"帧级训练数据保存到: {output_path}")
    logger.info(f"总样本数: {len(all_frame_samples)}")
    
    return True

class FrameLevelDataset(Dataset):
    """帧级数据集 - 每个样本是一帧"""
    
    def __init__(self, data_path: str, processor):
        self.processor = processor
        with open(data_path, 'r', encoding='utf-8') as f:
            self.data = json.load(f)
        logger.info(f"加载了 {len(self.data)} 个帧级训练样本")
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        sample = self.data[idx]
        
        # 加载单帧图像
        frame_info = sample['frame_info']
        try:
            image = Image.open(frame_info['file_path']).convert('RGB')
            # 调整图像大小
            image = image.resize((336, 336))
        except Exception as e:
            logger.warning(f"无法加载图像 {frame_info['file_path']}: {e}")
            return None
        
        # 构建消息
        conversations = sample['conversations']
        messages = [
            {
                "role": "user",
                "content": [
                    {"type": "image", "image": image},
                    {"type": "text", "text": conversations[0]['value']}
                ]
            },
            {
                "role": "assistant", 
                "content": conversations[1]['value']
            }
        ]
        
        return {
            'messages': messages,
            'frame_info': frame_info
        }

def frame_level_collate_fn(batch, processor):
    """帧级数据整理函数"""
    # 过滤None样本
    batch = [item for item in batch if item is not None]
    if not batch:
        return None
    
    try:
        example = batch[0]
        messages = example["messages"]
        
        # 应用聊天模板
        text = processor.apply_chat_template(
            messages, tokenize=False, add_generation_prompt=True
        )
        
        # 处理视觉信息
        image_inputs, video_inputs = process_vision_info(messages)
        
        # 处理输入
        inputs = processor(
            text=[text],
            images=image_inputs,
            videos=video_inputs,
            padding=True,
            return_tensors="pt"
        )
        
        # 检查输入是否有效
        if inputs["input_ids"].numel() == 0:
            logger.warning("生成的input_ids为空，跳过此样本")
            return None
        
        # 为训练添加labels
        inputs["labels"] = inputs["input_ids"].clone()
        
        return inputs
        
    except Exception as e:
        logger.warning(f"处理样本时出错: {e}")
        return None

class FrameLevelTrainer:
    """帧级训练器"""
    
    def __init__(self, model_path: str):
        self.model_path = model_path
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        
        logger.info(f"使用设备: {self.device}")
        logger.info(f"模型路径: {model_path}")
        
        # 加载模型和处理器
        self.processor = AutoProcessor.from_pretrained(model_path)
        self.model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
            model_path,
            torch_dtype=torch.float16,
            device_map="auto",
            trust_remote_code=True,
            low_cpu_mem_usage=True,
            use_cache=False
        )
        
        logger.info("模型和处理器加载完成")
    
    def setup_lora(self):
        """设置帧级LoRA配置"""
        config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            r=16,
            lora_alpha=32,
            target_modules=["q_proj", "v_proj"],
            lora_dropout=0.1,
        )
        
        self.model = get_peft_model(self.model, config)
        self.model.print_trainable_parameters()
        logger.info("帧级LoRA配置完成")
    
    def train(self, train_data_path: str, output_dir: str):
        """开始帧级训练"""
        logger.info("开始帧级训练...")
        
        # 创建数据集
        dataset = FrameLevelDataset(train_data_path, self.processor)
        
        # 创建数据加载器
        def collate_wrapper(batch):
            return frame_level_collate_fn(batch, self.processor)
        
        # 帧级训练参数
        args = TrainingArguments(
            output_dir=output_dir,
            num_train_epochs=3,  # 适中的训练轮数
            per_device_train_batch_size=1,
            gradient_accumulation_steps=4,
            learning_rate=2e-5,
            fp16=True,
            save_steps=50,
            logging_steps=10,
            save_total_limit=2,
            remove_unused_columns=False,
            dataloader_drop_last=True,
            dataloader_num_workers=0,
            skip_memory_metrics=True,
            max_grad_norm=1.0,
            warmup_steps=20,
            lr_scheduler_type="linear",
            weight_decay=0.01,
        )
        
        # 创建训练器
        trainer = Trainer(
            model=self.model,
            args=args,
            train_dataset=dataset,
            data_collator=collate_wrapper,
        )
        
        # 开始训练
        logger.info("开始帧级微调训练，预计时间：30-60分钟...")
        trainer.train()
        
        # 保存模型
        trainer.save_model()
        self.processor.save_pretrained(output_dir)
        
        logger.info(f"帧级训练完成，模型保存到: {output_dir}")
        return True

def main():
    """主函数"""
    print("=== 帧级视频序列微调训练系统 ===")
    print("🎯 目标: 使用732个独立帧样本进行真正的深度微调")
    print("📊 策略: 每帧一个样本，真正的大规模训练")
    print("🔧 技术: 帧级LoRA + 深度训练")
    print("⏱️  预计时间: 30-60分钟")
    
    # 第1步：生成帧级训练数据
    print("\n第1步：生成帧级训练数据...")
    if not generate_frame_level_training_data():
        print("❌ 帧级数据生成失败")
        return False
    
    # 第2步：初始化训练器
    print("\n第2步：初始化帧级训练器...")
    model_path = "qwen2.5-vl-7b"
    trainer = FrameLevelTrainer(model_path)
    
    # 第3步：设置LoRA
    print("\n第3步：设置帧级LoRA配置...")
    trainer.setup_lora()
    
    # 第4步：开始训练
    print("\n第4步：开始帧级微调训练...")
    output_dir = "output/frame_level_video_sequence_lora"
    success = trainer.train("data/frame_level_training_data.json", output_dir)
    
    if success:
        print("\n🎉 帧级微调训练完成！")
        print(f"📁 模型保存到: {output_dir}")
        print("📋 下一步: 使用帧级模型进行检测")
    else:
        print("\n❌ 帧级训练失败！")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
