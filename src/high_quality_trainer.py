#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高质量Qwen2.5-VL微调训练器
专注于提升检测精度和召回率，而不是数量
"""

import os
import sys
import json
import torch
import logging
from pathlib import Path
from typing import List, Dict, Any
from PIL import Image
from transformers import (
    Qwen2_5_VLForConditionalGeneration,
    AutoProcessor,
)
from peft import LoraConfig, get_peft_model, TaskType
import gc
import re
import random
import numpy as np

# 强制使用GPU卡2
os.environ['CUDA_VISIBLE_DEVICES'] = '2'
torch.cuda.set_device(0)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def clear_memory():
    """清理显存"""
    gc.collect()
    torch.cuda.empty_cache()

def get_image_files(seq_dir: Path, seq_info: Dict) -> List[Path]:
    """获取图像文件列表"""
    if seq_info['naming'] == 'simple':
        image_files = list(seq_dir.glob(f"*{seq_info['format']}"))
        return sorted(image_files, key=lambda x: int(x.stem))
    else:
        image_files = list(seq_dir.glob(f"*{seq_info['format']}"))
        def complex_sort_key(path):
            numbers = re.findall(r'(\d+)', path.name)
            return int(numbers[-1]) if numbers else 0
        return sorted(image_files, key=complex_sort_key)

def load_frame_annotation(seq_id, frame_id, size):
    """加载帧标注"""
    labels_dir = Path(f"dataset/labels/{seq_id}")
    label_file = labels_dir / f"{frame_id}.txt"
    
    if not label_file.exists():
        return []
    
    with open('dataset/class.json', 'r', encoding='utf-8') as f:
        class_map = json.load(f)
    
    annotations = []
    try:
        with open(label_file, 'r') as f:
            for line in f:
                parts = line.strip().split()
                if len(parts) >= 5:
                    class_id = int(parts[0])
                    x_center = float(parts[1])
                    y_center = float(parts[2])
                    width = float(parts[3])
                    height = float(parts[4])
                    
                    # 转换为绝对坐标
                    img_width, img_height = size
                    x1 = int((x_center - width/2) * img_width)
                    y1 = int((y_center - height/2) * img_height)
                    x2 = int((x_center + width/2) * img_width)
                    y2 = int((y_center + height/2) * img_height)
                    
                    # 确保坐标有效
                    x1 = max(0, min(x1, img_width))
                    y1 = max(0, min(y1, img_height))
                    x2 = max(x1, min(x2, img_width))
                    y2 = max(y1, min(y2, img_height))
                    
                    # 只保留有效的bbox
                    if x2 > x1 and y2 > y1:
                        class_name = class_map.get(str(class_id), "unknown")
                        annotations.append({
                            "bbox": [x1, y1, x2, y2],
                            "label": class_name,
                            "frame_id": frame_id
                        })
    except Exception as e:
        logger.warning(f"加载标注文件失败 {label_file}: {e}")
    
    return annotations

def generate_high_quality_training_data():
    """生成高质量训练数据 - 专注于质量而非数量"""
    logger.info("生成高质量训练数据...")
    
    # 只使用有丰富标注的序列
    sequences = {
        'data01': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
        'data02': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
        'data04': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
        'data06': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
        'data07': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
    }
    
    all_training_samples = []
    
    for seq_id, seq_info in sequences.items():
        print(f"处理序列 {seq_id}...")
        
        images_dir = Path(f"dataset/images/{seq_id}")
        image_files = get_image_files(images_dir, seq_info)
        
        # 策略1: 精选有标注的单帧样本
        annotated_frames = []
        for i in range(0, min(len(image_files), 300), 5):  # 每5帧检查一次
            frame_file = image_files[i]
            frame_id = frame_file.stem
            
            annotations = load_frame_annotation(seq_id, frame_id, seq_info['size'])
            if annotations:  # 只保留有标注的帧
                annotated_frames.append((frame_file, frame_id, annotations))
        
        print(f"  序列 {seq_id} 找到 {len(annotated_frames)} 个有标注的帧")
        
        # 为每个有标注的帧生成高质量训练样本
        for frame_file, frame_id, annotations in annotated_frames:
            # 高质量提示词 - 简洁但精确
            training_sample = {
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "image",
                                "image": str(frame_file)
                            },
                            {
                                "type": "text",
                                "text": f"""检测红外图像中的弱小目标。

目标特征：
- 尺寸小（5-20像素）
- 在红外图像中为亮点或暗点
- 类别：drone, car, ship, bus, pedestrian, cyclist

要求：
1. 精确定位每个目标
2. 边界框紧贴目标
3. 坐标格式[x1,y1,x2,y2]

输出JSON格式：
[{{"bbox":[x1,y1,x2,y2],"label":"类别"}}]

开始检测："""
                            }
                        ]
                    },
                    {
                        "role": "assistant",
                        "content": json.dumps(annotations, ensure_ascii=False)
                    }
                ]
            }
            
            all_training_samples.append(training_sample)
            print(f"  ✅ 生成单帧样本: 帧{frame_id}, {len(annotations)}个标注")
        
        # 策略2: 高质量序列样本 - 严格按照你的要求
        if len(annotated_frames) >= 5:
            # 选择有标注的帧组成序列
            for start_idx in range(0, len(annotated_frames)-4, 3):
                sequence_frames = annotated_frames[start_idx:start_idx+5]
                
                sequence_annotations = []
                sequence_frame_paths = []
                
                for frame_file, frame_id, annotations in sequence_frames:
                    sequence_frame_paths.append(str(frame_file))
                    for ann in annotations:
                        ann_copy = ann.copy()
                        ann_copy['frame_id'] = frame_id
                        sequence_annotations.append(ann_copy)
                
                if sequence_annotations:
                    content = []
                    for frame_path in sequence_frame_paths:
                        content.append({
                            "type": "image",
                            "image": frame_path
                        })
                    
                    content.append({
                        "type": "text",
                        "text": f"""检测视频序列中的弱小目标。

序列信息：
- 序列：{seq_id}
- 帧数：{len(sequence_frame_paths)}
- 抽取策略：选择有标注的帧

目标特征：
- 尺寸小（5-20像素）
- 类别：drone, car, ship, bus, pedestrian, cyclist

要求：
1. 检测每帧中的所有目标
2. 为每个检测标注frame_id
3. 保持检测一致性

输出JSON格式：
[{{"bbox":[x1,y1,x2,y2],"label":"类别","frame_id":"帧号"}}]

开始检测："""
                    })
                    
                    sequence_sample = {
                        "messages": [
                            {
                                "role": "user",
                                "content": content
                            },
                            {
                                "role": "assistant",
                                "content": json.dumps(sequence_annotations, ensure_ascii=False)
                            }
                        ]
                    }
                    
                    all_training_samples.append(sequence_sample)
                    print(f"  ✅ 生成序列样本: {len(sequence_frame_paths)}帧, {len(sequence_annotations)}个标注")
    
    # 保存高质量训练数据
    output_path = "data/high_quality_training_data.json"
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(all_training_samples, f, ensure_ascii=False, indent=2)
    
    logger.info(f"高质量训练数据保存到: {output_path}")
    logger.info(f"总训练样本数: {len(all_training_samples)}")
    
    return True

class HighQualityTrainer:
    """高质量训练器"""
    
    def __init__(self, model_path: str):
        self.model_path = model_path
        self.device = "cuda"
        
        clear_memory()
        
        logger.info("加载高质量模型...")
        
        self.processor = AutoProcessor.from_pretrained(model_path)
        self.model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
            model_path,
            torch_dtype=torch.float16,
            device_map="auto",
            trust_remote_code=True,
            low_cpu_mem_usage=True,
            use_cache=False,
        )
        
        logger.info("高质量模型加载完成")
    
    def setup_lora(self):
        """设置高质量LoRA - 平衡表达能力和稳定性"""
        config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            r=16,  # 适中的rank
            lora_alpha=32,
            target_modules=["q_proj", "v_proj", "k_proj", "o_proj"],  # 核心模块
            lora_dropout=0.1,
        )
        
        self.model = get_peft_model(self.model, config)
        self.model.print_trainable_parameters()
        logger.info("高质量LoRA配置完成")
    
    def high_quality_train(self, train_data_path: str, output_dir: str):
        """高质量训练"""
        logger.info("开始高质量微调...")
        
        clear_memory()
        
        # 设置LoRA
        self.setup_lora()
        
        # 加载训练数据
        with open(train_data_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 高质量训练参数
        optimizer = torch.optim.AdamW(
            self.model.parameters(), 
            lr=2e-5,  # 较低的学习率确保稳定
            weight_decay=0.01,
            betas=(0.9, 0.999),
            eps=1e-8
        )
        
        scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
            optimizer, 
            T_max=len(data)*6
        )
        
        # 训练循环
        self.model.train()
        
        for epoch in range(6):  # 适中的训练轮数
            logger.info(f"高质量训练轮次 {epoch + 1}/6")
            
            epoch_loss = 0
            valid_samples = 0
            
            for i, sample in enumerate(data):
                messages = sample["messages"]
                
                try:
                    # 处理输入
                    text = self.processor.apply_chat_template(
                        messages, tokenize=False, add_generation_prompt=True
                    )
                    
                    # 处理图像 - 保持合适分辨率
                    images = []
                    for content in messages[0]["content"]:
                        if content["type"] == "image":
                            img = Image.open(content["image"]).convert('RGB')
                            img.thumbnail((384, 384), Image.Resampling.LANCZOS)
                            images.append(img)
                    
                    if not images:
                        continue
                    
                    # 处理输入
                    inputs = self.processor(
                        text=[text],
                        images=images,
                        padding=True,
                        return_tensors="pt"
                    )
                    
                    # 移动到设备
                    inputs = {k: v.to(self.device) for k, v in inputs.items()}
                    inputs["labels"] = inputs["input_ids"].clone()
                    
                    # 前向传播
                    optimizer.zero_grad()
                    outputs = self.model(**inputs)
                    loss = outputs.loss
                    
                    # 反向传播
                    loss.backward()
                    torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                    optimizer.step()
                    scheduler.step()
                    
                    epoch_loss += loss.item()
                    valid_samples += 1
                    
                    if (i + 1) % 5 == 0:
                        logger.info(f"  样本 {i+1}/{len(data)}, 损失: {loss.item():.4f}, LR: {scheduler.get_last_lr()[0]:.6f}")
                    
                    clear_memory()
                    
                except Exception as e:
                    logger.warning(f"处理样本 {i} 时出错: {e}")
                    continue
            
            if valid_samples > 0:
                avg_loss = epoch_loss / valid_samples
                logger.info(f"轮次 {epoch + 1} 平均损失: {avg_loss:.4f}")
        
        # 保存模型
        os.makedirs(output_dir, exist_ok=True)
        self.model.save_pretrained(output_dir)
        self.processor.save_pretrained(output_dir)
        
        logger.info(f"高质量微调完成，模型保存到: {output_dir}")
        return True

def main():
    """主函数"""
    print("=== 高质量Qwen2.5-VL微调训练系统 ===")
    print("🎯 目标: 真正提升检测精度和召回率")
    print("📊 策略: 高质量标注数据+精确训练+质量验证")
    print("🎬 训练方式: 精确LoRA微调")
    print("🔧 技术: 精确LoRA + 高质量标注 + 稳定训练")
    print("⏱️  预计时间: 20-30分钟")
    
    # 第1步：生成训练数据
    print("\n第1步：生成高质量训练数据...")
    if not generate_high_quality_training_data():
        print("❌ 训练数据生成失败")
        return False
    
    # 第2步：初始化训练器
    print("\n第2步：初始化高质量训练器...")
    model_path = "qwen2.5-vl-7b"
    trainer = HighQualityTrainer(model_path)
    
    # 第3步：开始训练
    print("\n第3步：开始高质量微调...")
    output_dir = "output/high_quality_qwen_finetuned"
    success = trainer.high_quality_train("data/high_quality_training_data.json", output_dir)
    
    if success:
        print("\n🎉 高质量微调完成！")
        print(f"📁 模型保存到: {output_dir}")
        print("📋 这是高质量版本，专注于检测精度")
        print("💡 应该有更好的精度和召回率")
    else:
        print("\n❌ 高质量微调失败！")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
