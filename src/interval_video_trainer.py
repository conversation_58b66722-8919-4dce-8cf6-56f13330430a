#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
间隔视频序列微调训练器
按照用户思路：每隔10帧取1帧组成视频序列进行微调
例如：1,11,21,31,41 和 51,61,71,81,91
"""

import os
import sys
import json
import random
import logging
from pathlib import Path
from typing import List, Dict, Any, Tuple
from PIL import Image, ImageEnhance
import torch
from transformers import (
    Qwen2_5_VLForConditionalGeneration, 
    AutoProcessor,
    TrainingArguments,
    Trainer
)
from peft import LoraConfig, get_peft_model, TaskType
from torch.utils.data import Dataset
import re

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class IntervalVideoTrainer:
    """间隔视频序列微调训练器"""
    
    def __init__(self):
        self.sequences = {
            'data01': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data02': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data04': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data06': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data07': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data19': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
            'data23': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
            'data26': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'}
        }
        
        # 加载类别映射
        with open('dataset/class.json', 'r', encoding='utf-8') as f:
            self.class_map = json.load(f)
    
    def get_image_files(self, seq_dir: Path, seq_info: Dict) -> List[Path]:
        """获取图像文件列表"""
        if seq_info['naming'] == 'simple':
            image_files = list(seq_dir.glob(f"*{seq_info['format']}"))
            return sorted(image_files, key=lambda x: int(x.stem))
        else:
            image_files = list(seq_dir.glob(f"*{seq_info['format']}"))
            def complex_sort_key(path):
                numbers = re.findall(r'(\d+)', path.name)
                return int(numbers[-1]) if numbers else 0
            return sorted(image_files, key=complex_sort_key)
    
    def create_interval_video_sequences(self, seq_id: str, sequence_length: int = 5, frame_interval: int = 10) -> List[Dict]:
        """创建间隔视频序列 - 按照用户思路每隔10帧取1帧"""
        seq_info = self.sequences[seq_id]
        seq_dir = Path(f"dataset/images/{seq_id}")
        labels_dir = Path(f"dataset/labels/{seq_id}")
        
        if not seq_dir.exists() or not labels_dir.exists():
            logger.warning(f"序列 {seq_id} 目录不存在")
            return []
        
        image_files = self.get_image_files(seq_dir, seq_info)
        video_sequences = []
        
        # 按照用户思路：1,11,21,31,41 然后 51,61,71,81,91
        # 每隔10帧取1帧，组成长度为5的视频序列
        
        # 计算可以创建多少个完整的视频序列
        max_frame_needed = (sequence_length - 1) * frame_interval
        
        logger.info(f"序列 {seq_id}: 总帧数 {len(image_files)}")
        
        # 滑动窗口，每次移动50帧（5*10）创建新的视频序列
        for start_idx in range(0, len(image_files) - max_frame_needed, frame_interval * sequence_length):
            # 创建一个视频序列：例如 1,11,21,31,41
            video_frames = []
            valid_sequence = True
            frame_indices = []
            
            for i in range(sequence_length):
                frame_idx = start_idx + i * frame_interval
                
                if frame_idx >= len(image_files):
                    valid_sequence = False
                    break
                
                frame_indices.append(frame_idx)
                img_file = image_files[frame_idx]
                
                # 加载该帧的标注
                if seq_info['naming'] == 'simple':
                    label_file = labels_dir / f"{img_file.stem}.txt"
                else:
                    label_file = labels_dir / f"{img_file.stem}.txt"
                
                frame_annotations = []
                if label_file.exists():
                    try:
                        with open(label_file, 'r') as f:
                            lines = f.readlines()
                        
                        for line in lines:
                            parts = line.strip().split()
                            if len(parts) >= 5:
                                class_id = int(parts[0])
                                x_center = float(parts[1])
                                y_center = float(parts[2])
                                width = float(parts[3])
                                height = float(parts[4])
                                
                                # 转换为绝对坐标
                                img_width, img_height = seq_info['size']
                                x1 = int((x_center - width/2) * img_width)
                                y1 = int((y_center - height/2) * img_height)
                                x2 = int((x_center + width/2) * img_width)
                                y2 = int((y_center + height/2) * img_height)
                                
                                # 验证弱小目标尺寸
                                bbox_width = x2 - x1
                                bbox_height = y2 - y1
                                
                                if (bbox_width >= 3 and bbox_height >= 3 and 
                                    bbox_width <= 80 and bbox_height <= 80):
                                    
                                    class_name = self.class_map.get(str(class_id), "drone")
                                    frame_annotations.append({
                                        "bbox": [x1, y1, x2, y2],
                                        "label": class_name
                                    })
                    
                    except Exception as e:
                        logger.warning(f"加载标注失败 {label_file}: {e}")
                
                video_frames.append({
                    "frame_idx": frame_idx,
                    "global_frame_id": frame_idx,
                    "sequence_frame_id": i,
                    "image_path": str(img_file),
                    "annotations": frame_annotations
                })
            
            if valid_sequence and len(video_frames) == sequence_length:
                # 检查是否至少有一帧有标注
                has_annotations = any(len(frame['annotations']) > 0 for frame in video_frames)
                
                if has_annotations:
                    video_sequences.append({
                        "sequence_id": seq_id,
                        "video_start_frame": start_idx,
                        "frame_interval": frame_interval,
                        "video_frames": video_frames,
                        "frame_indices": frame_indices
                    })
        
        logger.info(f"序列 {seq_id}: 创建了 {len(video_sequences)} 个间隔视频序列")
        logger.info(f"  每个视频序列: {sequence_length} 帧，间隔 {frame_interval} 帧")
        logger.info(f"  示例帧索引: {video_sequences[0]['frame_indices'] if video_sequences else '无'}")
        
        return video_sequences
    
    def split_interval_video_data(self, test_ratio: float = 0.2) -> Tuple[List[Dict], List[Dict]]:
        """按序列划分间隔视频数据"""
        logger.info("开始划分间隔视频序列训练数据...")
        
        all_sequences = list(self.sequences.keys())
        random.shuffle(all_sequences)
        
        # 按序列划分
        num_test_sequences = max(1, int(len(all_sequences) * test_ratio))
        test_sequences = all_sequences[:num_test_sequences]
        train_sequences = all_sequences[num_test_sequences:]
        
        logger.info(f"训练序列: {train_sequences}")
        logger.info(f"测试序列: {test_sequences}")
        
        # 加载间隔视频训练数据
        train_data = []
        for seq_id in train_sequences:
            seq_video_data = self.create_interval_video_sequences(seq_id, sequence_length=5, frame_interval=10)
            train_data.extend(seq_video_data)
        
        # 加载间隔视频测试数据
        test_data = []
        for seq_id in test_sequences:
            seq_video_data = self.create_interval_video_sequences(seq_id, sequence_length=5, frame_interval=10)
            test_data.extend(seq_video_data)
        
        logger.info(f"间隔视频训练数据: {len(train_data)} 个视频序列")
        logger.info(f"间隔视频测试数据: {len(test_data)} 个视频序列")
        
        return train_data, test_data
    
    def create_interval_video_training_samples(self, data: List[Dict]) -> List[Dict]:
        """创建间隔视频训练样本"""
        samples = []
        
        for item in data:
            seq_id = item["sequence_id"]
            video_frames = item["video_frames"]
            frame_indices = item["frame_indices"]
            seq_info = self.sequences[seq_id]
            
            # 构建间隔视频序列提示词
            video_prompt = f"""分析这个间隔采样的弱小目标检测视频序列。

序列信息：
- 序列ID: {seq_id}
- 图像尺寸: {seq_info['size']}
- 视频帧数: {len(video_frames)}
- 采样策略: 每隔10帧取1帧
- 帧索引: {frame_indices}
- 目标特征: 3-80像素的弱小目标

任务要求：
1. 检测每帧中的弱小目标
2. 理解目标在间隔帧中的运动轨迹
3. 预测中间帧的目标位置
4. 学习时序插值能力

输出格式：
{{
  "interval_detections": [
    {{"frame_id": {frame_indices[0]}, "detections": [{{"bbox":[x1,y1,x2,y2],"label":"类别"}}]}},
    {{"frame_id": {frame_indices[1]}, "detections": [{{"bbox":[x1,y1,x2,y2],"label":"类别"}}]}},
    {{"frame_id": {frame_indices[2]}, "detections": [{{"bbox":[x1,y1,x2,y2],"label":"类别"}}]}},
    {{"frame_id": {frame_indices[3]}, "detections": [{{"bbox":[x1,y1,x2,y2],"label":"类别"}}]}},
    {{"frame_id": {frame_indices[4]}, "detections": [{{"bbox":[x1,y1,x2,y2],"label":"类别"}}]}}
  ],
  "trajectories": [
    {{"track_id": 1, "key_frames": {frame_indices}, "label": "drone", "motion_type": "linear/curved"}}
  ],
  "interpolation_capability": "learned"
}}

开始分析间隔视频序列："""
            
            # 构建真实标注
            interval_detections = []
            trajectories = []
            track_id = 1
            
            for frame_data in video_frames:
                frame_id = frame_data["global_frame_id"]
                annotations = frame_data["annotations"]
                
                frame_detections = []
                for ann in annotations:
                    frame_detections.append({
                        "bbox": ann["bbox"],
                        "label": ann["label"]
                    })
                
                interval_detections.append({
                    "frame_id": frame_id,
                    "detections": frame_detections
                })
                
                # 构建轨迹
                for ann in annotations:
                    trajectories.append({
                        "track_id": track_id,
                        "key_frames": [frame_id],
                        "label": ann["label"],
                        "motion_type": "unknown"
                    })
                    track_id += 1
            
            ground_truth = {
                "interval_detections": interval_detections,
                "trajectories": trajectories,
                "interpolation_capability": "learned"
            }
            
            ground_truth_json = json.dumps(ground_truth, ensure_ascii=False)
            
            # 构建多图像输入（按时序顺序）
            content = []
            for frame_data in video_frames:
                content.append({
                    "type": "image",
                    "image": frame_data["image_path"]
                })
            
            content.append({
                "type": "text",
                "text": video_prompt
            })
            
            sample = {
                "messages": [
                    {
                        "role": "user",
                        "content": content
                    },
                    {
                        "role": "assistant",
                        "content": ground_truth_json
                    }
                ]
            }
            samples.append(sample)
        
        return samples
    
    def prepare_interval_video_training_data(self):
        """准备间隔视频序列训练数据"""
        logger.info("开始准备间隔视频序列训练数据...")
        
        # 设置随机种子
        random.seed(42)
        
        # 划分间隔视频数据
        train_data, test_data = self.split_interval_video_data(test_ratio=0.25)
        
        # 创建间隔视频训练样本
        train_samples = self.create_interval_video_training_samples(train_data)
        test_samples = self.create_interval_video_training_samples(test_data)
        
        logger.info(f"生成间隔视频训练样本: {len(train_samples)} 个")
        logger.info(f"生成间隔视频测试样本: {len(test_samples)} 个")
        
        # 保存间隔视频训练数据
        os.makedirs("data", exist_ok=True)
        
        with open("data/interval_video_train_data.json", 'w', encoding='utf-8') as f:
            json.dump(train_samples, f, ensure_ascii=False, indent=2)
        
        with open("data/interval_video_test_data.json", 'w', encoding='utf-8') as f:
            json.dump(test_samples, f, ensure_ascii=False, indent=2)
        
        logger.info("间隔视频序列训练数据已保存")
        logger.info(f"间隔视频训练数据: data/interval_video_train_data.json ({len(train_samples)} 样本)")
        logger.info(f"间隔视频测试数据: data/interval_video_test_data.json ({len(test_samples)} 样本)")
        
        return train_samples, test_samples

def main():
    """主函数"""
    print("=== 间隔视频序列弱小目标微调训练系统 ===")
    print("🎯 特点: 每隔10帧取1帧组成视频序列")
    print("📊 策略: 1,11,21,31,41 和 51,61,71,81,91")
    print("🔧 技术: 间隔采样 + 时序插值学习")
    print("💡 完全按照用户思路实现")
    
    # 创建间隔视频序列训练器
    trainer = IntervalVideoTrainer()
    
    # 准备间隔视频序列训练数据
    train_samples, test_samples = trainer.prepare_interval_video_training_data()
    
    print(f"\n✅ 间隔视频序列数据准备完成:")
    print(f"   间隔视频训练样本: {len(train_samples)}")
    print(f"   间隔视频测试样本: {len(test_samples)}")
    print(f"   每个视频序列: 5帧 (间隔10帧)")
    print(f"   学习内容: 间隔检测 + 时序插值")
    
    print("\n💡 下一步: 运行间隔视频LoRA训练器")
    print("🎯 这正是用户要求的：每隔10帧取1帧的视频序列微调")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
