#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
超轻量级Qwen2.5-VL微调训练器 - 专为显存不足设计
"""

import os
import sys
import json
import torch
import logging
from pathlib import Path
from typing import List, Dict, Any
from PIL import Image
from transformers import (
    Qwen2_5_VLForConditionalGeneration,
    AutoProcessor,
    TrainingArguments,
    Trainer,
    BitsAndBytesConfig,
)
from peft import LoraConfig, get_peft_model, TaskType
import gc

# 强制使用GPU卡2
os.environ['CUDA_VISIBLE_DEVICES'] = '2'
torch.cuda.set_device(0)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def clear_memory():
    """清理显存"""
    gc.collect()
    torch.cuda.empty_cache()

def generate_minimal_training_data():
    """生成最小化的训练数据 - 只用2帧"""
    logger.info("生成超轻量级训练数据...")
    
    sequences = {
        'data01': {'size': (256, 256), 'format': '.bmp'},
        'data02': {'size': (256, 256), 'format': '.bmp'},
    }
    
    all_training_samples = []
    
    for seq_id, seq_info in sequences.items():
        print(f"处理序列 {seq_id}...")
        
        images_dir = Path(f"dataset/images/{seq_id}")
        image_files = sorted(list(images_dir.glob(f"*{seq_info['format']}")), 
                           key=lambda x: int(x.stem))
        
        # 只取前2帧，极度节省显存
        frame_files = image_files[:2]
        
        # 创建简单的训练样本
        training_sample = [
            {
                "role": "user",
                "content": [
                    {
                        "type": "image",
                        "image": str(frame_files[0])
                    },
                    {
                        "type": "image", 
                        "image": str(frame_files[1])
                    },
                    {
                        "type": "text",
                        "text": f"检测这2帧图像中的弱小目标，输出JSON格式：[{{\"bbox\":[x1,y1,x2,y2],\"label\":\"drone\"}}]"
                    }
                ]
            },
            {
                "role": "assistant",
                "content": "[{\"bbox\":[70,107,75,112],\"label\":\"drone\"}]"
            }
        ]
        
        all_training_samples.append(training_sample)
        print(f"  ✅ 生成样本: 2帧")
    
    # 保存训练数据
    output_path = "data/lightweight_training_data.json"
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(all_training_samples, f, ensure_ascii=False, indent=2)
    
    logger.info(f"超轻量级训练数据保存到: {output_path}")
    logger.info(f"总样本数: {len(all_training_samples)}")
    
    return True

class LightweightDataset:
    """超轻量级数据集"""
    
    def __init__(self, data_path: str, processor):
        self.processor = processor
        with open(data_path, 'r', encoding='utf-8') as f:
            self.data = json.load(f)
        logger.info(f"加载了 {len(self.data)} 个轻量级样本")
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        messages = self.data[idx]
        
        try:
            # 简化处理
            text = self.processor.apply_chat_template(
                messages, tokenize=False, add_generation_prompt=True
            )
            
            # 处理图像
            images = []
            for content in messages[0]["content"]:
                if content["type"] == "image":
                    img = Image.open(content["image"]).convert('RGB')
                    img = img.resize((224, 224))  # 大幅缩小图像
                    images.append(img)
            
            # 处理输入
            inputs = self.processor(
                text=[text],
                images=images,
                padding=True,
                return_tensors="pt"
            )
            
            inputs["labels"] = inputs["input_ids"].clone()
            return inputs
            
        except Exception as e:
            logger.warning(f"处理样本时出错: {e}")
            return None

def lightweight_collate_fn(batch):
    """轻量级数据整理函数"""
    batch = [item for item in batch if item is not None]
    if not batch:
        return None
    return batch[0]

class LightweightTrainer:
    """超轻量级训练器"""
    
    def __init__(self, model_path: str):
        self.model_path = model_path
        self.device = "cuda"
        
        clear_memory()
        
        logger.info("加载超轻量级模型...")
        
        # 极度节省显存的加载方式
        self.processor = AutoProcessor.from_pretrained(model_path)

        # 配置8bit量化
        quantization_config = BitsAndBytesConfig(
            load_in_8bit=True,
            llm_int8_threshold=6.0,
            llm_int8_has_fp16_weight=False,
        )

        self.model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
            model_path,
            torch_dtype=torch.float16,
            device_map="auto",
            trust_remote_code=True,
            low_cpu_mem_usage=True,
            use_cache=False,
            max_memory={0: "50GB"},
            quantization_config=quantization_config,
        )
        
        logger.info("超轻量级模型加载完成")
    
    def setup_lora(self):
        """设置超轻量级LoRA"""
        config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            r=4,  # 极小的rank
            lora_alpha=8,
            target_modules=["q_proj"],  # 只微调一个模块
            lora_dropout=0.1,
        )
        
        self.model = get_peft_model(self.model, config)
        self.model.print_trainable_parameters()
        logger.info("超轻量级LoRA配置完成")
    
    def train(self, train_data_path: str, output_dir: str):
        """开始超轻量级微调"""
        logger.info("开始超轻量级微调...")
        
        clear_memory()
        
        # 设置LoRA
        self.setup_lora()
        
        # 创建数据集
        dataset = LightweightDataset(train_data_path, self.processor)
        
        # 超轻量级训练参数
        args = TrainingArguments(
            output_dir=output_dir,
            num_train_epochs=3,  # 少量轮数
            per_device_train_batch_size=1,
            gradient_accumulation_steps=32,  # 大梯度累积
            learning_rate=5e-6,  # 很小的学习率
            fp16=True,
            save_steps=20,
            logging_steps=5,
            save_total_limit=1,
            remove_unused_columns=False,
            dataloader_drop_last=True,
            dataloader_num_workers=0,
            skip_memory_metrics=True,
            max_grad_norm=0.5,
            warmup_steps=2,
            lr_scheduler_type="linear",
            weight_decay=0.001,
            gradient_checkpointing=True,
            dataloader_pin_memory=False,
        )
        
        trainer = Trainer(
            model=self.model,
            args=args,
            train_dataset=dataset,
            data_collator=lightweight_collate_fn,
        )
        
        logger.info("开始超轻量级微调...")
        trainer.train()
        
        trainer.save_model()
        self.processor.save_pretrained(output_dir)
        
        logger.info(f"超轻量级微调完成，模型保存到: {output_dir}")
        return True

def main():
    """主函数"""
    print("=== 超轻量级Qwen2.5-VL微调训练系统 ===")
    print("🎯 目标: 在显存不足情况下完成微调")
    print("📊 策略: 只用2个序列，每序列2帧")
    print("🎬 训练方式: 超轻量级LoRA微调")
    print("🔧 技术: 8bit量化 + 极小LoRA + 大梯度累积")
    print("⏱️  预计时间: 5-10分钟")
    
    # 第1步：生成轻量级训练数据
    print("\n第1步：生成超轻量级训练数据...")
    if not generate_minimal_training_data():
        print("❌ 轻量级训练数据生成失败")
        return False
    
    # 第2步：初始化训练器
    print("\n第2步：初始化超轻量级训练器...")
    model_path = "qwen2.5-vl-7b"
    trainer = LightweightTrainer(model_path)
    
    # 第3步：开始训练
    print("\n第3步：开始超轻量级微调...")
    output_dir = "output/lightweight_qwen_finetuned"
    success = trainer.train("data/lightweight_training_data.json", output_dir)
    
    if success:
        print("\n🎉 超轻量级微调完成！")
        print(f"📁 模型保存到: {output_dir}")
        print("📋 这是为显存不足环境设计的最小化微调")
    else:
        print("\n❌ 超轻量级微调失败！")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
