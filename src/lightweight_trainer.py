#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
轻量级真实训练器 - 使用较短的视频序列进行真实训练
"""

import os
import sys
import json
import torch
import logging
from pathlib import Path
from typing import List, Dict, Any
from PIL import Image
from torch.utils.data import Dataset, DataLoader
from transformers import (
    Qwen2_5_VLForConditionalGeneration,
    AutoProcessor,
    TrainingArguments,
    Trainer,
)
from peft import LoraConfig, get_peft_model, TaskType
from qwen_vl_utils import process_vision_info
import numpy as np

# 固定使用GPU卡2
os.environ['CUDA_VISIBLE_DEVICES'] = '2'

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def generate_lightweight_training_data():
    """生成轻量级训练数据 - 每个序列只取5帧"""
    from video_sequence_trainer import generate_all_sequences_training_data

    logger.info("生成轻量级训练数据...")

    # 生成轻量级训练数据，每个序列最多5帧
    all_training_data = generate_all_sequences_training_data(max_frames=5)

    if not all_training_data:
        logger.error("轻量级训练数据生成失败")
        return False

    # 保存轻量级训练数据
    output_path = "data/lightweight_training_data.json"
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(all_training_data, f, ensure_ascii=False, indent=2)

    logger.info(f"轻量级训练数据保存到: {output_path}")
    logger.info(f"总样本数: {len(all_training_data)}")

    return True

class LightweightDataset(Dataset):
    """轻量级数据集"""
    
    def __init__(self, data_path: str, processor):
        self.processor = processor
        with open(data_path, 'r', encoding='utf-8') as f:
            self.data = json.load(f)
        logger.info(f"加载了 {len(self.data)} 个轻量级训练样本")
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        sample = self.data[idx]
        
        # 只加载前3帧，进一步减少内存使用
        frame_infos = sample['sequence_info']['video_frames'][:3]
        video_frames = []
        
        for frame_info in frame_infos:
            try:
                image = Image.open(frame_info['file_path']).convert('RGB')
                # 调整图像大小以减少内存使用
                image = image.resize((224, 224))
                video_frames.append(image)
            except Exception as e:
                logger.warning(f"无法加载图像 {frame_info['file_path']}: {e}")
                continue
        
        if not video_frames:
            return None
        
        # 构建简化的消息
        conversations = sample['conversations']
        messages = []
        
        for conv in conversations:
            if conv['from'] == 'human':
                # 简化提示词
                simplified_prompt = f"检测这{len(video_frames)}帧图像中的目标，输出JSON格式结果。"
                message = {
                    "role": "user",
                    "content": [
                        {"type": "video", "video": video_frames},
                        {"type": "text", "text": simplified_prompt}
                    ]
                }
            else:
                # 简化输出，只保留前3个检测
                import json as json_lib
                try:
                    detections = json_lib.loads(conv['value'])
                    simplified_detections = detections[:3]  # 只保留前3个
                    message = {
                        "role": "assistant", 
                        "content": json_lib.dumps(simplified_detections)
                    }
                except:
                    message = {
                        "role": "assistant", 
                        "content": conv['value'][:200]  # 截断长文本
                    }
            messages.append(message)
        
        return {
            'messages': messages,
            'sequence_info': sample['sequence_info']
        }

def lightweight_collate_fn(batch, processor):
    """轻量级数据整理函数"""
    # 过滤None样本
    batch = [item for item in batch if item is not None]
    if not batch:
        return None
    
    # 只处理第一个样本
    try:
        example = batch[0]
        messages = example["messages"]
        
        # 应用聊天模板
        text = processor.apply_chat_template(
            messages, tokenize=False, add_generation_prompt=True
        )
        
        # 截断过长的文本
        if len(text) > 2048:
            text = text[:2048]
        
        # 处理视觉信息
        image_inputs, video_inputs = process_vision_info(messages)
        
        # 处理输入
        inputs = processor(
            text=[text],
            images=image_inputs,
            videos=video_inputs,
            padding=True,
            return_tensors="pt",
            max_length=512,  # 限制最大长度
            truncation=True
        )

        # 为训练添加labels
        inputs["labels"] = inputs["input_ids"].clone()

        return inputs
        
    except Exception as e:
        logger.warning(f"处理样本时出错: {e}")
        return None

class LightweightTrainer:
    """轻量级训练器"""
    
    def __init__(self, model_path: str):
        self.model_path = model_path
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        
        logger.info(f"使用设备: {self.device}")
        logger.info(f"模型路径: {model_path}")
        
        # 加载模型和处理器
        self.processor = AutoProcessor.from_pretrained(model_path)
        self.model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
            model_path,
            torch_dtype=torch.float16,
            device_map="auto",
            trust_remote_code=True,
            low_cpu_mem_usage=True,
            use_cache=False
        )
        
        logger.info("模型和处理器加载完成")
    
    def setup_lora(self):
        """设置轻量级LoRA配置"""
        config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            r=8,  # 减少rank
            lora_alpha=16,  # 减少alpha
            target_modules=["q_proj", "v_proj"],
            lora_dropout=0.1,
        )
        
        self.model = get_peft_model(self.model, config)
        self.model.print_trainable_parameters()
        logger.info("轻量级LoRA配置完成")
    
    def train(self, train_data_path: str, output_dir: str):
        """开始轻量级训练"""
        logger.info("开始轻量级训练...")
        
        # 创建数据集
        dataset = LightweightDataset(train_data_path, self.processor)
        
        # 创建数据加载器
        def collate_wrapper(batch):
            return lightweight_collate_fn(batch, self.processor)
        
        # 轻量级训练参数
        args = TrainingArguments(
            output_dir=output_dir,
            num_train_epochs=2,  # 减少训练轮数
            per_device_train_batch_size=1,
            gradient_accumulation_steps=4,
            learning_rate=1e-4,  # 较高的学习率
            fp16=True,
            save_steps=20,
            logging_steps=5,
            save_total_limit=1,
            remove_unused_columns=False,
            dataloader_drop_last=True,
            max_grad_norm=1.0,
            warmup_steps=10
        )
        
        # 创建训练器
        trainer = Trainer(
            model=self.model,
            args=args,
            train_dataset=dataset,
            data_collator=collate_wrapper,
        )
        
        # 开始训练
        trainer.train()
        
        # 保存模型
        trainer.save_model()
        self.processor.save_pretrained(output_dir)
        
        logger.info(f"轻量级训练完成，模型保存到: {output_dir}")
        return True

def main():
    """主函数"""
    print("=== 轻量级真实视频序列微调训练 ===")
    print("🎯 目标: 使用真实Qwen2.5-VL进行轻量级LoRA微调")
    print("📊 策略: 每个序列5帧，每帧3个检测")
    print("🔧 技术: 轻量级LoRA + 内存优化")
    
    # 第1步：生成轻量级训练数据
    print("\n第1步：生成轻量级训练数据...")
    if not generate_lightweight_training_data():
        print("❌ 轻量级数据生成失败")
        return False
    
    # 第2步：初始化训练器
    print("\n第2步：初始化轻量级训练器...")
    model_path = "qwen2.5-vl-7b"
    trainer = LightweightTrainer(model_path)
    
    # 第3步：设置LoRA
    print("\n第3步：设置轻量级LoRA配置...")
    trainer.setup_lora()
    
    # 第4步：开始训练
    print("\n第4步：开始轻量级微调训练...")
    output_dir = "output/lightweight_video_sequence_lora"
    success = trainer.train("data/lightweight_training_data.json", output_dir)
    
    if success:
        print("\n🎉 轻量级微调训练完成！")
        print(f"📁 模型保存到: {output_dir}")
        print("📋 下一步: 使用轻量级模型进行检测")
    else:
        print("\n❌ 轻量级训练失败！")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
