#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最小化Qwen2.5-VL微调训练器 - 专为显存不足设计
只做最基本的微调验证
"""

import os
import sys
import json
import torch
import logging
from pathlib import Path
from PIL import Image
from transformers import (
    Qwen2_5_VLForConditionalGeneration,
    AutoProcessor,
)
from peft import LoraConfig, get_peft_model, TaskType
import gc

# 强制使用GPU卡2
os.environ['CUDA_VISIBLE_DEVICES'] = '2'
torch.cuda.set_device(0)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def clear_memory():
    """清理显存"""
    gc.collect()
    torch.cuda.empty_cache()

def generate_simple_training_data():
    """生成最简单的训练数据"""
    logger.info("生成最简单的训练数据...")
    
    # 只用一个序列的前2帧
    seq_id = 'data01'
    images_dir = Path(f"dataset/images/{seq_id}")
    image_files = sorted(list(images_dir.glob("*.bmp")), key=lambda x: int(x.stem))
    
    # 创建最简单的训练样本
    training_sample = {
        "messages": [
            {
                "role": "user",
                "content": [
                    {
                        "type": "image",
                        "image": str(image_files[0])
                    },
                    {
                        "type": "text",
                        "text": "检测这张图像中的弱小目标，输出JSON格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"drone\"}]"
                    }
                ]
            },
            {
                "role": "assistant",
                "content": "[{\"bbox\":[70,107,75,112],\"label\":\"drone\"}]"
            }
        ]
    }
    
    # 保存训练数据
    output_path = "data/minimal_training_data.json"
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump([training_sample], f, ensure_ascii=False, indent=2)
    
    logger.info(f"最简单训练数据保存到: {output_path}")
    return True

class MinimalTrainer:
    """最小化训练器"""
    
    def __init__(self, model_path: str):
        self.model_path = model_path
        self.device = "cuda"
        
        clear_memory()
        
        logger.info("加载最小化模型...")
        
        # 最简单的加载方式
        self.processor = AutoProcessor.from_pretrained(model_path)
        self.model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
            model_path,
            torch_dtype=torch.float16,
            device_map="auto",
            trust_remote_code=True,
            low_cpu_mem_usage=True,
            use_cache=False,
        )
        
        logger.info("最小化模型加载完成")
    
    def setup_lora(self):
        """设置最小LoRA"""
        config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            r=2,  # 最小的rank
            lora_alpha=4,
            target_modules=["q_proj"],  # 只微调一个模块
            lora_dropout=0.1,
        )
        
        self.model = get_peft_model(self.model, config)
        self.model.print_trainable_parameters()
        logger.info("最小LoRA配置完成")
    
    def simple_train(self, train_data_path: str, output_dir: str):
        """最简单的训练"""
        logger.info("开始最简单的微调...")
        
        clear_memory()
        
        # 设置LoRA
        self.setup_lora()
        
        # 加载训练数据
        with open(train_data_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        sample = data[0]
        messages = sample["messages"]
        
        # 处理输入
        text = self.processor.apply_chat_template(
            messages, tokenize=False, add_generation_prompt=True
        )
        
        # 加载图像
        image_path = messages[0]["content"][0]["image"]
        image = Image.open(image_path).convert('RGB')
        image = image.resize((224, 224))
        
        # 处理输入
        inputs = self.processor(
            text=[text],
            images=[image],
            padding=True,
            return_tensors="pt"
        )
        
        # 移动到设备
        inputs = {k: v.to(self.device) for k, v in inputs.items()}
        inputs["labels"] = inputs["input_ids"].clone()
        
        # 设置优化器
        optimizer = torch.optim.AdamW(self.model.parameters(), lr=1e-5)
        
        # 简单训练循环
        self.model.train()
        
        for epoch in range(3):
            logger.info(f"训练轮次 {epoch + 1}/3")
            
            optimizer.zero_grad()
            
            outputs = self.model(**inputs)
            loss = outputs.loss
            
            logger.info(f"损失: {loss.item():.4f}")
            
            loss.backward()
            optimizer.step()
            
            clear_memory()
        
        # 保存模型
        os.makedirs(output_dir, exist_ok=True)
        self.model.save_pretrained(output_dir)
        self.processor.save_pretrained(output_dir)
        
        logger.info(f"最简单微调完成，模型保存到: {output_dir}")
        return True

def main():
    """主函数"""
    print("=== 最小化Qwen2.5-VL微调训练系统 ===")
    print("🎯 目标: 在显存不足情况下完成最基本的微调验证")
    print("📊 策略: 只用1个样本，1张图像")
    print("🎬 训练方式: 最小LoRA微调")
    print("🔧 技术: 最小rank + 简单训练循环")
    print("⏱️  预计时间: 2-5分钟")
    
    # 第1步：生成简单训练数据
    print("\n第1步：生成最简单训练数据...")
    if not generate_simple_training_data():
        print("❌ 简单训练数据生成失败")
        return False
    
    # 第2步：初始化训练器
    print("\n第2步：初始化最小化训练器...")
    model_path = "qwen2.5-vl-7b"
    trainer = MinimalTrainer(model_path)
    
    # 第3步：开始训练
    print("\n第3步：开始最简单微调...")
    output_dir = "output/minimal_qwen_finetuned"
    success = trainer.simple_train("data/minimal_training_data.json", output_dir)
    
    if success:
        print("\n🎉 最小化微调完成！")
        print(f"📁 模型保存到: {output_dir}")
        print("📋 这是最基本的微调验证")
        print("💡 证明了微调流程可以工作")
    else:
        print("\n❌ 最小化微调失败！")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
