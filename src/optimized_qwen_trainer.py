#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的Qwen2.5-VL微调训练器 - 改进方案
增加训练数据量，优化LoRA配置，改进提示词
"""

import os
import sys
import json
import torch
import logging
from pathlib import Path
from typing import List, Dict, Any
from PIL import Image
from transformers import (
    Qwen2_5_VLForConditionalGeneration,
    AutoProcessor,
    TrainingArguments,
    Trainer,
    BitsAndBytesConfig,
)
from peft import LoraConfig, get_peft_model, TaskType
import gc
import re

# 强制使用GPU卡2
os.environ['CUDA_VISIBLE_DEVICES'] = '2'
torch.cuda.set_device(0)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def clear_memory():
    """清理显存"""
    gc.collect()
    torch.cuda.empty_cache()

def get_image_files(seq_dir: Path, seq_info: Dict) -> List[Path]:
    """获取图像文件列表"""
    if seq_info['naming'] == 'simple':
        image_files = list(seq_dir.glob(f"*{seq_info['format']}"))
        return sorted(image_files, key=lambda x: int(x.stem))
    else:
        image_files = list(seq_dir.glob(f"*{seq_info['format']}"))
        def complex_sort_key(path):
            numbers = re.findall(r'(\d+)', path.name)
            return int(numbers[-1]) if numbers else 0
        return sorted(image_files, key=complex_sort_key)

def load_frame_annotation(seq_id, frame_id, size):
    """加载帧标注"""
    labels_dir = Path(f"dataset/labels/{seq_id}")
    label_file = labels_dir / f"{frame_id}.txt"
    
    if not label_file.exists():
        return []
    
    with open('dataset/class.json', 'r', encoding='utf-8') as f:
        class_map = json.load(f)
    
    annotations = []
    try:
        with open(label_file, 'r') as f:
            for line in f:
                parts = line.strip().split()
                if len(parts) >= 5:
                    class_id = int(parts[0])
                    x_center = float(parts[1])
                    y_center = float(parts[2])
                    width = float(parts[3])
                    height = float(parts[4])
                    
                    # 转换为绝对坐标
                    img_width, img_height = size
                    x1 = int((x_center - width/2) * img_width)
                    y1 = int((y_center - height/2) * img_height)
                    x2 = int((x_center + width/2) * img_width)
                    y2 = int((y_center + height/2) * img_height)
                    
                    class_name = class_map.get(str(class_id), "unknown")
                    
                    annotations.append({
                        "bbox": [x1, y1, x2, y2],
                        "label": class_name,
                        "frame_id": frame_id
                    })
    except Exception as e:
        logger.warning(f"加载标注文件失败 {label_file}: {e}")
    
    return annotations

def generate_optimized_training_data():
    """生成优化的训练数据 - 增加数据量和质量"""
    logger.info("生成优化的训练数据...")
    
    sequences = {
        'data01': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
        'data02': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
        'data04': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
        'data06': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
    }
    
    all_training_samples = []
    
    for seq_id, seq_info in sequences.items():
        print(f"处理序列 {seq_id}...")
        
        images_dir = Path(f"dataset/images/{seq_id}")
        image_files = get_image_files(images_dir, seq_info)
        
        # 每个序列生成多个训练样本
        total_frames = len(image_files)
        
        # 策略1: 单帧检测样本 - 选择有标注的帧
        for i in range(0, min(total_frames, 100), 20):  # 每20帧选一个
            frame_file = image_files[i]
            frame_id = frame_file.stem
            
            # 加载标注
            annotations = load_frame_annotation(seq_id, frame_id, seq_info['size'])
            
            if annotations:  # 只有有标注的帧才生成训练样本
                # 优化的提示词
                training_sample = [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "image",
                                "image": str(frame_file)
                            },
                            {
                                "type": "text",
                                "text": f"""你是专业的红外弱小目标检测专家。请检测这张红外图像中的弱小目标。

图像信息：
- 序列：{seq_id}
- 帧号：{frame_id}
- 尺寸：{seq_info['size']}

弱小目标特征：
- 尺寸很小（通常5-20像素）
- 在红外图像中呈现为亮点或暗点
- 可能是点状、小块状或小矩形
- 目标类别：drone, car, ship, bus, pedestrian, cyclist

检测要求：
1. 仔细观察图像中的小亮点或异常区域
2. 边界框要紧贴目标，不要过大
3. 坐标必须是整数
4. 如果没有目标，输出空数组[]

输出格式（严格JSON）：
[{{"bbox":[x1,y1,x2,y2],"label":"类别"}}]

开始检测："""
                            }
                        ]
                    },
                    {
                        "role": "assistant",
                        "content": json.dumps(annotations, ensure_ascii=False)
                    }
                ]
                
                all_training_samples.append(training_sample)
                print(f"  ✅ 生成单帧样本: 帧{frame_id}, {len(annotations)}个标注")
        
        # 策略2: 双帧对比样本 - 增强时序理解
        for i in range(0, min(total_frames-10, 80), 30):
            frame1_file = image_files[i]
            frame2_file = image_files[i+10]
            frame1_id = frame1_file.stem
            frame2_id = frame2_file.stem
            
            annotations1 = load_frame_annotation(seq_id, frame1_id, seq_info['size'])
            annotations2 = load_frame_annotation(seq_id, frame2_id, seq_info['size'])
            
            if annotations1 or annotations2:
                # 合并标注
                all_annotations = []
                for ann in annotations1:
                    ann_copy = ann.copy()
                    ann_copy['frame_id'] = frame1_id
                    all_annotations.append(ann_copy)
                for ann in annotations2:
                    ann_copy = ann.copy()
                    ann_copy['frame_id'] = frame2_id
                    all_annotations.append(ann_copy)
                
                training_sample = [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "image",
                                "image": str(frame1_file)
                            },
                            {
                                "type": "image",
                                "image": str(frame2_file)
                            },
                            {
                                "type": "text",
                                "text": f"""你是专业的红外弱小目标检测专家。请检测这两张连续红外图像中的弱小目标。

图像信息：
- 序列：{seq_id}
- 第1帧：{frame1_id}
- 第2帧：{frame2_id}
- 尺寸：{seq_info['size']}

弱小目标特征：
- 尺寸很小（通常5-20像素）
- 在红外图像中呈现为亮点或暗点
- 目标类别：drone, car, ship, bus, pedestrian, cyclist

检测要求：
1. 分别检测两张图像中的所有弱小目标
2. 边界框要小而精确
3. 为每个检测结果标注对应的frame_id

输出格式（严格JSON）：
[{{"bbox":[x1,y1,x2,y2],"label":"类别","frame_id":"帧号"}}]

开始检测："""
                            }
                        ]
                    },
                    {
                        "role": "assistant",
                        "content": json.dumps(all_annotations, ensure_ascii=False)
                    }
                ]
                
                all_training_samples.append(training_sample)
                print(f"  ✅ 生成双帧样本: 帧{frame1_id}-{frame2_id}, {len(all_annotations)}个标注")
    
    # 保存优化的训练数据
    output_path = "data/optimized_training_data.json"
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(all_training_samples, f, ensure_ascii=False, indent=2)
    
    logger.info(f"优化训练数据保存到: {output_path}")
    logger.info(f"总训练样本数: {len(all_training_samples)}")
    
    return True

class OptimizedDataset:
    """优化的数据集"""
    
    def __init__(self, data_path: str, processor):
        self.processor = processor
        with open(data_path, 'r', encoding='utf-8') as f:
            self.data = json.load(f)
        logger.info(f"加载了 {len(self.data)} 个优化训练样本")
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        messages = self.data[idx]
        
        try:
            # 应用聊天模板
            text = self.processor.apply_chat_template(
                messages, tokenize=False, add_generation_prompt=True
            )
            
            # 处理图像 - 保持更高分辨率
            images = []
            for content in messages[0]["content"]:
                if content["type"] == "image":
                    img = Image.open(content["image"]).convert('RGB')
                    # 保持原始比例，但限制最大尺寸
                    img.thumbnail((336, 336), Image.Resampling.LANCZOS)
                    images.append(img)
            
            # 处理输入
            inputs = self.processor(
                text=[text],
                images=images,
                padding=True,
                return_tensors="pt"
            )
            
            inputs["labels"] = inputs["input_ids"].clone()
            return inputs
            
        except Exception as e:
            logger.warning(f"处理样本时出错: {e}")
            return None

def optimized_collate_fn(batch):
    """优化的数据整理函数"""
    batch = [item for item in batch if item is not None]
    if not batch:
        return None
    return batch[0]

class OptimizedTrainer:
    """优化的训练器"""
    
    def __init__(self, model_path: str):
        self.model_path = model_path
        self.device = "cuda"
        
        clear_memory()
        
        logger.info("加载优化模型...")
        
        # 优化的量化配置
        quantization_config = BitsAndBytesConfig(
            load_in_8bit=True,
            llm_int8_threshold=6.0,
            llm_int8_has_fp16_weight=False,
        )
        
        self.processor = AutoProcessor.from_pretrained(model_path)
        self.model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
            model_path,
            torch_dtype=torch.float16,
            device_map="auto",
            trust_remote_code=True,
            low_cpu_mem_usage=True,
            use_cache=False,
            max_memory={0: "60GB"},
            quantization_config=quantization_config,
        )
        
        logger.info("优化模型加载完成")
    
    def setup_lora(self):
        """设置优化的LoRA"""
        config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            r=8,  # 增加rank提高表达能力
            lora_alpha=16,
            target_modules=["q_proj", "v_proj", "k_proj", "o_proj"],  # 更多目标模块
            lora_dropout=0.1,
        )
        
        self.model = get_peft_model(self.model, config)
        self.model.print_trainable_parameters()
        logger.info("优化LoRA配置完成")
    
    def train(self, train_data_path: str, output_dir: str):
        """开始优化微调"""
        logger.info("开始优化微调...")
        
        clear_memory()
        
        # 设置LoRA
        self.setup_lora()
        
        # 创建数据集
        dataset = OptimizedDataset(train_data_path, self.processor)
        
        # 优化的训练参数
        args = TrainingArguments(
            output_dir=output_dir,
            num_train_epochs=5,  # 增加训练轮数
            per_device_train_batch_size=1,
            gradient_accumulation_steps=8,  # 适中的梯度累积
            learning_rate=2e-5,  # 适中的学习率
            fp16=True,
            save_steps=10,
            logging_steps=2,
            save_total_limit=2,
            remove_unused_columns=False,
            dataloader_drop_last=True,
            dataloader_num_workers=0,
            skip_memory_metrics=True,
            max_grad_norm=1.0,
            warmup_steps=5,
            lr_scheduler_type="cosine",
            weight_decay=0.01,
            gradient_checkpointing=True,
            dataloader_pin_memory=False,
        )
        
        trainer = Trainer(
            model=self.model,
            args=args,
            train_dataset=dataset,
            data_collator=optimized_collate_fn,
        )
        
        logger.info("开始优化微调...")
        trainer.train()
        
        trainer.save_model()
        self.processor.save_pretrained(output_dir)
        
        logger.info(f"优化微调完成，模型保存到: {output_dir}")
        return True

def main():
    """主函数"""
    print("=== 优化的Qwen2.5-VL微调训练系统 ===")
    print("🎯 目标: 大幅提升检测效果")
    print("📊 策略: 增加训练数据+优化LoRA+改进提示词")
    print("🎬 训练方式: 优化LoRA微调")
    print("🔧 技术: 8bit量化 + 优化LoRA + 多样化训练数据")
    print("⏱️  预计时间: 15-25分钟")
    
    # 第1步：生成优化训练数据
    print("\n第1步：生成优化训练数据...")
    if not generate_optimized_training_data():
        print("❌ 优化训练数据生成失败")
        return False
    
    # 第2步：初始化训练器
    print("\n第2步：初始化优化训练器...")
    model_path = "qwen2.5-vl-7b"
    trainer = OptimizedTrainer(model_path)
    
    # 第3步：开始训练
    print("\n第3步：开始优化微调...")
    output_dir = "output/optimized_qwen_finetuned"
    success = trainer.train("data/optimized_training_data.json", output_dir)
    
    if success:
        print("\n🎉 优化微调完成！")
        print(f"📁 模型保存到: {output_dir}")
        print("📋 这是优化后的微调模型")
        print("💡 应该有更好的检测效果")
    else:
        print("\n❌ 优化微调失败！")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
