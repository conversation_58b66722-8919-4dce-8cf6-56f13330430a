#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Qwen2.5-VL视频检测器 - 使用全参数微调后的模型
对完整长序列进行一次性检测，然后线性插值补全
"""

import os
import sys
import json
import torch
import logging
from pathlib import Path
from typing import List, Dict, Any
from PIL import Image
from transformers import Qwen2_5_VLForConditionalGeneration, AutoProcessor
from peft import PeftModel
from peft import PeftModel
from qwen_vl_utils import process_vision_info
import re
import numpy as np

# 固定使用GPU卡2
os.environ['CUDA_VISIBLE_DEVICES'] = '2'

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class QwenVideoDetector:
    """Qwen2.5-VL视频检测器"""

    def __init__(self, base_model_path: str, lora_model_path: str):
        self.device = "cuda" if torch.cuda.is_available() else "cpu"

        logger.info(f"使用设备: {self.device}")
        logger.info(f"基础模型: {base_model_path}")
        logger.info(f"LoRA模型: {lora_model_path}")

        # 加载基础模型
        self.processor = AutoProcessor.from_pretrained(base_model_path)
        self.model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
            base_model_path,
            torch_dtype=torch.float16,
            device_map="auto",
            trust_remote_code=True,
            low_cpu_mem_usage=True
        )

        # 加载LoRA微调模型
        logger.info("加载LoRA微调模型...")
        self.model = PeftModel.from_pretrained(self.model, lora_model_path)
        logger.info("Qwen2.5-VL LoRA微调模型加载完成")
        
        # 序列信息
        self.sequences = {
            'data01': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data02': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data04': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data06': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data07': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data19': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
            'data23': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
            'data26': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'}
        }
        
        # 加载类别映射
        with open('dataset/class.json', 'r', encoding='utf-8') as f:
            self.class_map = json.load(f)
    
    def get_image_files(self, seq_dir: Path, seq_info: Dict) -> List[Path]:
        """获取图像文件列表"""
        if seq_info['naming'] == 'simple':
            image_files = list(seq_dir.glob(f"*{seq_info['format']}"))
            return sorted(image_files, key=lambda x: int(x.stem))
        else:
            image_files = list(seq_dir.glob(f"*{seq_info['format']}"))
            def complex_sort_key(path):
                numbers = re.findall(r'(\d+)', path.name)
                return int(numbers[-1]) if numbers else 0
            return sorted(image_files, key=complex_sort_key)
    
    def load_ground_truth(self, sequence_id: str, frame_indices: List[int]) -> Dict[int, List[Dict]]:
        """加载真实标注"""
        seq_info = self.sequences[sequence_id]
        labels_dir = Path(f"dataset/labels/{sequence_id}")
        ground_truth = {}
        
        for frame_idx in frame_indices:
            frame_id = str(frame_idx)
            label_file = labels_dir / f"{frame_id}.txt"
            
            annotations = []
            if label_file.exists():
                try:
                    with open(label_file, 'r') as f:
                        for line in f:
                            parts = line.strip().split()
                            if len(parts) >= 5:
                                class_id = int(parts[0])
                                x_center = float(parts[1])
                                y_center = float(parts[2])
                                width = float(parts[3])
                                height = float(parts[4])
                                
                                # 转换为绝对坐标
                                img_width, img_height = seq_info['size']
                                x1 = (x_center - width/2) * img_width
                                y1 = (y_center - height/2) * img_height
                                x2 = (x_center + width/2) * img_width
                                y2 = (y_center + height/2) * img_height
                                
                                class_name = self.class_map.get(str(class_id), "unknown")
                                
                                annotations.append({
                                    "bbox": [x1, y1, x2, y2],
                                    "label": class_name,
                                    "frame_id": frame_id,
                                    "frame_idx": frame_idx
                                })
                except Exception as e:
                    logger.warning(f"加载标注文件失败 {label_file}: {e}")
            
            ground_truth[frame_idx] = annotations
        
        return ground_truth
    
    def detect_video_segments(self, video_frames: List[Image.Image], sequence_id: str, frame_indices: List[int]) -> List[Dict]:
        """对视频片段进行检测，然后合并结果"""
        # 将视频帧分割成片段进行检测
        segment_size = 4
        all_detections = []

        for i in range(0, len(video_frames), segment_size):
            segment_frames = video_frames[i:i+segment_size]
            segment_indices = frame_indices[i:i+segment_size]

            if len(segment_frames) < 2:
                continue

            try:
                frame_ids_str = ", ".join([str(idx) for idx in segment_indices])
                prompt = f"""你是专业的红外弱小目标检测专家。现在检测序列{sequence_id}的视频片段。

视频信息：
- 序列ID：{sequence_id}
- 片段帧数：{len(segment_frames)}帧
- 抽取策略：每隔10帧抽取1帧
- 帧索引：{frame_ids_str}

检测任务：
1. 仔细检测每一帧中的所有弱小目标
2. 目标类别：drone, car, ship, bus, pedestrian, cyclist
3. 为每个检测到的目标输出精确的边界框坐标
4. 按帧顺序输出所有检测结果

输出格式（严格遵守）：
[
  {{"bbox":[x1,y1,x2,y2],"label":"drone","frame_id":"{segment_indices[0]}"}},
  {{"bbox":[x1,y1,x2,y2],"label":"drone","frame_id":"{segment_indices[1] if len(segment_indices) > 1 else segment_indices[0]}"}},
  ...
]

要求：
- 必须使用"bbox"字段
- 坐标必须是整数
- frame_id对应实际帧索引
- 只输出JSON数组

开始检测："""
            
                messages = [
                    {
                        "role": "user",
                        "content": [
                            {"type": "video", "video": segment_frames},
                            {"type": "text", "text": prompt}
                        ]
                    }
                ]

                text = self.processor.apply_chat_template(
                    messages, tokenize=False, add_generation_prompt=True
                )

                image_inputs, video_inputs = process_vision_info(messages)

                inputs = self.processor(
                    text=[text],
                    images=image_inputs,
                    videos=video_inputs,
                    padding=True,
                    return_tensors="pt"
                )

                inputs = {k: v.to(self.device) for k, v in inputs.items()}

                with torch.no_grad():
                    outputs = self.model.generate(
                        **inputs,
                        max_new_tokens=2048,
                        do_sample=False,
                        temperature=0.1
                    )

                generated_text = self.processor.decode(
                    outputs[0][inputs["input_ids"].shape[1]:],
                    skip_special_tokens=True
                )

                # 解析JSON结果
                try:
                    clean_text = generated_text.strip()
                    if clean_text.startswith('```json'):
                        clean_text = clean_text[7:]
                    if clean_text.endswith('```'):
                        clean_text = clean_text[:-3]
                    clean_text = clean_text.strip()

                    detections = json.loads(clean_text)
                    if isinstance(detections, list):
                        logger.info(f"片段检测成功: {len(detections)} 个检测")

                        for detection in detections:
                            if isinstance(detection, dict):
                                if 'bbox_2d' in detection and 'bbox' not in detection:
                                    detection['bbox'] = detection['bbox_2d']
                                    del detection['bbox_2d']

                                if 'frame_id' in detection:
                                    try:
                                        detection['frame_idx'] = int(detection['frame_id'])
                                    except:
                                        detection['frame_idx'] = 0

                        all_detections.extend(detections)

                except json.JSONDecodeError as e:
                    logger.warning(f"无法解析片段检测结果: {generated_text[:200]}...")
                    logger.warning(f"JSON解析错误: {e}")

            except Exception as e:
                logger.error(f"检测视频片段时出错: {e}")
                continue

        logger.info(f"所有片段检测完成，总计: {len(all_detections)} 个检测")
        return all_detections
    
    def linear_interpolate_detections(self, key_frame_detections: Dict[int, List[Dict]], all_frame_indices: List[int]) -> Dict[int, List[Dict]]:
        """线性插值补全所有帧的检测结果"""
        all_detections = {}
        
        # 首先复制关键帧的检测结果
        for frame_idx in key_frame_detections:
            all_detections[frame_idx] = key_frame_detections[frame_idx]
        
        # 对每个缺失的帧进行线性插值
        for frame_idx in all_frame_indices:
            if frame_idx not in all_detections:
                prev_frame = None
                next_frame = None
                
                for key_frame in sorted(key_frame_detections.keys()):
                    if key_frame < frame_idx:
                        prev_frame = key_frame
                    elif key_frame > frame_idx and next_frame is None:
                        next_frame = key_frame
                        break
                
                interpolated_detections = []
                
                if prev_frame is not None and next_frame is not None:
                    # 双向插值
                    prev_detections = key_frame_detections[prev_frame]
                    next_detections = key_frame_detections[next_frame]
                    
                    if prev_detections and next_detections:
                        for i, prev_det in enumerate(prev_detections):
                            if i < len(next_detections):
                                next_det = next_detections[i]
                                
                                total_gap = next_frame - prev_frame
                                current_gap = frame_idx - prev_frame
                                weight = current_gap / total_gap
                                
                                prev_bbox = prev_det['bbox']
                                next_bbox = next_det['bbox']
                                
                                interpolated_bbox = [
                                    int(prev_bbox[0] + (next_bbox[0] - prev_bbox[0]) * weight),
                                    int(prev_bbox[1] + (next_bbox[1] - prev_bbox[1]) * weight),
                                    int(prev_bbox[2] + (next_bbox[2] - prev_bbox[2]) * weight),
                                    int(prev_bbox[3] + (next_bbox[3] - prev_bbox[3]) * weight)
                                ]
                                
                                interpolated_detections.append({
                                    "bbox": interpolated_bbox,
                                    "label": prev_det['label'],
                                    "frame_id": str(frame_idx),
                                    "frame_idx": frame_idx,
                                    "interpolated": True,
                                    "interpolated_from": [prev_frame, next_frame]
                                })
                
                elif prev_frame is not None:
                    for det in key_frame_detections[prev_frame]:
                        if isinstance(det, dict) and 'bbox' in det and 'label' in det:
                            interpolated_detections.append({
                                "bbox": det['bbox'],
                                "label": det['label'],
                                "frame_id": str(frame_idx),
                                "frame_idx": frame_idx,
                                "interpolated": True,
                                "interpolated_from": [prev_frame]
                            })
                
                elif next_frame is not None:
                    for det in key_frame_detections[next_frame]:
                        if isinstance(det, dict) and 'bbox' in det and 'label' in det:
                            interpolated_detections.append({
                                "bbox": det['bbox'],
                                "label": det['label'],
                                "frame_id": str(frame_idx),
                                "frame_idx": frame_idx,
                                "interpolated": True,
                                "interpolated_from": [next_frame]
                            })
                
                all_detections[frame_idx] = interpolated_detections
        
        return all_detections
    
    def calculate_metrics(self, predictions: Dict[int, List[Dict]], ground_truth: Dict[int, List[Dict]], iou_threshold: float = 0.5) -> Dict:
        """计算检测指标"""
        def calculate_iou(box1, box2):
            x1 = max(box1[0], box2[0])
            y1 = max(box1[1], box2[1])
            x2 = min(box1[2], box2[2])
            y2 = min(box1[3], box2[3])
            
            if x2 <= x1 or y2 <= y1:
                return 0.0
            
            intersection = (x2 - x1) * (y2 - y1)
            area1 = (box1[2] - box1[0]) * (box1[3] - box1[1])
            area2 = (box2[2] - box2[0]) * (box2[3] - box2[1])
            union = area1 + area2 - intersection
            
            return intersection / union if union > 0 else 0.0
        
        total_tp = 0
        total_fp = 0
        total_fn = 0
        
        for frame_idx in ground_truth:
            gt_boxes = ground_truth[frame_idx]
            pred_boxes = predictions.get(frame_idx, [])
            
            matched_gt = set()
            
            for pred in pred_boxes:
                if not isinstance(pred, dict) or 'bbox' not in pred:
                    continue
                    
                best_iou = 0
                best_gt_idx = -1
                
                for gt_idx, gt in enumerate(gt_boxes):
                    if gt_idx in matched_gt:
                        continue
                    
                    if not isinstance(gt, dict) or 'bbox' not in gt:
                        continue
                    
                    iou = calculate_iou(pred['bbox'], gt['bbox'])
                    if iou > best_iou and iou >= iou_threshold:
                        best_iou = iou
                        best_gt_idx = gt_idx
                
                if best_gt_idx >= 0:
                    total_tp += 1
                    matched_gt.add(best_gt_idx)
                else:
                    total_fp += 1
            
            total_fn += len(gt_boxes) - len(matched_gt)
        
        precision = total_tp / (total_tp + total_fp) if (total_tp + total_fp) > 0 else 0
        recall = total_tp / (total_tp + total_fn) if (total_tp + total_fn) > 0 else 0
        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
        
        return {
            "precision": precision,
            "recall": recall,
            "f1": f1,
            "tp": total_tp,
            "fp": total_fp,
            "fn": total_fn
        }
    
    def detect_sequence_with_qwen_method(self, sequence_id: str, test_ratio: float = 0.05) -> Dict:
        """使用Qwen2.5-VL方法检测序列：完整长序列检测+线性插值"""
        logger.info(f"开始检测序列 {sequence_id}...")
        
        seq_info = self.sequences[sequence_id]
        seq_dir = Path(f"dataset/images/{sequence_id}")
        
        if not seq_dir.exists():
            logger.error(f"序列目录不存在: {seq_dir}")
            return {}
        
        image_files = self.get_image_files(seq_dir, seq_info)
        if not image_files:
            logger.error(f"序列 {sequence_id} 中没有找到图像文件")
            return {}
        
        # 计算测试帧数（前5%）
        total_frames = len(image_files)
        test_frames = max(int(total_frames * test_ratio), 50)
        test_frames = min(test_frames, total_frames)
        test_image_files = image_files[:test_frames]
        
        logger.info(f"序列 {sequence_id}: 总帧数 {total_frames}, 测试帧数 {test_frames}")
        
        # 每隔10帧抽取，组成完整长视频序列
        frame_interval = 10
        key_frame_indices = list(range(0, test_frames, frame_interval))
        all_frame_indices = list(range(test_frames))
        
        logger.info(f"关键帧数: {len(key_frame_indices)} (每隔{frame_interval}帧)")
        logger.info(f"总帧数: {len(all_frame_indices)} (需要插值)")
        
        # 加载完整长视频序列
        video_frames = []
        for idx in key_frame_indices:
            try:
                image = Image.open(test_image_files[idx]).convert('RGB')
                image = image.resize((336, 336))
                video_frames.append(image)
            except Exception as e:
                logger.warning(f"无法加载图像 {test_image_files[idx]}: {e}")
        
        if len(video_frames) < 2:
            logger.warning(f"序列 {sequence_id} 可用帧数不足")
            return {}
        
        # 对视频片段进行检测
        logger.info(f"对视频片段进行检测 ({len(video_frames)} 帧)...")
        key_detections = self.detect_video_segments(video_frames, sequence_id, key_frame_indices)
        
        # 将检测结果按帧组织
        key_frame_detections = {}
        for detection in key_detections:
            if isinstance(detection, dict):
                frame_idx = detection.get('frame_idx', 0)
                if frame_idx not in key_frame_detections:
                    key_frame_detections[frame_idx] = []
                key_frame_detections[frame_idx].append(detection)
        
        # 线性插值补全所有帧
        logger.info("进行线性插值补全...")
        all_detections = self.linear_interpolate_detections(key_frame_detections, all_frame_indices)
        
        # 加载真实标注
        logger.info("加载真实标注...")
        ground_truth = self.load_ground_truth(sequence_id, all_frame_indices)
        
        # 计算指标
        logger.info("计算检测指标...")
        metrics = self.calculate_metrics(all_detections, ground_truth)
        
        # 统计结果
        total_detections = sum(len(dets) for dets in all_detections.values())
        direct_detections = sum(len(dets) for frame_idx, dets in all_detections.items() 
                               if frame_idx in key_frame_detections)
        interpolated_detections = total_detections - direct_detections
        
        result = {
            "sequence_id": sequence_id,
            "total_frames": test_frames,
            "key_frames": len(key_frame_indices),
            "total_detections": total_detections,
            "direct_detections": direct_detections,
            "interpolated_detections": interpolated_detections,
            "metrics": metrics,
            "detections": all_detections
        }
        
        logger.info(f"序列 {sequence_id} 检测完成:")
        logger.info(f"  总检测数: {total_detections}")
        logger.info(f"  直接检测: {direct_detections}")
        logger.info(f"  插值检测: {interpolated_detections}")
        logger.info(f"  精确度: {metrics['precision']:.3f}")
        logger.info(f"  召回率: {metrics['recall']:.3f}")
        logger.info(f"  F1分数: {metrics['f1']:.3f}")
        
        return result

def main():
    """主函数"""
    print("=== Qwen2.5-VL视频检测与指标计算系统 ===")
    print("🎯 目标序列: data01, data02, data04, data06, data07, data19, data23, data26")
    print("📊 检测策略: 对完整长序列进行一次性检测")
    print("🔄 插值策略: 线性插值补全缺失帧")
    print("📈 指标计算: 使用真实labels计算指标")
    print("🤖 模型: 使用Qwen2.5-VL全参数微调后的模型")
    
    # 检查模型是否存在
    base_model_path = "qwen2.5-vl-7b"
    lora_model_path = "output/correct_video_sequence_lora"  # 使用之前成功的模型

    if not os.path.exists(lora_model_path):
        print(f"\n❌ 未找到Qwen2.5-VL LoRA微调后的模型: {lora_model_path}")
        print("请先运行 python train.py 进行Qwen2.5-VL视频微调训练")
        return False

    print(f"\n✅ 找到Qwen2.5-VL LoRA微调后的模型: {lora_model_path}")

    # 初始化检测器
    print("\n初始化Qwen2.5-VL视频检测器...")
    detector = QwenVideoDetector(base_model_path, lora_model_path)
    
    # 开始检测
    print("\n开始Qwen2.5-VL视频检测与指标计算...")
    
    sequences = ['data01', 'data02', 'data04', 'data06', 'data07', 'data19', 'data23', 'data26']
    all_results = {}
    
    for sequence_id in sequences:
        print(f"\n{'='*60}")
        print(f"检测序列: {sequence_id}")
        print(f"{'='*60}")
        
        result = detector.detect_sequence_with_qwen_method(sequence_id)
        all_results[sequence_id] = result
    
    # 保存完整结果
    os.makedirs("results", exist_ok=True)
    with open("results/qwen_video_detection_results.json", 'w', encoding='utf-8') as f:
        json.dump(all_results, f, ensure_ascii=False, indent=2)
    
    print("\n🎉 Qwen2.5-VL视频检测完成！")
    print("📊 检测结果已保存到 results/qwen_video_detection_results.json")
    
    # 统计总体结果
    print("\n📈 总体检测统计:")
    total_detections = 0
    total_direct = 0
    total_interpolated = 0
    overall_metrics = {"tp": 0, "fp": 0, "fn": 0}
    
    for seq_id, result in all_results.items():
        if result:
            total_detections += result['total_detections']
            total_direct += result['direct_detections']
            total_interpolated += result['interpolated_detections']
            
            metrics = result['metrics']
            overall_metrics['tp'] += metrics['tp']
            overall_metrics['fp'] += metrics['fp']
            overall_metrics['fn'] += metrics['fn']
            
            print(f"   {seq_id}: {result['total_detections']} 个检测 "
                  f"(直接: {result['direct_detections']}, 插值: {result['interpolated_detections']}) "
                  f"P: {metrics['precision']:.3f}, R: {metrics['recall']:.3f}, F1: {metrics['f1']:.3f}")
    
    # 计算总体指标
    overall_precision = overall_metrics['tp'] / (overall_metrics['tp'] + overall_metrics['fp']) if (overall_metrics['tp'] + overall_metrics['fp']) > 0 else 0
    overall_recall = overall_metrics['tp'] / (overall_metrics['tp'] + overall_metrics['fn']) if (overall_metrics['tp'] + overall_metrics['fn']) > 0 else 0
    overall_f1 = 2 * overall_precision * overall_recall / (overall_precision + overall_recall) if (overall_precision + overall_recall) > 0 else 0
    
    print(f"\n📊 总体统计:")
    print(f"   总检测数: {total_detections}")
    print(f"   直接检测: {total_direct}")
    print(f"   插值检测: {total_interpolated}")
    print(f"   总体精确度: {overall_precision:.3f}")
    print(f"   总体召回率: {overall_recall:.3f}")
    print(f"   总体F1分数: {overall_f1:.3f}")
    
    print("\n💡 这是使用Qwen2.5-VL全参数微调的检测结果:")
    print("包括：完整长序列一次性检测 + 线性插值补全 + 真实指标计算")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
