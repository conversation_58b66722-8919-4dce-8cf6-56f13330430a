#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Qwen2.5-VL视频微调训练器 - 全参数微调，不使用LoRA
严格按照用户要求：每个序列生成一个完整的长视频序列样本
"""

import os
import sys
import json
import torch
import logging
from pathlib import Path
from typing import List, Dict, Any
from PIL import Image
from torch.utils.data import Dataset, DataLoader
from transformers import (
    Qwen2_5_VLForConditionalGeneration,
    AutoProcessor,
    TrainingArguments,
    Trainer,
)
from qwen_vl_utils import process_vision_info
import numpy as np
import re

# 强制使用GPU卡2
import torch
os.environ['CUDA_VISIBLE_DEVICES'] = '2'
torch.cuda.set_device(0)
if torch.cuda.is_available():
    torch.cuda.empty_cache()
    print(f"当前使用GPU: {torch.cuda.current_device()}")
    print(f"GPU名称: {torch.cuda.get_device_name()}")
    print(f"GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_image_files(seq_dir: Path, seq_info: Dict) -> List[Path]:
    """获取图像文件列表"""
    if seq_info['naming'] == 'simple':
        image_files = list(seq_dir.glob(f"*{seq_info['format']}"))
        return sorted(image_files, key=lambda x: int(x.stem))
    else:
        image_files = list(seq_dir.glob(f"*{seq_info['format']}"))
        def complex_sort_key(path):
            numbers = re.findall(r'(\d+)', path.name)
            return int(numbers[-1]) if numbers else 0
        return sorted(image_files, key=complex_sort_key)

def load_frame_annotation(seq_id, frame_id, size):
    """加载帧标注"""
    labels_dir = Path(f"dataset/labels/{seq_id}")
    label_file = labels_dir / f"{frame_id}.txt"
    
    if not label_file.exists():
        return []
    
    with open('dataset/class.json', 'r', encoding='utf-8') as f:
        class_map = json.load(f)
    
    annotations = []
    try:
        with open(label_file, 'r') as f:
            for line in f:
                parts = line.strip().split()
                if len(parts) >= 5:
                    class_id = int(parts[0])
                    x_center = float(parts[1])
                    y_center = float(parts[2])
                    width = float(parts[3])
                    height = float(parts[4])
                    
                    # 转换为绝对坐标
                    img_width, img_height = size
                    x1 = (x_center - width/2) * img_width
                    y1 = (y_center - height/2) * img_height
                    x2 = (x_center + width/2) * img_width
                    y2 = (y_center + height/2) * img_height
                    
                    class_name = class_map.get(str(class_id), "unknown")
                    
                    annotations.append({
                        "bbox": [x1, y1, x2, y2],
                        "label": class_name,
                        "frame_id": frame_id
                    })
    except Exception as e:
        logger.warning(f"加载标注文件失败 {label_file}: {e}")
    
    return annotations

def generate_qwen_video_training_data():
    """生成Qwen2.5-VL视频微调数据 - 每个序列一个完整样本"""
    logger.info("生成Qwen2.5-VL视频微调数据...")
    
    sequences = {
        'data01': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
        'data02': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
        'data04': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
        'data06': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
        'data07': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
        'data19': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
        'data23': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
        'data26': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'}
    }
    
    all_sequence_samples = []
    
    for seq_id, seq_info in sequences.items():
        print(f"\n处理序列 {seq_id}...")
        
        images_dir = Path(f"dataset/images/{seq_id}")
        image_files = get_image_files(images_dir, seq_info)
        
        total_frames = len(image_files)
        print(f"  总帧数: {total_frames}")
        
        # 每隔10帧抽取1帧，组成完整的长视频序列
        frame_interval = 10
        sampled_indices = list(range(0, total_frames, frame_interval))
        
        print(f"  抽取帧数: {len(sampled_indices)} (每隔{frame_interval}帧)")
        
        # 构建完整的长视频序列 - 关键：每个序列只生成一个样本
        video_frames_info = []
        all_annotations = []
        
        for frame_idx in sampled_indices:
            frame_file = image_files[frame_idx]
            frame_id = frame_file.stem
            
            video_frames_info.append({
                'frame_idx': frame_idx,
                'frame_id': frame_id,
                'file_path': str(frame_file)
            })
            
            annotations = load_frame_annotation(seq_id, frame_id, seq_info['size'])
            for ann in annotations:
                ann['frame_id'] = frame_id
                ann['frame_idx'] = frame_idx
            
            all_annotations.extend(annotations)
        
        # 创建完整序列训练样本 - 每个序列只有一个样本！
        if len(video_frames_info) >= 2:
            sequence_sample = {
                "conversations": [
                    {
                        "from": "human",
                        "value": f"这是序列{seq_id}的完整视频序列，包含{len(video_frames_info)}帧（每隔{frame_interval}帧抽取）。图像尺寸为{seq_info['size']}。请仔细检测序列中每一帧的弱小目标。目标类别包括：drone, car, ship, bus, pedestrian, cyclist。请按帧顺序输出所有检测结果，格式为JSON数组：[{{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}}]"
                    },
                    {
                        "from": "gpt",
                        "value": json.dumps(all_annotations)
                    }
                ],
                "sequence_info": {
                    "sequence_id": seq_id,
                    "video_frames": video_frames_info,
                    "size": seq_info['size'],
                    "format": seq_info['format'],
                    "frame_count": len(video_frames_info),
                    "total_annotations": len(all_annotations),
                    "is_complete_sequence": True
                }
            }
            
            all_sequence_samples.append(sequence_sample)
            print(f"  ✅ 生成完整序列样本: {len(video_frames_info)}帧, {len(all_annotations)}个标注")
        else:
            print(f"  ❌ 序列 {seq_id} 帧数不足，跳过")
    
    output_path = "data/qwen_video_training_data.json"
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(all_sequence_samples, f, ensure_ascii=False, indent=2)
    
    logger.info(f"Qwen2.5-VL视频微调数据保存到: {output_path}")
    logger.info(f"总序列样本数: {len(all_sequence_samples)} (每个序列一个样本)")
    
    return True

class QwenVideoDataset(Dataset):
    """Qwen2.5-VL视频数据集"""
    
    def __init__(self, data_path: str, processor):
        self.processor = processor
        with open(data_path, 'r', encoding='utf-8') as f:
            self.data = json.load(f)
        logger.info(f"加载了 {len(self.data)} 个完整序列训练样本")
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        sample = self.data[idx]
        
        sequence_info = sample['sequence_info']
        video_frames = []
        
        # 控制帧数以适应内存
        max_frames = 4  # 每个完整序列最多4帧
        frame_list = sequence_info['video_frames'][:max_frames]
        
        for frame_info in frame_list:
            try:
                image = Image.open(frame_info['file_path']).convert('RGB')
                image = image.resize((336, 336))
                video_frames.append(image)
            except Exception as e:
                logger.warning(f"无法加载图像 {frame_info['file_path']}: {e}")
                continue
        
        if len(video_frames) < 2:
            return None
        
        conversations = sample['conversations']
        messages = [
            {
                "role": "user",
                "content": [
                    {"type": "video", "video": video_frames},
                    {"type": "text", "text": conversations[0]['value']}
                ]
            },
            {
                "role": "assistant", 
                "content": conversations[1]['value']
            }
        ]
        
        return {
            'messages': messages,
            'sequence_info': sequence_info
        }

def qwen_video_collate_fn(batch, processor):
    """Qwen2.5-VL视频数据整理函数"""
    batch = [item for item in batch if item is not None]
    if not batch:
        return None
    
    try:
        example = batch[0]
        messages = example["messages"]
        
        text = processor.apply_chat_template(
            messages, tokenize=False, add_generation_prompt=True
        )
        
        image_inputs, video_inputs = process_vision_info(messages)
        
        inputs = processor(
            text=[text],
            images=image_inputs,
            videos=video_inputs,
            padding=True,
            return_tensors="pt"
        )
        
        if inputs["input_ids"].numel() == 0:
            logger.warning("生成的input_ids为空，跳过此样本")
            return None
        
        inputs["labels"] = inputs["input_ids"].clone()
        
        return inputs
        
    except Exception as e:
        logger.warning(f"处理样本时出错: {e}")
        return None

class QwenVideoTrainer:
    """Qwen2.5-VL视频微调训练器 - 全参数微调"""
    
    def __init__(self, model_path: str):
        self.model_path = model_path
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        
        logger.info(f"使用设备: {self.device}")
        logger.info(f"模型路径: {model_path}")
        
        torch.cuda.empty_cache()
        
        # 加载模型和处理器 - 全参数微调
        self.processor = AutoProcessor.from_pretrained(model_path)
        self.model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
            model_path,
            torch_dtype=torch.float16,
            device_map="auto",
            trust_remote_code=True,
            low_cpu_mem_usage=True,
            use_cache=False,
            max_memory={0: "60GB"}  # 为全参数微调预留内存
        )
        
        logger.info("Qwen2.5-VL模型加载完成 - 准备全参数微调")
    
    def train(self, train_data_path: str, output_dir: str):
        """开始Qwen2.5-VL视频全参数微调"""
        logger.info("开始Qwen2.5-VL视频全参数微调...")
        
        dataset = QwenVideoDataset(train_data_path, self.processor)
        
        def collate_wrapper(batch):
            return qwen_video_collate_fn(batch, self.processor)
        
        # Qwen2.5-VL全参数微调参数
        args = TrainingArguments(
            output_dir=output_dir,
            num_train_epochs=2,  # 全参数微调用较少轮数
            per_device_train_batch_size=1,
            gradient_accumulation_steps=1,
            learning_rate=5e-6,  # 全参数微调用更小的学习率
            fp16=True,
            save_steps=2,
            logging_steps=1,
            save_total_limit=1,
            remove_unused_columns=False,
            dataloader_drop_last=True,
            dataloader_num_workers=0,
            skip_memory_metrics=True,
            max_grad_norm=1.0,
            warmup_steps=1,
            lr_scheduler_type="linear",
            weight_decay=0.01,
        )
        
        trainer = Trainer(
            model=self.model,
            args=args,
            train_dataset=dataset,
            data_collator=collate_wrapper,
        )
        
        logger.info("开始Qwen2.5-VL视频全参数微调...")
        trainer.train()
        
        trainer.save_model()
        self.processor.save_pretrained(output_dir)
        
        logger.info(f"Qwen2.5-VL视频微调完成，模型保存到: {output_dir}")
        return True

def main():
    """主函数"""
    print("=== Qwen2.5-VL视频微调训练系统 ===")
    print("🎯 目标: 每个序列生成一个完整的长视频序列样本")
    print("📊 策略: 每隔10帧抽取组成完整长序列，每个序列一个样本")
    print("🎬 训练方式: Qwen2.5-VL全参数视频微调")
    print("🔧 技术: 全参数微调 (不使用LoRA)")
    print("⏱️  预计时间: 10-20分钟")
    
    # 第1步：生成视频微调数据
    print("\n第1步：生成Qwen2.5-VL视频微调数据...")
    if not generate_qwen_video_training_data():
        print("❌ 视频微调数据生成失败")
        return False
    
    # 第2步：初始化训练器
    print("\n第2步：初始化Qwen2.5-VL视频微调训练器...")
    model_path = "qwen2.5-vl-7b"
    trainer = QwenVideoTrainer(model_path)
    
    # 第3步：开始训练
    print("\n第3步：开始Qwen2.5-VL视频全参数微调...")
    output_dir = "output/qwen_video_finetuned"
    success = trainer.train("data/qwen_video_training_data.json", output_dir)
    
    if success:
        print("\n🎉 Qwen2.5-VL视频微调完成！")
        print(f"📁 模型保存到: {output_dir}")
        print("📋 下一步: 使用微调后的模型进行检测")
    else:
        print("\n❌ 视频微调失败！")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
