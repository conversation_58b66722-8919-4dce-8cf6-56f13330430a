#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真正的视频序列检测器
使用微调后的Qwen2.5-VL模型进行检测
"""

import os
import sys
import json
import torch
import logging
from pathlib import Path
from typing import List, Dict, Any, Tuple
from PIL import Image
import numpy as np
from transformers import Qwen2VLForConditionalGeneration, AutoProcessor
from peft import PeftModel
from qwen_vl_utils import process_vision_info
import re

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RealVideoSequenceDetector:
    """真正的视频序列检测器"""
    
    def __init__(self, base_model_path: str, lora_model_path: str = None):
        self.base_model_path = base_model_path
        self.lora_model_path = lora_model_path
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        
        logger.info(f"使用设备: {self.device}")
        logger.info(f"基础模型: {base_model_path}")
        if lora_model_path:
            logger.info(f"LoRA模型: {lora_model_path}")
        
        # 加载模型和处理器
        self.processor = AutoProcessor.from_pretrained(base_model_path)
        self.model = Qwen2VLForConditionalGeneration.from_pretrained(
            base_model_path,
            torch_dtype=torch.float16 if self.device == "cuda" else torch.float32,
            device_map="auto" if self.device == "cuda" else None
        )
        
        # 如果有LoRA模型，加载它
        if lora_model_path and os.path.exists(lora_model_path):
            logger.info("加载LoRA微调模型...")
            self.model = PeftModel.from_pretrained(self.model, lora_model_path)
            logger.info("LoRA模型加载完成")
        else:
            logger.warning("未找到LoRA模型，使用基础模型")
        
        self.model.eval()
        
        # 序列配置
        self.sequences = {
            'data01': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data02': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data04': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data06': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data07': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data19': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
            'data23': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
            'data26': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'}
        }
    
    def detect_video_sequence(self, video_frames: List[Image.Image], sequence_id: str) -> List[Dict]:
        """检测视频序列中的目标"""
        try:
            # 构建检测提示
            seq_info = self.sequences[sequence_id]
            prompt = f"""这是一个包含{len(video_frames)}帧的红外视频序列（从{sequence_id}每隔10帧抽取1帧组成），图像尺寸为{seq_info['size'][0]}x{seq_info['size'][1]}，请仔细检测序列中所有帧的弱小目标。目标类别包括：drone, car, ship, bus, pedestrian, cyclist。请以JSON数组格式输出检测结果，每个目标必须包含精确的边界框坐标、类别标签和帧ID。格式要求：[{{"bbox":[x1,y1,x2,y2],"label":"类别","frame_id":"帧号"}}]"""
            
            # 构建消息
            messages = [{
                "role": "user",
                "content": [
                    {"type": "video", "video": video_frames},
                    {"type": "text", "text": prompt}
                ]
            }]
            
            # 应用聊天模板
            text = self.processor.apply_chat_template(
                messages, tokenize=False, add_generation_prompt=True
            )
            
            # 处理视觉信息
            image_inputs, video_inputs = process_vision_info(messages)
            
            # 处理输入
            inputs = self.processor(
                text=[text],
                images=image_inputs,
                videos=video_inputs,
                padding=True,
                return_tensors="pt",
            )
            
            # 移动到设备
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # 生成检测结果
            with torch.no_grad():
                outputs = self.model.generate(
                    **inputs,
                    max_new_tokens=2048,
                    do_sample=False,
                    temperature=0.1,
                    pad_token_id=self.processor.tokenizer.eos_token_id
                )
            
            # 解码结果
            generated_text = self.processor.batch_decode(
                outputs[:, inputs['input_ids'].shape[1]:], 
                skip_special_tokens=True
            )[0]
            
            # 解析检测结果
            detections = self.parse_detection_results(generated_text, sequence_id)
            
            logger.info(f"序列 {sequence_id} 检测到 {len(detections)} 个目标")
            return detections
            
        except Exception as e:
            logger.error(f"检测序列 {sequence_id} 时出错: {e}")
            return []
    
    def parse_detection_results(self, text: str, sequence_id: str) -> List[Dict]:
        """解析检测结果文本"""
        detections = []
        
        try:
            # 尝试直接解析JSON
            if text.strip().startswith('[') and text.strip().endswith(']'):
                results = json.loads(text.strip())
                for result in results:
                    if isinstance(result, dict) and 'bbox' in result:
                        detection = {
                            'bbox': result['bbox'],
                            'label': result.get('label', 'unknown'),
                            'confidence': result.get('confidence', 0.8),
                            'frame_id': result.get('frame_id', '0'),
                            'sequence_id': sequence_id,
                            'interpolated': False
                        }
                        detections.append(detection)
            else:
                # 使用正则表达式提取
                pattern = r'\{"bbox":\s*\[([^\]]+)\],\s*"label":\s*"([^"]+)",\s*"frame_id":\s*"([^"]+)"\}'
                matches = re.findall(pattern, text)
                
                for match in matches:
                    try:
                        bbox_str, label, frame_id = match
                        bbox = [float(x.strip()) for x in bbox_str.split(',')]
                        
                        detection = {
                            'bbox': bbox,
                            'label': label,
                            'confidence': 0.8,
                            'frame_id': frame_id,
                            'sequence_id': sequence_id,
                            'interpolated': False
                        }
                        detections.append(detection)
                    except:
                        continue
                        
        except Exception as e:
            logger.warning(f"解析检测结果失败: {e}")
        
        return detections
    
    def get_image_files(self, seq_dir: Path, seq_info: Dict) -> List[Path]:
        """获取图像文件列表"""
        if seq_info['naming'] == 'simple':
            # 简单命名：0.bmp, 1.bmp, ...
            image_files = list(seq_dir.glob(f"*{seq_info['format']}"))
            return sorted(image_files, key=lambda x: int(x.stem))
        else:
            # 复杂命名：按文件名中的数字排序
            image_files = list(seq_dir.glob(f"*{seq_info['format']}"))
            def complex_sort_key(path):
                import re
                numbers = re.findall(r'(\d+)', path.name)
                return int(numbers[-1]) if numbers else 0
            return sorted(image_files, key=complex_sort_key)
    
    def detect_sequence_with_interpolation(self, sequence_id: str, test_ratio: float = 0.05) -> List[Dict]:
        """检测序列并进行线性插值"""
        logger.info(f"开始检测序列 {sequence_id}...")
        
        seq_info = self.sequences[sequence_id]
        seq_dir = Path(f"dataset/images/{sequence_id}")
        
        if not seq_dir.exists():
            logger.error(f"序列目录不存在: {seq_dir}")
            return []
        
        # 获取图像文件
        image_files = self.get_image_files(seq_dir, seq_info)
        if not image_files:
            logger.error(f"序列 {sequence_id} 中没有找到图像文件")
            return []
        
        # 计算测试帧数（前5%）
        total_frames = len(image_files)
        test_frames = max(int(total_frames * test_ratio), 50)
        test_frames = min(test_frames, total_frames)
        test_image_files = image_files[:test_frames]
        
        logger.info(f"序列 {sequence_id}: 总帧数 {total_frames}, 测试帧数 {test_frames}")
        
        # 每隔10帧抽取关键帧进行检测
        frame_interval = 10
        keyframe_indices = list(range(0, test_frames, frame_interval))
        
        logger.info(f"关键帧数: {len(keyframe_indices)} (每隔{frame_interval}帧)")
        
        # 加载关键帧图像
        keyframe_images = []
        for idx in keyframe_indices:
            try:
                image = Image.open(test_image_files[idx]).convert('RGB')
                keyframe_images.append(image)
            except Exception as e:
                logger.warning(f"无法加载图像 {test_image_files[idx]}: {e}")
        
        if not keyframe_images:
            logger.error(f"无法加载序列 {sequence_id} 的关键帧")
            return []
        
        # 进行检测
        keyframe_detections = self.detect_video_sequence(keyframe_images, sequence_id)
        
        # 将检测结果映射到帧索引
        detection_map = {}
        for detection in keyframe_detections:
            frame_id = detection['frame_id']
            try:
                frame_idx = int(frame_id) // frame_interval * frame_interval
                if frame_idx not in detection_map:
                    detection_map[frame_idx] = []
                detection_map[frame_idx].append(detection)
            except:
                continue
        
        # 进行线性插值
        all_detections = self.interpolate_detections(detection_map, test_frames, frame_interval, sequence_id)
        
        logger.info(f"序列 {sequence_id} 检测完成，总检测数: {len(all_detections)}")
        return all_detections
    
    def interpolate_detections(self, detection_map: Dict, total_frames: int, frame_interval: int, sequence_id: str) -> List[Dict]:
        """线性插值检测结果"""
        all_detections = []
        
        # 添加关键帧检测结果
        for frame_idx, detections in detection_map.items():
            all_detections.extend(detections)
        
        # 进行线性插值
        sorted_keyframes = sorted(detection_map.keys())
        
        for i in range(len(sorted_keyframes) - 1):
            start_frame = sorted_keyframes[i]
            end_frame = sorted_keyframes[i + 1]
            
            start_detections = detection_map[start_frame]
            end_detections = detection_map[end_frame]
            
            # 对中间帧进行插值
            for frame_idx in range(start_frame + 1, end_frame):
                if frame_idx >= total_frames:
                    break
                
                alpha = (frame_idx - start_frame) / (end_frame - start_frame)
                
                # 简单插值：取较少检测数的数量
                max_detections = max(len(start_detections), len(end_detections))
                
                for j in range(max_detections):
                    start_det = start_detections[j] if j < len(start_detections) else None
                    end_det = end_detections[j] if j < len(end_detections) else None
                    
                    if start_det and end_det:
                        # 插值边界框
                        interpolated_bbox = [
                            start_det['bbox'][0] + alpha * (end_det['bbox'][0] - start_det['bbox'][0]),
                            start_det['bbox'][1] + alpha * (end_det['bbox'][1] - start_det['bbox'][1]),
                            start_det['bbox'][2] + alpha * (end_det['bbox'][2] - start_det['bbox'][2]),
                            start_det['bbox'][3] + alpha * (end_det['bbox'][3] - start_det['bbox'][3])
                        ]
                        
                        interpolated_detection = {
                            'bbox': interpolated_bbox,
                            'label': start_det['label'],
                            'confidence': min(start_det['confidence'], end_det['confidence']),
                            'frame_id': str(frame_idx),
                            'sequence_id': sequence_id,
                            'interpolated': True
                        }
                        all_detections.append(interpolated_detection)
        
        return all_detections

def main():
    """主函数"""
    print("=== 真正的视频序列检测器 ===")
    
    # 检查是否有微调后的模型
    base_model_path = "qwen2.5-vl-7b"
    lora_model_path = "output/video_sequence_lora"
    
    if not os.path.exists(lora_model_path):
        print(f"❌ 未找到微调后的模型: {lora_model_path}")
        print("请先运行 python train.py 进行微调训练")
        return False
    
    # 初始化检测器
    detector = RealVideoSequenceDetector(base_model_path, lora_model_path)
    
    # 检测所有序列
    all_results = {}
    sequences = ['data01', 'data02', 'data04', 'data06', 'data07', 'data19', 'data23', 'data26']
    
    for seq_id in sequences:
        print(f"\n检测序列 {seq_id}...")
        detections = detector.detect_sequence_with_interpolation(seq_id, test_ratio=0.05)
        all_results[seq_id] = detections
    
    # 保存结果
    results_path = "results/real_detection_results.json"
    os.makedirs("results", exist_ok=True)
    
    with open(results_path, 'w', encoding='utf-8') as f:
        json.dump(all_results, f, ensure_ascii=False, indent=2)
    
    print(f"\n🎉 检测完成！结果保存到: {results_path}")
    
    # 统计结果
    total_detections = sum(len(detections) for detections in all_results.values())
    print(f"📊 总检测数: {total_detections}")
    
    for seq_id, detections in all_results.items():
        print(f"   {seq_id}: {len(detections)} 个检测")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
