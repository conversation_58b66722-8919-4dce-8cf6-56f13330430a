#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真正的完整序列微调训练器 - 严格按照用户要求实现
每个序列生成一个完整的长视频序列样本，适应Qwen2.5-VL-7B
"""

import os
import sys
import json
import torch
import logging
from pathlib import Path
from typing import List, Dict, Any
from PIL import Image
from torch.utils.data import Dataset, DataLoader
from transformers import (
    Qwen2_5_VLForConditionalGeneration,
    AutoProcessor,
    TrainingArguments,
    Trainer,
)
from peft import LoraConfig, get_peft_model, TaskType
from qwen_vl_utils import process_vision_info
import numpy as np
import re

# 强制使用GPU卡2
import torch
os.environ['CUDA_VISIBLE_DEVICES'] = '2'
torch.cuda.set_device(0)  # 在可见设备中选择第0个（即原来的GPU 2）
if torch.cuda.is_available():
    torch.cuda.empty_cache()
    print(f"当前使用GPU: {torch.cuda.current_device()}")
    print(f"GPU名称: {torch.cuda.get_device_name()}")
    print(f"GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_image_files(seq_dir: Path, seq_info: Dict) -> List[Path]:
    """获取图像文件列表"""
    if seq_info['naming'] == 'simple':
        # 简单命名：0.bmp, 1.bmp, ...
        image_files = list(seq_dir.glob(f"*{seq_info['format']}"))
        return sorted(image_files, key=lambda x: int(x.stem))
    else:
        # 复杂命名：img00001.jpg, img00002.jpg, ...
        image_files = list(seq_dir.glob(f"*{seq_info['format']}"))
        def complex_sort_key(path):
            numbers = re.findall(r'(\d+)', path.name)
            return int(numbers[-1]) if numbers else 0
        return sorted(image_files, key=complex_sort_key)

def load_frame_annotation(seq_id, frame_id, size):
    """加载帧标注"""
    labels_dir = Path(f"dataset/labels/{seq_id}")
    label_file = labels_dir / f"{frame_id}.txt"
    
    if not label_file.exists():
        return []
    
    # 加载类别映射
    with open('dataset/class.json', 'r', encoding='utf-8') as f:
        class_map = json.load(f)
    
    annotations = []
    try:
        with open(label_file, 'r') as f:
            for line in f:
                parts = line.strip().split()
                if len(parts) >= 5:
                    class_id = int(parts[0])
                    x_center = float(parts[1])
                    y_center = float(parts[2])
                    width = float(parts[3])
                    height = float(parts[4])
                    
                    # 转换为绝对坐标
                    img_width, img_height = size
                    x1 = (x_center - width/2) * img_width
                    y1 = (y_center - height/2) * img_height
                    x2 = (x_center + width/2) * img_width
                    y2 = (y_center + height/2) * img_height
                    
                    # 获取类别名称
                    class_name = class_map.get(str(class_id), "unknown")
                    
                    annotations.append({
                        "bbox": [x1, y1, x2, y2],
                        "label": class_name,
                        "frame_id": frame_id
                    })
    except Exception as e:
        logger.warning(f"加载标注文件失败 {label_file}: {e}")
    
    return annotations

def generate_real_sequence_training_data():
    """生成真正的完整序列训练数据 - 每个序列一个样本"""
    logger.info("生成真正的完整序列训练数据...")
    
    # 序列信息
    sequences = {
        'data01': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
        'data02': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
        'data04': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
        'data06': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
        'data07': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
        'data19': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
        'data23': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
        'data26': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'}
    }
    
    all_sequence_samples = []
    
    for seq_id, seq_info in sequences.items():
        print(f"\n处理序列 {seq_id}...")
        
        # 获取序列信息
        images_dir = Path(f"dataset/images/{seq_id}")
        
        # 获取图像文件
        image_files = get_image_files(images_dir, seq_info)
        
        total_frames = len(image_files)
        print(f"  总帧数: {total_frames}")
        
        # 每隔10帧抽取1帧，组成完整的长视频序列
        frame_interval = 10
        sampled_indices = list(range(0, total_frames, frame_interval))
        
        print(f"  抽取帧数: {len(sampled_indices)} (每隔{frame_interval}帧)")
        
        # 构建完整的长视频序列 - 这是关键！每个序列只生成一个样本
        video_frames_info = []
        all_annotations = []
        
        for frame_idx in sampled_indices:
            frame_file = image_files[frame_idx]
            frame_id = frame_file.stem
            
            # 记录帧信息
            video_frames_info.append({
                'frame_idx': frame_idx,
                'frame_id': frame_id,
                'file_path': str(frame_file)
            })
            
            # 加载该帧的标注
            annotations = load_frame_annotation(seq_id, frame_id, seq_info['size'])
            
            # 为每个标注添加帧信息
            for ann in annotations:
                ann['frame_id'] = frame_id
                ann['frame_idx'] = frame_idx
            
            all_annotations.extend(annotations)
        
        # 创建完整序列训练样本 - 每个序列只有一个样本！
        if len(video_frames_info) >= 3:  # 确保有足够的帧
            sequence_sample = {
                "conversations": [
                    {
                        "from": "human",
                        "value": f"这是序列{seq_id}的完整视频序列，包含{len(video_frames_info)}帧（每隔{frame_interval}帧抽取）。图像尺寸为{seq_info['size']}。请仔细检测序列中每一帧的弱小目标。目标类别包括：drone, car, ship, bus, pedestrian, cyclist。请按帧顺序输出所有检测结果，格式为JSON数组：[{{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\",\"frame_id\":\"帧号\"}}]"
                    },
                    {
                        "from": "gpt",
                        "value": json.dumps(all_annotations)
                    }
                ],
                "sequence_info": {
                    "sequence_id": seq_id,
                    "video_frames": video_frames_info,
                    "size": seq_info['size'],
                    "format": seq_info['format'],
                    "frame_count": len(video_frames_info),
                    "total_annotations": len(all_annotations),
                    "is_complete_sequence": True  # 标记这是完整序列
                }
            }
            
            all_sequence_samples.append(sequence_sample)
            print(f"  ✅ 生成完整序列样本: {len(video_frames_info)}帧, {len(all_annotations)}个标注")
        else:
            print(f"  ❌ 序列 {seq_id} 帧数不足，跳过")
    
    # 保存完整序列训练数据
    output_path = "data/real_sequence_training_data.json"
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(all_sequence_samples, f, ensure_ascii=False, indent=2)
    
    logger.info(f"真正的完整序列训练数据保存到: {output_path}")
    logger.info(f"总序列样本数: {len(all_sequence_samples)} (每个序列一个样本)")
    
    return True

class RealSequenceDataset(Dataset):
    """真正的完整序列数据集 - 适应Qwen2.5-VL-7B"""
    
    def __init__(self, data_path: str, processor):
        self.processor = processor
        with open(data_path, 'r', encoding='utf-8') as f:
            self.data = json.load(f)
        logger.info(f"加载了 {len(self.data)} 个完整序列训练样本")
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        sample = self.data[idx]
        
        # 加载完整视频序列帧
        sequence_info = sample['sequence_info']
        video_frames = []
        
        # 适应Qwen2.5-VL-7B的内存限制，但保持完整性
        max_frames = 3  # 每个完整序列最多3帧，严格控制内存
        frame_list = sequence_info['video_frames'][:max_frames]
        
        for frame_info in frame_list:
            try:
                image = Image.open(frame_info['file_path']).convert('RGB')
                # 适应Qwen2.5-VL-7B的标准尺寸
                image = image.resize((336, 336))
                video_frames.append(image)
            except Exception as e:
                logger.warning(f"无法加载图像 {frame_info['file_path']}: {e}")
                continue
        
        if len(video_frames) < 3:
            return None
        
        # 构建消息
        conversations = sample['conversations']
        messages = [
            {
                "role": "user",
                "content": [
                    {"type": "video", "video": video_frames},
                    {"type": "text", "text": conversations[0]['value']}
                ]
            },
            {
                "role": "assistant", 
                "content": conversations[1]['value']
            }
        ]
        
        return {
            'messages': messages,
            'sequence_info': sequence_info
        }

def real_sequence_collate_fn(batch, processor):
    """真正的完整序列数据整理函数"""
    # 过滤None样本
    batch = [item for item in batch if item is not None]
    if not batch:
        return None
    
    try:
        example = batch[0]
        messages = example["messages"]
        
        # 应用聊天模板
        text = processor.apply_chat_template(
            messages, tokenize=False, add_generation_prompt=True
        )
        
        # 处理视觉信息
        image_inputs, video_inputs = process_vision_info(messages)
        
        # 处理输入
        inputs = processor(
            text=[text],
            images=image_inputs,
            videos=video_inputs,
            padding=True,
            return_tensors="pt"
        )
        
        # 检查输入是否有效
        if inputs["input_ids"].numel() == 0:
            logger.warning("生成的input_ids为空，跳过此样本")
            return None
        
        # 为训练添加labels
        inputs["labels"] = inputs["input_ids"].clone()
        
        return inputs
        
    except Exception as e:
        logger.warning(f"处理样本时出错: {e}")
        return None

class RealSequenceTrainer:
    """真正的完整序列训练器 - 适应Qwen2.5-VL-7B"""
    
    def __init__(self, model_path: str):
        self.model_path = model_path
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        
        logger.info(f"使用设备: {self.device}")
        logger.info(f"模型路径: {model_path}")
        
        # 清理GPU内存
        torch.cuda.empty_cache()
        
        # 加载模型和处理器 - 适应Qwen2.5-VL-7B
        self.processor = AutoProcessor.from_pretrained(model_path)
        self.model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
            model_path,
            torch_dtype=torch.float16,
            device_map="auto",  # 自动分配到可见GPU
            trust_remote_code=True,
            low_cpu_mem_usage=True,
            use_cache=False,
            max_memory={0: "65GB"}  # 为Qwen2.5-VL-7B严格控制内存
        )
        
        logger.info("Qwen2.5-VL-7B模型和处理器加载完成")
    
    def setup_lora(self):
        """设置适应Qwen2.5-VL-7B的LoRA配置"""
        config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            r=8,  # 降低rank以适应7B模型
            lora_alpha=16,  # 相应调整alpha
            target_modules=["q_proj", "v_proj"],
            lora_dropout=0.1,
        )
        
        self.model = get_peft_model(self.model, config)
        self.model.print_trainable_parameters()
        logger.info("适应Qwen2.5-VL-7B的LoRA配置完成")
    
    def train(self, train_data_path: str, output_dir: str):
        """开始真正的完整序列训练"""
        logger.info("开始真正的完整序列训练...")
        
        # 创建数据集
        dataset = RealSequenceDataset(train_data_path, self.processor)
        
        # 创建数据加载器
        def collate_wrapper(batch):
            return real_sequence_collate_fn(batch, self.processor)
        
        # 适应Qwen2.5-VL-7B的训练参数 - 最小内存配置
        args = TrainingArguments(
            output_dir=output_dir,
            num_train_epochs=3,  # 减少训练轮数
            per_device_train_batch_size=1,
            gradient_accumulation_steps=1,  # 最小梯度累积
            learning_rate=1e-5,  # 较小的学习率适应7B模型
            fp16=True,
            save_steps=3,
            logging_steps=1,
            save_total_limit=1,
            remove_unused_columns=False,
            dataloader_drop_last=True,
            dataloader_num_workers=0,
            skip_memory_metrics=True,
            max_grad_norm=1.0,
            warmup_steps=1,
            lr_scheduler_type="linear",
            weight_decay=0.01,
        )
        
        # 创建训练器
        trainer = Trainer(
            model=self.model,
            args=args,
            train_dataset=dataset,
            data_collator=collate_wrapper,
        )
        
        # 开始训练
        logger.info("开始真正的完整序列微调训练，适应Qwen2.5-VL-7B...")
        trainer.train()
        
        # 保存模型
        trainer.save_model()
        self.processor.save_pretrained(output_dir)
        
        logger.info(f"真正的完整序列训练完成，模型保存到: {output_dir}")
        return True

def main():
    """主函数"""
    print("=== 真正的完整序列微调训练系统 ===")
    print("🎯 目标: 每个序列生成一个完整的长视频序列样本")
    print("📊 策略: 每隔10帧抽取组成完整长序列，每个序列一个样本")
    print("🎬 训练方式: 真正的完整序列微调，适应Qwen2.5-VL-7B")
    print("🔧 技术: 完整序列LoRA + 深度训练")
    print("⏱️  预计时间: 15-30分钟")
    
    # 第1步：生成完整序列训练数据
    print("\n第1步：生成真正的完整序列训练数据...")
    if not generate_real_sequence_training_data():
        print("❌ 完整序列数据生成失败")
        return False
    
    # 第2步：初始化训练器
    print("\n第2步：初始化完整序列训练器...")
    model_path = "qwen2.5-vl-7b"
    trainer = RealSequenceTrainer(model_path)
    
    # 第3步：设置LoRA
    print("\n第3步：设置适应Qwen2.5-VL-7B的LoRA配置...")
    trainer.setup_lora()
    
    # 第4步：开始训练
    print("\n第4步：开始真正的完整序列微调训练...")
    output_dir = "output/real_sequence_lora"
    success = trainer.train("data/real_sequence_training_data.json", output_dir)
    
    if success:
        print("\n🎉 真正的完整序列微调训练完成！")
        print(f"📁 模型保存到: {output_dir}")
        print("📋 下一步: 使用完整序列模型进行检测")
    else:
        print("\n❌ 完整序列训练失败！")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
