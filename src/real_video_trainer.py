#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真正的视频序列微调训练器
使用Qwen2.5-VL进行LoRA微调训练
"""

import os
import sys
import json
import torch
import logging
from pathlib import Path
from typing import List, Dict, Any
from PIL import Image
from torch.utils.data import Dataset, DataLoader
from transformers import (
    Qwen2_5_VLForConditionalGeneration,
    AutoProcessor,
    TrainingArguments,
    Trainer,
    DataCollatorWithPadding
)
from peft import LoraConfig, get_peft_model, TaskType
from qwen_vl_utils import process_vision_info
import numpy as np
from tqdm import tqdm

# 固定使用GPU卡2
os.environ['CUDA_VISIBLE_DEVICES'] = '2'

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class VideoSequenceDataset(Dataset):
    """视频序列数据集"""
    
    def __init__(self, data_path: str, processor):
        self.processor = processor
        with open(data_path, 'r', encoding='utf-8') as f:
            self.data = json.load(f)
        logger.info(f"加载了 {len(self.data)} 个训练样本")
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        sample = self.data[idx]

        # 加载视频帧
        frame_infos = sample['sequence_info']['video_frames']
        video_frames = []

        for frame_info in frame_infos:
            try:
                image = Image.open(frame_info['file_path']).convert('RGB')
                video_frames.append(image)
            except Exception as e:
                logger.warning(f"无法加载图像 {frame_info['file_path']}: {e}")
                continue
        
        if not video_frames:
            return None
        
        # 构建消息
        conversations = sample['conversations']
        messages = []
        
        for conv in conversations:
            if conv['from'] == 'human':
                message = {
                    "role": "user",
                    "content": [
                        {"type": "video", "video": video_frames},
                        {"type": "text", "text": conv['value']}
                    ]
                }
            else:
                message = {
                    "role": "assistant", 
                    "content": conv['value']
                }
            messages.append(message)
        
        return {
            'messages': messages,
            'sequence_info': sample['sequence_info']
        }

def collate_fn(batch, processor):
    """数据整理函数"""
    # 过滤None样本
    batch = [item for item in batch if item is not None]
    if not batch:
        return None

    # 只处理第一个样本，避免批次大小不匹配问题
    if len(batch) > 1:
        batch = [batch[0]]

    try:
        example = batch[0]
        messages = example["messages"]

        # 应用聊天模板
        text = processor.apply_chat_template(
            messages, tokenize=False, add_generation_prompt=True
        )

        # 处理视觉信息
        image_inputs, video_inputs = process_vision_info(messages)

        # 处理输入
        inputs = processor(
            text=[text],
            images=image_inputs,
            videos=video_inputs,
            padding=True,
            return_tensors="pt",
        )

        return inputs

    except Exception as e:
        logger.warning(f"处理样本时出错: {e}")
        return None

class VideoSequenceTrainer:
    """视频序列训练器"""
    
    def __init__(self, model_path: str, device: str = "auto"):
        self.model_path = model_path
        self.device = device if device != "auto" else ("cuda" if torch.cuda.is_available() else "cpu")
        
        logger.info(f"使用设备: {self.device}")
        logger.info(f"模型路径: {model_path}")
        
        # 加载模型和处理器
        self.processor = AutoProcessor.from_pretrained(model_path)
        self.model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
            model_path,
            torch_dtype=torch.float16 if self.device == "cuda" else torch.float32,
            device_map="auto" if self.device == "cuda" else None,
            trust_remote_code=True,
            low_cpu_mem_usage=True,
            use_cache=False  # 减少内存使用
        )
        
        logger.info("模型和处理器加载完成")
    
    def setup_lora(self, lora_config: Dict):
        """设置LoRA配置"""
        config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            r=lora_config.get('rank', 16),
            lora_alpha=lora_config.get('alpha', 32),
            target_modules=lora_config.get('target_modules', ["q_proj", "v_proj"]),
            lora_dropout=lora_config.get('dropout', 0.05),
        )
        
        self.model = get_peft_model(self.model, config)
        self.model.print_trainable_parameters()
        logger.info("LoRA配置完成")
    
    def train(self, train_data_path: str, output_dir: str, training_args: Dict):
        """开始训练"""
        logger.info("开始训练...")
        
        # 创建数据集
        dataset = VideoSequenceDataset(train_data_path, self.processor)
        
        # 创建数据加载器
        def collate_wrapper(batch):
            return collate_fn(batch, self.processor)
        
        dataloader = DataLoader(
            dataset,
            batch_size=training_args.get('batch_size', 1),
            shuffle=True,
            collate_fn=collate_wrapper,
            num_workers=0  # 避免多进程问题
        )
        
        # 训练参数
        args = TrainingArguments(
            output_dir=output_dir,
            num_train_epochs=training_args.get('num_epochs', 3),
            per_device_train_batch_size=training_args.get('batch_size', 1),
            gradient_accumulation_steps=training_args.get('gradient_accumulation_steps', 4),
            learning_rate=training_args.get('learning_rate', 5e-5),
            fp16=training_args.get('fp16', True),
            save_steps=training_args.get('save_steps', 100),
            logging_steps=training_args.get('logging_steps', 10),
            save_total_limit=2,
            remove_unused_columns=False,
            dataloader_drop_last=True,
        )
        
        # 创建训练器
        trainer = Trainer(
            model=self.model,
            args=args,
            train_dataset=dataset,
            data_collator=collate_wrapper,
        )
        
        # 开始训练
        trainer.train()
        
        # 保存模型
        trainer.save_model()
        self.processor.save_pretrained(output_dir)
        
        logger.info(f"训练完成，模型保存到: {output_dir}")
        return True

def generate_training_data():
    """生成训练数据"""
    from video_sequence_trainer import generate_all_sequences_training_data
    
    logger.info("生成视频序列训练数据...")
    training_data = generate_all_sequences_training_data()
    
    if not training_data:
        logger.error("训练数据生成失败")
        return False
    
    # 保存训练数据
    output_path = "data/training_data.json"
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(training_data, f, ensure_ascii=False, indent=2)
    
    logger.info(f"训练数据保存到: {output_path}")
    logger.info(f"生成了 {len(training_data)} 个训练样本")
    
    return True

def main():
    """主函数"""
    print("=== 真正的视频序列微调训练 ===")
    print("🎯 目标: 使用Qwen2.5-VL进行LoRA微调")
    print("📊 策略: 视频序列微调 (每隔10帧抽取1帧)")
    print("🔧 技术: LoRA + 视频序列处理")
    
    # 第1步：生成训练数据
    print("\n第1步：生成训练数据...")
    if not generate_training_data():
        print("❌ 训练数据生成失败")
        return False
    
    # 第2步：初始化训练器
    print("\n第2步：初始化训练器...")
    model_path = "qwen2.5-vl-7b"
    trainer = VideoSequenceTrainer(model_path)
    
    # 第3步：设置LoRA
    print("\n第3步：设置LoRA配置...")
    lora_config = {
        'rank': 16,
        'alpha': 32,
        'target_modules': ["q_proj", "v_proj"],
        'dropout': 0.05
    }
    trainer.setup_lora(lora_config)
    
    # 第4步：开始训练
    print("\n第4步：开始微调训练...")
    print("🔧 A100-80GB优化配置:")
    print("   - 批次大小: 2")
    print("   - 梯度累积: 4步")
    print("   - 训练轮数: 3轮")
    print("   - 学习率: 5e-5")
    print("   - 精度: FP16")

    training_args = {
        'batch_size': 1,  # 使用批次大小1避免张量大小不匹配
        'gradient_accumulation_steps': 8,  # 有效批次大小 = 1*8 = 8
        'num_epochs': 3,  # 增加训练轮数
        'learning_rate': 5e-5,  # 适中的学习率
        'fp16': True,
        'save_steps': 50,
        'logging_steps': 10
    }
    
    output_dir = "output/video_sequence_lora"
    success = trainer.train("data/training_data.json", output_dir, training_args)
    
    if success:
        print("\n🎉 微调训练完成！")
        print(f"📁 模型保存到: {output_dir}")
        print("📋 下一步: 使用微调后的模型进行检测")
    else:
        print("\n❌ 微调训练失败！")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
