#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化优化的Qwen2.5-VL微调训练器 - 修复梯度问题
"""

import os
import sys
import json
import torch
import logging
from pathlib import Path
from typing import List, Dict, Any
from PIL import Image
from transformers import (
    Qwen2_5_VLForConditionalGeneration,
    AutoProcessor,
)
from peft import LoraConfig, get_peft_model, TaskType
import gc
import re

# 强制使用GPU卡2
os.environ['CUDA_VISIBLE_DEVICES'] = '2'
torch.cuda.set_device(0)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def clear_memory():
    """清理显存"""
    gc.collect()
    torch.cuda.empty_cache()

def get_image_files(seq_dir: Path, seq_info: Dict) -> List[Path]:
    """获取图像文件列表"""
    if seq_info['naming'] == 'simple':
        image_files = list(seq_dir.glob(f"*{seq_info['format']}"))
        return sorted(image_files, key=lambda x: int(x.stem))
    else:
        image_files = list(seq_dir.glob(f"*{seq_info['format']}"))
        def complex_sort_key(path):
            numbers = re.findall(r'(\d+)', path.name)
            return int(numbers[-1]) if numbers else 0
        return sorted(image_files, key=complex_sort_key)

def load_frame_annotation(seq_id, frame_id, size):
    """加载帧标注"""
    labels_dir = Path(f"dataset/labels/{seq_id}")
    label_file = labels_dir / f"{frame_id}.txt"
    
    if not label_file.exists():
        return []
    
    with open('dataset/class.json', 'r', encoding='utf-8') as f:
        class_map = json.load(f)
    
    annotations = []
    try:
        with open(label_file, 'r') as f:
            for line in f:
                parts = line.strip().split()
                if len(parts) >= 5:
                    class_id = int(parts[0])
                    x_center = float(parts[1])
                    y_center = float(parts[2])
                    width = float(parts[3])
                    height = float(parts[4])
                    
                    # 转换为绝对坐标
                    img_width, img_height = size
                    x1 = int((x_center - width/2) * img_width)
                    y1 = int((y_center - height/2) * img_height)
                    x2 = int((x_center + width/2) * img_width)
                    y2 = int((y_center + height/2) * img_height)
                    
                    class_name = class_map.get(str(class_id), "unknown")
                    
                    annotations.append({
                        "bbox": [x1, y1, x2, y2],
                        "label": class_name,
                        "frame_id": frame_id
                    })
    except Exception as e:
        logger.warning(f"加载标注文件失败 {label_file}: {e}")
    
    return annotations

def generate_simple_optimized_training_data():
    """生成简化优化的训练数据"""
    logger.info("生成简化优化的训练数据...")
    
    sequences = {
        'data01': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
        'data02': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
    }
    
    all_training_samples = []
    
    for seq_id, seq_info in sequences.items():
        print(f"处理序列 {seq_id}...")
        
        images_dir = Path(f"dataset/images/{seq_id}")
        image_files = get_image_files(images_dir, seq_info)
        
        # 选择有标注的帧生成训练样本
        for i in range(0, min(len(image_files), 50), 10):  # 每10帧选一个
            frame_file = image_files[i]
            frame_id = frame_file.stem
            
            # 加载标注
            annotations = load_frame_annotation(seq_id, frame_id, seq_info['size'])
            
            if annotations:  # 只有有标注的帧才生成训练样本
                # 优化的提示词
                training_sample = {
                    "messages": [
                        {
                            "role": "user",
                            "content": [
                                {
                                    "type": "image",
                                    "image": str(frame_file)
                                },
                                {
                                    "type": "text",
                                    "text": f"""检测这张红外图像中的弱小目标。

弱小目标特征：
- 尺寸很小（5-20像素）
- 在红外图像中呈现为亮点或暗点
- 目标类别：drone, car, ship, bus, pedestrian, cyclist

输出格式：[{{"bbox":[x1,y1,x2,y2],"label":"类别"}}]

开始检测："""
                                }
                            ]
                        },
                        {
                            "role": "assistant",
                            "content": json.dumps(annotations, ensure_ascii=False)
                        }
                    ]
                }
                
                all_training_samples.append(training_sample)
                print(f"  ✅ 生成样本: 帧{frame_id}, {len(annotations)}个标注")
    
    # 保存训练数据
    output_path = "data/simple_optimized_training_data.json"
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(all_training_samples, f, ensure_ascii=False, indent=2)
    
    logger.info(f"简化优化训练数据保存到: {output_path}")
    logger.info(f"总训练样本数: {len(all_training_samples)}")
    
    return True

class SimpleOptimizedTrainer:
    """简化优化的训练器"""
    
    def __init__(self, model_path: str):
        self.model_path = model_path
        self.device = "cuda"
        
        clear_memory()
        
        logger.info("加载简化优化模型...")
        
        self.processor = AutoProcessor.from_pretrained(model_path)
        self.model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
            model_path,
            torch_dtype=torch.float16,
            device_map="auto",
            trust_remote_code=True,
            low_cpu_mem_usage=True,
            use_cache=False,
        )
        
        logger.info("简化优化模型加载完成")
    
    def setup_lora(self):
        """设置优化的LoRA"""
        config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            r=4,  # 适中的rank
            lora_alpha=8,
            target_modules=["q_proj", "v_proj"],  # 两个目标模块
            lora_dropout=0.1,
        )
        
        self.model = get_peft_model(self.model, config)
        self.model.print_trainable_parameters()
        logger.info("简化优化LoRA配置完成")
    
    def simple_train(self, train_data_path: str, output_dir: str):
        """简化的训练"""
        logger.info("开始简化优化微调...")
        
        clear_memory()
        
        # 设置LoRA
        self.setup_lora()
        
        # 加载训练数据
        with open(train_data_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 设置优化器
        optimizer = torch.optim.AdamW(self.model.parameters(), lr=2e-5)
        
        # 训练循环
        self.model.train()
        
        for epoch in range(5):  # 5个轮次
            logger.info(f"训练轮次 {epoch + 1}/5")
            
            epoch_loss = 0
            for i, sample in enumerate(data):
                messages = sample["messages"]
                
                try:
                    # 处理输入
                    text = self.processor.apply_chat_template(
                        messages, tokenize=False, add_generation_prompt=True
                    )
                    
                    # 加载图像
                    image_path = messages[0]["content"][0]["image"]
                    image = Image.open(image_path).convert('RGB')
                    image = image.resize((256, 256))  # 适中的分辨率
                    
                    # 处理输入
                    inputs = self.processor(
                        text=[text],
                        images=[image],
                        padding=True,
                        return_tensors="pt"
                    )
                    
                    # 移动到设备
                    inputs = {k: v.to(self.device) for k, v in inputs.items()}
                    inputs["labels"] = inputs["input_ids"].clone()
                    
                    # 前向传播
                    optimizer.zero_grad()
                    outputs = self.model(**inputs)
                    loss = outputs.loss
                    
                    # 反向传播
                    loss.backward()
                    optimizer.step()
                    
                    epoch_loss += loss.item()
                    
                    if (i + 1) % 2 == 0:
                        logger.info(f"  样本 {i+1}/{len(data)}, 损失: {loss.item():.4f}")
                    
                    clear_memory()
                    
                except Exception as e:
                    logger.warning(f"处理样本 {i} 时出错: {e}")
                    continue
            
            avg_loss = epoch_loss / len(data)
            logger.info(f"轮次 {epoch + 1} 平均损失: {avg_loss:.4f}")
        
        # 保存模型
        os.makedirs(output_dir, exist_ok=True)
        self.model.save_pretrained(output_dir)
        self.processor.save_pretrained(output_dir)
        
        logger.info(f"简化优化微调完成，模型保存到: {output_dir}")
        return True

def main():
    """主函数"""
    print("=== 简化优化Qwen2.5-VL微调训练系统 ===")
    print("🎯 目标: 提升检测效果，避免梯度问题")
    print("📊 策略: 增加训练数据+优化LoRA+简化训练")
    print("🎬 训练方式: 简化优化LoRA微调")
    print("🔧 技术: 适中LoRA + 多样化训练数据")
    print("⏱️  预计时间: 10-15分钟")
    
    # 第1步：生成训练数据
    print("\n第1步：生成简化优化训练数据...")
    if not generate_simple_optimized_training_data():
        print("❌ 训练数据生成失败")
        return False
    
    # 第2步：初始化训练器
    print("\n第2步：初始化简化优化训练器...")
    model_path = "qwen2.5-vl-7b"
    trainer = SimpleOptimizedTrainer(model_path)
    
    # 第3步：开始训练
    print("\n第3步：开始简化优化微调...")
    output_dir = "output/simple_optimized_qwen_finetuned"
    success = trainer.simple_train("data/simple_optimized_training_data.json", output_dir)
    
    if success:
        print("\n🎉 简化优化微调完成！")
        print(f"📁 模型保存到: {output_dir}")
        print("📋 这是简化优化版本")
        print("💡 应该有更好的检测效果")
    else:
        print("\n❌ 简化优化微调失败！")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
