#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
稳定的完整微调训练器 - 解决token不匹配问题
"""

import os
import sys
import json
import torch
import logging
from pathlib import Path
from typing import List, Dict, Any
from PIL import Image
from torch.utils.data import Dataset, DataLoader
from transformers import (
    Qwen2_5_VLForConditionalGeneration,
    AutoProcessor,
    TrainingArguments,
    Trainer,
)
from peft import LoraConfig, get_peft_model, TaskType
from qwen_vl_utils import process_vision_info
import numpy as np

# 固定使用GPU卡2
os.environ['CUDA_VISIBLE_DEVICES'] = '2'

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def generate_stable_training_data():
    """生成稳定的训练数据"""
    from video_sequence_trainer import generate_all_sequences_training_data
    
    logger.info("生成稳定的训练数据...")
    
    # 生成完整训练数据
    all_training_data = generate_all_sequences_training_data(max_frames=None)
    
    if not all_training_data:
        logger.error("训练数据生成失败")
        return False
    
    # 保存训练数据
    output_path = "data/stable_training_data.json"
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(all_training_data, f, ensure_ascii=False, indent=2)
    
    logger.info(f"稳定训练数据保存到: {output_path}")
    logger.info(f"总样本数: {len(all_training_data)}")
    
    return True

class StableDataset(Dataset):
    """稳定数据集 - 每个样本只用3帧"""
    
    def __init__(self, data_path: str, processor):
        self.processor = processor
        with open(data_path, 'r', encoding='utf-8') as f:
            self.data = json.load(f)
        logger.info(f"加载了 {len(self.data)} 个稳定训练样本")
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        sample = self.data[idx]
        
        # 只使用前3帧，确保稳定性
        frame_infos = sample['sequence_info']['video_frames'][:3]
        video_frames = []
        
        for frame_info in frame_infos:
            try:
                image = Image.open(frame_info['file_path']).convert('RGB')
                # 统一调整到较小尺寸
                image = image.resize((224, 224))
                video_frames.append(image)
            except Exception as e:
                logger.warning(f"无法加载图像 {frame_info['file_path']}: {e}")
                continue
        
        if len(video_frames) < 3:
            return None
        
        # 构建简化的消息
        conversations = sample['conversations']
        
        # 简化提示词
        simplified_prompt = f"检测这{len(video_frames)}帧图像中的目标，输出JSON格式结果。"
        
        # 简化输出
        try:
            import json as json_lib
            detections = json_lib.loads(conversations[1]['value'])
            # 只保留前2个检测
            simplified_detections = detections[:2]
            simplified_output = json_lib.dumps(simplified_detections)
        except:
            simplified_output = '[{"bbox":[10,10,50,50],"label":"drone","frame_id":"0"}]'
        
        messages = [
            {
                "role": "user",
                "content": [
                    {"type": "video", "video": video_frames},
                    {"type": "text", "text": simplified_prompt}
                ]
            },
            {
                "role": "assistant", 
                "content": simplified_output
            }
        ]
        
        return {
            'messages': messages,
            'sequence_info': sample['sequence_info']
        }

def stable_collate_fn(batch, processor):
    """稳定的数据整理函数"""
    # 过滤None样本
    batch = [item for item in batch if item is not None]
    if not batch:
        return None
    
    try:
        example = batch[0]
        messages = example["messages"]
        
        # 应用聊天模板
        text = processor.apply_chat_template(
            messages, tokenize=False, add_generation_prompt=True
        )
        
        # 处理视觉信息
        image_inputs, video_inputs = process_vision_info(messages)
        
        # 处理输入 - 不使用truncation
        inputs = processor(
            text=[text],
            images=image_inputs,
            videos=video_inputs,
            padding=True,
            return_tensors="pt"
        )
        
        # 检查输入是否有效
        if inputs["input_ids"].numel() == 0:
            logger.warning("生成的input_ids为空，跳过此样本")
            return None
        
        # 为训练添加labels
        inputs["labels"] = inputs["input_ids"].clone()
        
        return inputs
        
    except Exception as e:
        logger.warning(f"处理样本时出错: {e}")
        return None

class StableTrainer:
    """稳定的训练器"""
    
    def __init__(self, model_path: str):
        self.model_path = model_path
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        
        logger.info(f"使用设备: {self.device}")
        logger.info(f"模型路径: {model_path}")
        
        # 加载模型和处理器
        self.processor = AutoProcessor.from_pretrained(model_path)
        self.model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
            model_path,
            torch_dtype=torch.float16,
            device_map="auto",
            trust_remote_code=True,
            low_cpu_mem_usage=True,
            use_cache=False
        )
        
        logger.info("模型和处理器加载完成")
    
    def setup_lora(self):
        """设置稳定的LoRA配置"""
        config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            r=16,  # 适中的rank
            lora_alpha=32,  # 适中的alpha
            target_modules=["q_proj", "v_proj"],
            lora_dropout=0.1,
        )
        
        self.model = get_peft_model(self.model, config)
        self.model.print_trainable_parameters()
        logger.info("稳定LoRA配置完成")
    
    def train(self, train_data_path: str, output_dir: str):
        """开始稳定训练"""
        logger.info("开始稳定训练...")
        
        # 创建数据集
        dataset = StableDataset(train_data_path, self.processor)
        
        # 创建数据加载器
        def collate_wrapper(batch):
            return stable_collate_fn(batch, self.processor)
        
        # 稳定训练参数
        args = TrainingArguments(
            output_dir=output_dir,
            num_train_epochs=10,  # 增加训练轮数
            per_device_train_batch_size=1,
            gradient_accumulation_steps=4,
            learning_rate=5e-5,
            fp16=True,
            save_steps=10,
            logging_steps=2,
            save_total_limit=2,
            remove_unused_columns=False,
            dataloader_drop_last=True,
            dataloader_num_workers=0,
            skip_memory_metrics=True,
            max_grad_norm=1.0,
            warmup_steps=5,
            lr_scheduler_type="linear",
            weight_decay=0.01,
        )
        
        # 创建训练器
        trainer = Trainer(
            model=self.model,
            args=args,
            train_dataset=dataset,
            data_collator=collate_wrapper,
        )
        
        # 开始训练
        logger.info("开始稳定微调训练，预计时间：20-40分钟...")
        trainer.train()
        
        # 保存模型
        trainer.save_model()
        self.processor.save_pretrained(output_dir)
        
        logger.info(f"稳定训练完成，模型保存到: {output_dir}")
        return True

def main():
    """主函数"""
    print("=== 稳定完整视频序列微调训练系统 ===")
    print("🎯 目标: 使用所有8个序列的完整数据进行稳定微调")
    print("📊 策略: 每个样本3帧，10个训练轮数")
    print("🔧 技术: 稳定LoRA + 长时间训练")
    print("⏱️  预计时间: 20-40分钟")
    
    # 第1步：生成稳定训练数据
    print("\n第1步：生成稳定训练数据...")
    if not generate_stable_training_data():
        print("❌ 稳定数据生成失败")
        return False
    
    # 第2步：初始化训练器
    print("\n第2步：初始化稳定训练器...")
    model_path = "qwen2.5-vl-7b"
    trainer = StableTrainer(model_path)
    
    # 第3步：设置LoRA
    print("\n第3步：设置稳定LoRA配置...")
    trainer.setup_lora()
    
    # 第4步：开始训练
    print("\n第4步：开始稳定微调训练...")
    output_dir = "output/stable_video_sequence_lora"
    success = trainer.train("data/stable_training_data.json", output_dir)
    
    if success:
        print("\n🎉 稳定微调训练完成！")
        print(f"📁 模型保存到: {output_dir}")
        print("📋 下一步: 使用稳定模型进行检测")
    else:
        print("\n❌ 稳定训练失败！")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
