#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
超强Qwen2.5-VL检测器 - 最大化检测效果
多策略融合检测，最强JSON解析，最优插值算法
"""

import os
import sys
import json
import torch
import logging
from pathlib import Path
from typing import List, Dict, Any
from PIL import Image
from transformers import Qwen2_5_VLForConditionalGeneration, AutoProcessor
from peft import PeftModel
import re
import numpy as np
from collections import defaultdict

# 固定使用GPU卡2
os.environ['CUDA_VISIBLE_DEVICES'] = '2'

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SuperchargedQwenDetector:
    """超强Qwen2.5-VL检测器"""
    
    def __init__(self, base_model_path: str, lora_model_path: str):
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        
        logger.info(f"使用设备: {self.device}")
        logger.info(f"基础模型: {base_model_path}")
        logger.info(f"LoRA模型: {lora_model_path}")
        
        # 加载基础模型
        self.processor = AutoProcessor.from_pretrained(base_model_path)
        self.model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
            base_model_path,
            torch_dtype=torch.float16,
            device_map="auto",
            trust_remote_code=True,
            low_cpu_mem_usage=True
        )
        
        # 加载LoRA微调模型
        logger.info("加载LoRA微调模型...")
        self.model = PeftModel.from_pretrained(self.model, lora_model_path)
        logger.info("超强Qwen2.5-VL LoRA微调模型加载完成")
        
        # 序列信息
        self.sequences = {
            'data01': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data02': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data04': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data06': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data07': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data19': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
            'data23': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
            'data26': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'}
        }
        
        # 加载类别映射
        with open('dataset/class.json', 'r', encoding='utf-8') as f:
            self.class_map = json.load(f)
    
    def get_image_files(self, seq_dir: Path, seq_info: Dict) -> List[Path]:
        """获取图像文件列表"""
        if seq_info['naming'] == 'simple':
            image_files = list(seq_dir.glob(f"*{seq_info['format']}"))
            return sorted(image_files, key=lambda x: int(x.stem))
        else:
            image_files = list(seq_dir.glob(f"*{seq_info['format']}"))
            def complex_sort_key(path):
                numbers = re.findall(r'(\d+)', path.name)
                return int(numbers[-1]) if numbers else 0
            return sorted(image_files, key=complex_sort_key)
    
    def ultra_robust_json_parse(self, text: str) -> List[Dict]:
        """超强JSON解析 - 最大化解析成功率"""
        try:
            # 第一层：标准清理
            clean_text = text.strip()
            
            # 移除各种markdown标记
            patterns_to_remove = [
                r'```json\s*',
                r'```\s*',
                r'json\s*',
                r'JSON\s*',
                r'检测结果[：:]\s*',
                r'输出[：:]\s*',
                r'结果[：:]\s*'
            ]
            
            for pattern in patterns_to_remove:
                clean_text = re.sub(pattern, '', clean_text, flags=re.IGNORECASE)
            
            clean_text = clean_text.strip()
            
            # 第二层：查找JSON数组
            json_patterns = [
                r'\[.*?\]',  # 标准数组
                r'\{.*?\}',  # 单个对象
            ]
            
            for pattern in json_patterns:
                matches = re.findall(pattern, clean_text, re.DOTALL)
                for match in matches:
                    try:
                        # 尝试直接解析
                        result = json.loads(match)
                        if isinstance(result, list):
                            return result
                        elif isinstance(result, dict):
                            return [result]
                    except:
                        continue
            
            # 第三层：修复常见JSON格式问题
            json_start = clean_text.find('[')
            json_end = clean_text.rfind(']')
            
            if json_start != -1 and json_end != -1 and json_end > json_start:
                json_str = clean_text[json_start:json_end+1]
                
                # 修复格式问题
                fixes = [
                    (r"'", '"'),  # 单引号改双引号
                    (r'(\w+):', r'"\1":'),  # 给键加引号
                    (r':\s*([^",\[\]{}]+)(?=\s*[,}])', r': "\1"'),  # 给值加引号
                    (r'"\s*(\d+)\s*"', r'\1'),  # 数字不要引号
                    (r'"\s*(true|false|null)\s*"', r'\1'),  # 布尔值不要引号
                ]
                
                for pattern, replacement in fixes:
                    json_str = re.sub(pattern, replacement, json_str)
                
                try:
                    result = json.loads(json_str)
                    if isinstance(result, list):
                        return result
                    elif isinstance(result, dict):
                        return [result]
                except:
                    pass
            
            # 第四层：正则表达式强制提取
            bbox_patterns = [
                r'\[(\d+),\s*(\d+),\s*(\d+),\s*(\d+)\]',
                r'bbox["\']?\s*:\s*\[(\d+),\s*(\d+),\s*(\d+),\s*(\d+)\]',
                r'(\d+),\s*(\d+),\s*(\d+),\s*(\d+)',
            ]
            
            label_patterns = [
                r'"label":\s*"([^"]+)"',
                r"'label':\s*'([^']+)'",
                r'label["\']?\s*:\s*["\']([^"\']+)["\']',
                r'"([^"]*(?:drone|car|ship|bus|pedestrian|cyclist)[^"]*)"',
            ]
            
            for bbox_pattern in bbox_patterns:
                bboxes = re.findall(bbox_pattern, text)
                if bboxes:
                    labels = []
                    for label_pattern in label_patterns:
                        labels = re.findall(label_pattern, text, re.IGNORECASE)
                        if labels:
                            break
                    
                    if not labels:
                        labels = ['drone'] * len(bboxes)
                    
                    detections = []
                    for i, bbox in enumerate(bboxes):
                        if len(bbox) == 4:
                            label = labels[i] if i < len(labels) else 'drone'
                            detections.append({
                                "bbox": [int(bbox[0]), int(bbox[1]), int(bbox[2]), int(bbox[3])],
                                "label": label.lower()
                            })
                    
                    if detections:
                        return detections
            
            # 第五层：智能推断
            numbers = re.findall(r'\d+', text)
            if len(numbers) >= 4:
                # 尝试将数字组合成bbox
                detections = []
                for i in range(0, len(numbers)-3, 4):
                    bbox = [int(numbers[i]), int(numbers[i+1]), int(numbers[i+2]), int(numbers[i+3])]
                    # 验证bbox合理性
                    if (bbox[2] > bbox[0] and bbox[3] > bbox[1] and 
                        bbox[0] >= 0 and bbox[1] >= 0 and 
                        bbox[2] <= 1000 and bbox[3] <= 1000):
                        detections.append({
                            "bbox": bbox,
                            "label": "drone"
                        })
                
                if detections:
                    return detections
            
            return []
            
        except Exception as e:
            logger.warning(f"超强JSON解析失败: {e}")
            return []
    
    def multi_strategy_detection(self, image_path: str, frame_id: str, seq_id: str) -> List[Dict]:
        """多策略融合检测 - 最大化检测成功率"""
        seq_info = self.sequences[seq_id]
        all_detections = []
        
        # 策略1: 标准检测
        try:
            messages = [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "image",
                            "image": image_path
                        },
                        {
                            "type": "text",
                            "text": f"""你是世界顶级的红外弱小目标检测专家。请精确检测这张红外图像中的所有弱小目标。

图像详细信息：
- 序列编号：{seq_id}
- 帧编号：{frame_id}
- 图像尺寸：{seq_info['size']}

弱小目标详细特征：
- 尺寸极小：通常只有5-20像素
- 外观特征：在红外图像中呈现为亮点、暗点或小块状
- 目标类别：drone, car, ship, bus, pedestrian, cyclist

专业检测要求：
1. 仔细扫描整个图像，特别关注小亮点和异常区域
2. 边界框必须紧贴目标，精确到像素级别
3. 坐标格式为[x1,y1,x2,y2]
4. 如果没有发现任何目标，输出空数组[]

输出格式（严格遵守JSON格式）：
[{{"bbox":[x1,y1,x2,y2],"label":"目标类别"}}]

现在开始专业检测："""
                        }
                    ]
                }
            ]
            
            detections1 = self._generate_detection(messages, image_path)
            all_detections.extend(detections1)
            
        except Exception as e:
            logger.warning(f"策略1检测失败: {e}")
        
        # 策略2: 简化检测
        try:
            messages = [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "image",
                            "image": image_path
                        },
                        {
                            "type": "text",
                            "text": f"""检测红外图像中的弱小目标。目标很小（5-20像素），可能是drone, car, ship, bus, pedestrian, cyclist。

输出JSON格式：[{{"bbox":[x1,y1,x2,y2],"label":"类别"}}]

开始检测："""
                        }
                    ]
                }
            ]
            
            detections2 = self._generate_detection(messages, image_path)
            all_detections.extend(detections2)
            
        except Exception as e:
            logger.warning(f"策略2检测失败: {e}")
        
        # 策略3: 引导式检测
        try:
            messages = [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "image",
                            "image": image_path
                        },
                        {
                            "type": "text",
                            "text": f"""这是红外图像，请找出所有小目标。

步骤：
1. 扫描图像寻找亮点或暗点
2. 确定是否为目标（drone/car/ship/bus/pedestrian/cyclist）
3. 给出精确边界框

格式：[{{"bbox":[x1,y1,x2,y2],"label":"类别"}}]"""
                        }
                    ]
                }
            ]
            
            detections3 = self._generate_detection(messages, image_path)
            all_detections.extend(detections3)
            
        except Exception as e:
            logger.warning(f"策略3检测失败: {e}")
        
        # 去重和合并
        unique_detections = self._merge_detections(all_detections, seq_info)
        
        # 添加帧信息
        for detection in unique_detections:
            detection['frame_idx'] = int(frame_id)
            detection['frame_id'] = frame_id
        
        logger.info(f"帧 {frame_id} 多策略检测: {len(unique_detections)} 个目标")
        return unique_detections
    
    def _generate_detection(self, messages: List[Dict], image_path: str) -> List[Dict]:
        """生成检测结果"""
        try:
            # 应用聊天模板
            text = self.processor.apply_chat_template(
                messages, tokenize=False, add_generation_prompt=True
            )
            
            # 加载和处理图像
            image = Image.open(image_path).convert('RGB')
            image.thumbnail((512, 512), Image.Resampling.LANCZOS)
            
            # 处理输入
            inputs = self.processor(
                text=[text],
                images=[image],
                padding=True,
                return_tensors="pt"
            )
            
            # 移动到设备
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # 多次生成取最佳结果
            best_detections = []
            for temp in [0.1, 0.3, 0.5]:
                with torch.no_grad():
                    outputs = self.model.generate(
                        **inputs,
                        max_new_tokens=1024,
                        do_sample=True,
                        temperature=temp,
                        top_p=0.9,
                        repetition_penalty=1.1,
                        pad_token_id=self.processor.tokenizer.eos_token_id,
                    )
                
                # 解码输出
                generated_text = self.processor.decode(
                    outputs[0][inputs["input_ids"].shape[1]:], 
                    skip_special_tokens=True
                )
                
                # 解析结果
                detections = self.ultra_robust_json_parse(generated_text)
                if len(detections) > len(best_detections):
                    best_detections = detections
            
            return best_detections
            
        except Exception as e:
            logger.warning(f"生成检测失败: {e}")
            return []
    
    def _merge_detections(self, detections: List[Dict], seq_info: Dict) -> List[Dict]:
        """合并和去重检测结果"""
        if not detections:
            return []
        
        # 验证和修正bbox
        valid_detections = []
        for det in detections:
            if isinstance(det, dict) and 'bbox' in det and 'label' in det:
                bbox = det['bbox']
                if isinstance(bbox, list) and len(bbox) == 4:
                    try:
                        # 修正坐标 - 添加错误处理
                        img_w, img_h = seq_info['size']
                        bbox[0] = max(0, min(int(float(bbox[0])), img_w))
                        bbox[1] = max(0, min(int(float(bbox[1])), img_h))
                        bbox[2] = max(bbox[0], min(int(float(bbox[2])), img_w))
                        bbox[3] = max(bbox[1], min(int(float(bbox[3])), img_h))

                        # 验证bbox大小
                        if bbox[2] > bbox[0] and bbox[3] > bbox[1]:
                            det['bbox'] = bbox
                            valid_detections.append(det)
                    except (ValueError, TypeError) as e:
                        logger.warning(f"无效的bbox坐标: {bbox}, 错误: {e}")
                        continue
        
        # 去重：合并重叠的检测框
        if len(valid_detections) <= 1:
            return valid_detections
        
        def calculate_iou(box1, box2):
            x1 = max(box1[0], box2[0])
            y1 = max(box1[1], box2[1])
            x2 = min(box1[2], box2[2])
            y2 = min(box1[3], box2[3])
            
            if x2 <= x1 or y2 <= y1:
                return 0.0
            
            intersection = (x2 - x1) * (y2 - y1)
            area1 = (box1[2] - box1[0]) * (box1[3] - box1[1])
            area2 = (box2[2] - box2[0]) * (box2[3] - box2[1])
            union = area1 + area2 - intersection
            
            return intersection / union if union > 0 else 0.0
        
        # NMS去重
        merged_detections = []
        used = set()
        
        for i, det1 in enumerate(valid_detections):
            if i in used:
                continue
            
            current_group = [det1]
            used.add(i)
            
            for j, det2 in enumerate(valid_detections):
                if j in used or i == j:
                    continue
                
                iou = calculate_iou(det1['bbox'], det2['bbox'])
                if iou > 0.3:  # 重叠阈值
                    current_group.append(det2)
                    used.add(j)
            
            # 合并组内检测
            if len(current_group) == 1:
                merged_detections.append(current_group[0])
            else:
                # 取平均坐标
                avg_bbox = [0, 0, 0, 0]
                labels = [det['label'] for det in current_group]
                most_common_label = max(set(labels), key=labels.count)
                
                for det in current_group:
                    for k in range(4):
                        avg_bbox[k] += det['bbox'][k]
                
                for k in range(4):
                    avg_bbox[k] = int(avg_bbox[k] / len(current_group))
                
                merged_detections.append({
                    'bbox': avg_bbox,
                    'label': most_common_label
                })
        
        return merged_detections
    
    def advanced_interpolation(self, key_frame_detections: Dict[int, List[Dict]], all_frame_indices: List[int]) -> Dict[int, List[Dict]]:
        """高级插值算法 - 考虑目标轨迹"""
        all_detections = {}
        
        # 复制关键帧检测结果
        for frame_idx in key_frame_detections:
            all_detections[frame_idx] = key_frame_detections[frame_idx]
        
        # 构建目标轨迹
        trajectories = self._build_trajectories(key_frame_detections)
        
        # 对每个缺失帧进行高级插值
        for frame_idx in all_frame_indices:
            if frame_idx not in all_detections:
                interpolated_detections = []
                
                # 基于轨迹的插值
                for trajectory in trajectories:
                    interpolated_det = self._interpolate_from_trajectory(trajectory, frame_idx)
                    if interpolated_det:
                        interpolated_detections.append(interpolated_det)
                
                all_detections[frame_idx] = interpolated_detections
        
        return all_detections
    
    def _build_trajectories(self, key_frame_detections: Dict[int, List[Dict]]) -> List[List[Dict]]:
        """构建目标轨迹"""
        trajectories = []
        used_detections = set()
        
        sorted_frames = sorted(key_frame_detections.keys())
        
        for i, frame_idx in enumerate(sorted_frames):
            detections = key_frame_detections[frame_idx]
            
            for j, det in enumerate(detections):
                if (frame_idx, j) in used_detections:
                    continue
                
                # 开始新轨迹
                trajectory = [{'frame_idx': frame_idx, 'detection': det}]
                used_detections.add((frame_idx, j))
                
                # 向后寻找匹配的检测
                for next_frame_idx in sorted_frames[i+1:]:
                    next_detections = key_frame_detections[next_frame_idx]
                    best_match = None
                    best_distance = float('inf')
                    best_idx = -1
                    
                    for k, next_det in enumerate(next_detections):
                        if (next_frame_idx, k) in used_detections:
                            continue
                        
                        # 计算距离
                        distance = self._calculate_detection_distance(det, next_det)
                        if distance < best_distance and distance < 50:  # 距离阈值
                            best_distance = distance
                            best_match = next_det
                            best_idx = k
                    
                    if best_match:
                        trajectory.append({'frame_idx': next_frame_idx, 'detection': best_match})
                        used_detections.add((next_frame_idx, best_idx))
                        det = best_match  # 更新当前检测用于下次匹配
                    else:
                        break
                
                if len(trajectory) >= 2:  # 至少需要2个点才能形成轨迹
                    trajectories.append(trajectory)
        
        return trajectories
    
    def _calculate_detection_distance(self, det1: Dict, det2: Dict) -> float:
        """计算两个检测之间的距离"""
        bbox1 = det1['bbox']
        bbox2 = det2['bbox']
        
        # 中心点距离
        center1 = [(bbox1[0] + bbox1[2]) / 2, (bbox1[1] + bbox1[3]) / 2]
        center2 = [(bbox2[0] + bbox2[2]) / 2, (bbox2[1] + bbox2[3]) / 2]
        
        distance = ((center1[0] - center2[0]) ** 2 + (center1[1] - center2[1]) ** 2) ** 0.5
        
        # 考虑标签匹配
        if det1['label'] != det2['label']:
            distance *= 2  # 标签不匹配时增加距离
        
        return distance
    
    def _interpolate_from_trajectory(self, trajectory: List[Dict], target_frame: int) -> Dict:
        """基于轨迹插值单个检测"""
        if len(trajectory) < 2:
            return None
        
        # 找到目标帧的前后帧
        prev_point = None
        next_point = None
        
        for point in trajectory:
            if point['frame_idx'] < target_frame:
                prev_point = point
            elif point['frame_idx'] > target_frame and next_point is None:
                next_point = point
                break
        
        if prev_point and next_point:
            # 线性插值
            prev_frame = prev_point['frame_idx']
            next_frame = next_point['frame_idx']
            prev_det = prev_point['detection']
            next_det = next_point['detection']
            
            weight = (target_frame - prev_frame) / (next_frame - prev_frame)
            
            prev_bbox = prev_det['bbox']
            next_bbox = next_det['bbox']
            
            interpolated_bbox = [
                int(prev_bbox[0] + (next_bbox[0] - prev_bbox[0]) * weight),
                int(prev_bbox[1] + (next_bbox[1] - prev_bbox[1]) * weight),
                int(prev_bbox[2] + (next_bbox[2] - prev_bbox[2]) * weight),
                int(prev_bbox[3] + (next_bbox[3] - prev_bbox[3]) * weight)
            ]
            
            return {
                "bbox": interpolated_bbox,
                "label": prev_det['label'],
                "frame_id": str(target_frame),
                "frame_idx": target_frame,
                "interpolated": True,
                "trajectory_based": True
            }
        
        elif prev_point:
            # 向前外推
            det = prev_point['detection']
            return {
                "bbox": det['bbox'].copy(),
                "label": det['label'],
                "frame_id": str(target_frame),
                "frame_idx": target_frame,
                "interpolated": True,
                "extrapolated": True
            }
        
        elif next_point:
            # 向后外推
            det = next_point['detection']
            return {
                "bbox": det['bbox'].copy(),
                "label": det['label'],
                "frame_id": str(target_frame),
                "frame_idx": target_frame,
                "interpolated": True,
                "extrapolated": True
            }
        
        return None
    
    def load_ground_truth(self, sequence_id: str, frame_indices: List[int]) -> Dict[int, List[Dict]]:
        """加载真实标注"""
        seq_info = self.sequences[sequence_id]
        labels_dir = Path(f"dataset/labels/{sequence_id}")
        ground_truth = {}
        
        for frame_idx in frame_indices:
            frame_id = str(frame_idx)
            label_file = labels_dir / f"{frame_id}.txt"
            
            annotations = []
            if label_file.exists():
                try:
                    with open(label_file, 'r') as f:
                        for line in f:
                            parts = line.strip().split()
                            if len(parts) >= 5:
                                class_id = int(parts[0])
                                x_center = float(parts[1])
                                y_center = float(parts[2])
                                width = float(parts[3])
                                height = float(parts[4])
                                
                                # 转换为绝对坐标
                                img_width, img_height = seq_info['size']
                                x1 = int((x_center - width/2) * img_width)
                                y1 = int((y_center - height/2) * img_height)
                                x2 = int((x_center + width/2) * img_width)
                                y2 = int((y_center + height/2) * img_height)
                                
                                class_name = self.class_map.get(str(class_id), "unknown")
                                
                                annotations.append({
                                    "bbox": [x1, y1, x2, y2],
                                    "label": class_name,
                                    "frame_id": frame_id,
                                    "frame_idx": frame_idx
                                })
                except Exception as e:
                    logger.warning(f"加载标注文件失败 {label_file}: {e}")
            
            ground_truth[frame_idx] = annotations
        
        return ground_truth
    
    def calculate_metrics(self, predictions: Dict[int, List[Dict]], ground_truth: Dict[int, List[Dict]], iou_threshold: float = 0.5) -> Dict:
        """计算检测指标"""
        def calculate_iou(box1, box2):
            x1 = max(box1[0], box2[0])
            y1 = max(box1[1], box2[1])
            x2 = min(box1[2], box2[2])
            y2 = min(box1[3], box2[3])
            
            if x2 <= x1 or y2 <= y1:
                return 0.0
            
            intersection = (x2 - x1) * (y2 - y1)
            area1 = (box1[2] - box1[0]) * (box1[3] - box1[1])
            area2 = (box2[2] - box2[0]) * (box2[3] - box2[1])
            union = area1 + area2 - intersection
            
            return intersection / union if union > 0 else 0.0
        
        total_tp = 0
        total_fp = 0
        total_fn = 0
        
        for frame_idx in ground_truth:
            gt_boxes = ground_truth[frame_idx]
            pred_boxes = predictions.get(frame_idx, [])
            
            matched_gt = set()
            
            for pred in pred_boxes:
                if not isinstance(pred, dict) or 'bbox' not in pred:
                    continue
                    
                best_iou = 0
                best_gt_idx = -1
                
                for gt_idx, gt in enumerate(gt_boxes):
                    if gt_idx in matched_gt:
                        continue
                    
                    if not isinstance(gt, dict) or 'bbox' not in gt:
                        continue
                    
                    iou = calculate_iou(pred['bbox'], gt['bbox'])
                    if iou > best_iou and iou >= iou_threshold:
                        best_iou = iou
                        best_gt_idx = gt_idx
                
                if best_gt_idx >= 0:
                    total_tp += 1
                    matched_gt.add(best_gt_idx)
                else:
                    total_fp += 1
            
            total_fn += len(gt_boxes) - len(matched_gt)
        
        precision = total_tp / (total_tp + total_fp) if (total_tp + total_fp) > 0 else 0
        recall = total_tp / (total_tp + total_fn) if (total_tp + total_fn) > 0 else 0
        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
        
        return {
            "precision": precision,
            "recall": recall,
            "f1": f1,
            "tp": total_tp,
            "fp": total_fp,
            "fn": total_fn
        }
    
    def detect_sequence_with_supercharged_method(self, sequence_id: str, test_ratio: float = 0.05) -> Dict:
        """使用超强方法检测序列：多策略检测+高级插值"""
        logger.info(f"开始超强检测序列 {sequence_id}...")
        
        seq_info = self.sequences[sequence_id]
        seq_dir = Path(f"dataset/images/{sequence_id}")
        
        if not seq_dir.exists():
            logger.error(f"序列目录不存在: {seq_dir}")
            return {}
        
        image_files = self.get_image_files(seq_dir, seq_info)
        if not image_files:
            logger.error(f"序列 {sequence_id} 中没有找到图像文件")
            return {}
        
        # 计算测试帧数（前5%）
        total_frames = len(image_files)
        test_frames = max(int(total_frames * test_ratio), 50)
        test_frames = min(test_frames, total_frames)
        test_image_files = image_files[:test_frames]
        
        logger.info(f"序列 {sequence_id}: 总帧数 {total_frames}, 测试帧数 {test_frames}")
        
        # 每隔10帧抽取进行检测 - 严格按照你的要求
        frame_interval = 10
        key_frame_indices = list(range(0, test_frames, frame_interval))
        all_frame_indices = list(range(test_frames))
        
        logger.info(f"关键帧数: {len(key_frame_indices)} (每隔{frame_interval}帧)")
        logger.info(f"总帧数: {len(all_frame_indices)} (需要插值)")
        
        # 多策略检测关键帧
        logger.info(f"开始超强多策略检测 ({len(key_frame_indices)} 关键帧)...")
        key_frame_detections = {}
        
        for frame_idx in key_frame_indices:
            frame_file = test_image_files[frame_idx]
            frame_id = str(frame_idx)
            
            detections = self.multi_strategy_detection(str(frame_file), frame_id, sequence_id)
            key_frame_detections[frame_idx] = detections
        
        # 高级插值补全所有帧
        logger.info("进行高级轨迹插值补全...")
        all_detections = self.advanced_interpolation(key_frame_detections, all_frame_indices)
        
        # 加载真实标注
        logger.info("加载真实标注...")
        ground_truth = self.load_ground_truth(sequence_id, all_frame_indices)
        
        # 计算指标
        logger.info("计算检测指标...")
        metrics = self.calculate_metrics(all_detections, ground_truth)
        
        # 统计结果
        total_detections = sum(len(dets) for dets in all_detections.values())
        direct_detections = sum(len(dets) for frame_idx, dets in all_detections.items() 
                               if frame_idx in key_frame_detections)
        interpolated_detections = total_detections - direct_detections
        
        result = {
            "sequence_id": sequence_id,
            "total_frames": test_frames,
            "key_frames": len(key_frame_indices),
            "total_detections": total_detections,
            "direct_detections": direct_detections,
            "interpolated_detections": interpolated_detections,
            "metrics": metrics,
            "detections": all_detections
        }
        
        logger.info(f"序列 {sequence_id} 超强检测完成:")
        logger.info(f"  总检测数: {total_detections}")
        logger.info(f"  直接检测: {direct_detections}")
        logger.info(f"  插值检测: {interpolated_detections}")
        logger.info(f"  精确度: {metrics['precision']:.3f}")
        logger.info(f"  召回率: {metrics['recall']:.3f}")
        logger.info(f"  F1分数: {metrics['f1']:.3f}")
        
        return result

def main():
    """主函数"""
    print("=== 超强Qwen2.5-VL检测与指标计算系统 ===")
    print("🎯 目标序列: data01, data02, data04, data06, data07, data19, data23, data26")
    print("📊 检测策略: 多策略融合检测+高级轨迹插值")
    print("🔄 插值策略: 基于轨迹的高级插值算法")
    print("📈 指标计算: 使用真实labels计算指标")
    print("🤖 模型: 使用超强微调后的Qwen2.5-VL模型")
    print("🔧 技术: 超强JSON解析+多策略检测+轨迹插值")
    
    # 检查模型是否存在
    base_model_path = "qwen2.5-vl-7b"
    lora_model_path = "output/supercharged_qwen_finetuned"
    
    if not os.path.exists(lora_model_path):
        print(f"\n❌ 未找到超强微调后的模型: {lora_model_path}")
        print("请先运行超强训练")
        return False
    
    print(f"\n✅ 找到超强微调后的模型: {lora_model_path}")
    
    # 初始化检测器
    print("\n初始化超强Qwen2.5-VL检测器...")
    detector = SuperchargedQwenDetector(base_model_path, lora_model_path)
    
    # 开始检测
    print("\n开始超强Qwen2.5-VL检测与指标计算...")
    
    sequences = ['data01', 'data02', 'data04', 'data06', 'data07', 'data19', 'data23', 'data26']
    all_results = {}
    
    for sequence_id in sequences:
        print(f"\n{'='*60}")
        print(f"超强检测序列: {sequence_id}")
        print(f"{'='*60}")
        
        result = detector.detect_sequence_with_supercharged_method(sequence_id)
        all_results[sequence_id] = result
    
    # 保存完整结果
    os.makedirs("results", exist_ok=True)
    with open("results/supercharged_qwen_detection_results.json", 'w', encoding='utf-8') as f:
        json.dump(all_results, f, ensure_ascii=False, indent=2)
    
    print("\n🎉 超强Qwen2.5-VL检测完成！")
    print("📊 检测结果已保存到 results/supercharged_qwen_detection_results.json")
    
    # 统计总体结果
    print("\n📈 总体检测统计:")
    total_detections = 0
    total_direct = 0
    total_interpolated = 0
    overall_metrics = {"tp": 0, "fp": 0, "fn": 0}
    
    for seq_id, result in all_results.items():
        if result:
            total_detections += result['total_detections']
            total_direct += result['direct_detections']
            total_interpolated += result['interpolated_detections']
            
            metrics = result['metrics']
            overall_metrics['tp'] += metrics['tp']
            overall_metrics['fp'] += metrics['fp']
            overall_metrics['fn'] += metrics['fn']
            
            print(f"   {seq_id}: {result['total_detections']} 个检测 "
                  f"(直接: {result['direct_detections']}, 插值: {result['interpolated_detections']}) "
                  f"P: {metrics['precision']:.3f}, R: {metrics['recall']:.3f}, F1: {metrics['f1']:.3f}")
    
    # 计算总体指标
    overall_precision = overall_metrics['tp'] / (overall_metrics['tp'] + overall_metrics['fp']) if (overall_metrics['tp'] + overall_metrics['fp']) > 0 else 0
    overall_recall = overall_metrics['tp'] / (overall_metrics['tp'] + overall_metrics['fn']) if (overall_metrics['tp'] + overall_metrics['fn']) > 0 else 0
    overall_f1 = 2 * overall_precision * overall_recall / (overall_precision + overall_recall) if (overall_precision + overall_recall) > 0 else 0
    
    print(f"\n📊 总体统计:")
    print(f"   总检测数: {total_detections}")
    print(f"   直接检测: {total_direct}")
    print(f"   插值检测: {total_interpolated}")
    print(f"   总体精确度: {overall_precision:.3f}")
    print(f"   总体召回率: {overall_recall:.3f}")
    print(f"   总体F1分数: {overall_f1:.3f}")
    
    print("\n💡 这是使用超强微调模型的检测结果:")
    print("包括：超强JSON解析 + 多策略融合检测 + 高级轨迹插值 + 真实指标计算")
    print("🎯 最大化优化了你的技术方案效果")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
