#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
超强Qwen2.5-VL微调训练器 - 真正的大幅优化
最大化训练数据，最强LoRA配置，最优训练策略
"""

import os
import sys
import json
import torch
import logging
from pathlib import Path
from typing import List, Dict, Any
from PIL import Image
from transformers import (
    Qwen2_5_VLForConditionalGeneration,
    AutoProcessor,
    TrainingArguments,
    Trainer,
)
from peft import LoraConfig, get_peft_model, TaskType
import gc
import re
import random
import numpy as np

# 强制使用GPU卡2
os.environ['CUDA_VISIBLE_DEVICES'] = '2'
torch.cuda.set_device(0)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def clear_memory():
    """清理显存"""
    gc.collect()
    torch.cuda.empty_cache()

def get_image_files(seq_dir: Path, seq_info: Dict) -> List[Path]:
    """获取图像文件列表"""
    if seq_info['naming'] == 'simple':
        image_files = list(seq_dir.glob(f"*{seq_info['format']}"))
        return sorted(image_files, key=lambda x: int(x.stem))
    else:
        image_files = list(seq_dir.glob(f"*{seq_info['format']}"))
        def complex_sort_key(path):
            numbers = re.findall(r'(\d+)', path.name)
            return int(numbers[-1]) if numbers else 0
        return sorted(image_files, key=complex_sort_key)

def load_frame_annotation(seq_id, frame_id, size):
    """加载帧标注"""
    labels_dir = Path(f"dataset/labels/{seq_id}")
    label_file = labels_dir / f"{frame_id}.txt"
    
    if not label_file.exists():
        return []
    
    with open('dataset/class.json', 'r', encoding='utf-8') as f:
        class_map = json.load(f)
    
    annotations = []
    try:
        with open(label_file, 'r') as f:
            for line in f:
                parts = line.strip().split()
                if len(parts) >= 5:
                    class_id = int(parts[0])
                    x_center = float(parts[1])
                    y_center = float(parts[2])
                    width = float(parts[3])
                    height = float(parts[4])
                    
                    # 转换为绝对坐标
                    img_width, img_height = size
                    x1 = int((x_center - width/2) * img_width)
                    y1 = int((y_center - height/2) * img_height)
                    x2 = int((x_center + width/2) * img_width)
                    y2 = int((y_center + height/2) * img_height)
                    
                    class_name = class_map.get(str(class_id), "unknown")
                    
                    annotations.append({
                        "bbox": [x1, y1, x2, y2],
                        "label": class_name,
                        "frame_id": frame_id
                    })
    except Exception as e:
        logger.warning(f"加载标注文件失败 {label_file}: {e}")
    
    return annotations

def generate_supercharged_training_data():
    """生成超强训练数据 - 最大化数据量和质量"""
    logger.info("生成超强训练数据...")
    
    # 使用所有序列
    sequences = {
        'data01': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
        'data02': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
        'data04': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
        'data06': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
        'data07': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
        'data19': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
        'data23': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
        'data26': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'}
    }
    
    all_training_samples = []
    
    for seq_id, seq_info in sequences.items():
        print(f"处理序列 {seq_id}...")
        
        images_dir = Path(f"dataset/images/{seq_id}")
        image_files = get_image_files(images_dir, seq_info)
        
        # 策略1: 密集单帧样本 - 大幅增加样本数
        for i in range(0, min(len(image_files), 500), 8):  # 每8帧选一个，大幅增加
            frame_file = image_files[i]
            frame_id = frame_file.stem
            
            # 加载标注
            annotations = load_frame_annotation(seq_id, frame_id, seq_info['size'])
            
            if annotations:  # 只有有标注的帧才生成训练样本
                # 超强提示词 - 更详细更精确
                training_sample = {
                    "messages": [
                        {
                            "role": "user",
                            "content": [
                                {
                                    "type": "image",
                                    "image": str(frame_file)
                                },
                                {
                                    "type": "text",
                                    "text": f"""你是世界顶级的红外弱小目标检测专家。请精确检测这张红外图像中的所有弱小目标。

图像详细信息：
- 序列编号：{seq_id}
- 帧编号：{frame_id}
- 图像尺寸：{seq_info['size']}
- 图像类型：红外热成像

弱小目标详细特征：
- 尺寸极小：通常只有5-20像素
- 外观特征：在红外图像中呈现为亮点、暗点或小块状
- 形状特征：可能是点状、小矩形、小椭圆
- 对比度：与背景有明显的温度差异
- 目标类别：drone（无人机）, car（汽车）, ship（船舶）, bus（公交车）, pedestrian（行人）, cyclist（骑行者）

专业检测要求：
1. 仔细扫描整个图像，特别关注小亮点和异常区域
2. 边界框必须紧贴目标，精确到像素级别
3. 坐标必须是整数，格式为[x1,y1,x2,y2]
4. 如果没有发现任何目标，输出空数组[]
5. 确保检测的完整性和准确性

输出格式（严格遵守JSON格式）：
[{{"bbox":[x1,y1,x2,y2],"label":"目标类别"}}]

现在开始专业检测："""
                                }
                            ]
                        },
                        {
                            "role": "assistant",
                            "content": json.dumps(annotations, ensure_ascii=False)
                        }
                    ]
                }
                
                all_training_samples.append(training_sample)
                print(f"  ✅ 生成单帧样本: 帧{frame_id}, {len(annotations)}个标注")
        
        # 策略2: 多尺度序列样本 - 不同长度的序列
        total_frames = len(image_files)
        
        # 短序列（3帧）
        for start_idx in range(0, min(total_frames-30, 200), 25):
            frame_interval = 10
            sampled_indices = [start_idx, start_idx+frame_interval, start_idx+2*frame_interval]
            
            sequence_annotations = []
            sequence_frame_paths = []
            
            for idx in sampled_indices:
                if idx < len(image_files):
                    frame_file = image_files[idx]
                    frame_id = frame_file.stem
                    sequence_frame_paths.append(str(frame_file))
                    
                    annotations = load_frame_annotation(seq_id, frame_id, seq_info['size'])
                    for ann in annotations:
                        ann_copy = ann.copy()
                        ann_copy['frame_id'] = frame_id
                        sequence_annotations.append(ann_copy)
            
            if sequence_annotations and len(sequence_frame_paths) == 3:
                content = []
                for frame_path in sequence_frame_paths:
                    content.append({
                        "type": "image",
                        "image": frame_path
                    })
                
                content.append({
                    "type": "text",
                    "text": f"""你是世界顶级的红外弱小目标检测专家。请精确检测这个短视频序列中的所有弱小目标。

序列详细信息：
- 序列编号：{seq_id}
- 帧数：{len(sequence_frame_paths)}帧
- 抽取策略：每隔{frame_interval}帧抽取
- 图像尺寸：{seq_info['size']}
- 序列类型：红外热成像视频

弱小目标时序特征：
- 目标可能在帧间移动
- 保持相对稳定的外观特征
- 可能有轻微的尺寸变化
- 目标类别：drone, car, ship, bus, pedestrian, cyclist

专业检测要求：
1. 逐帧检测每一帧中的所有弱小目标
2. 考虑目标的时序连续性
3. 边界框要小而精确
4. 为每个检测结果标注对应的frame_id

输出格式（严格遵守JSON格式）：
[{{"bbox":[x1,y1,x2,y2],"label":"目标类别","frame_id":"帧号"}}]

现在开始专业序列检测："""
                })
                
                sequence_sample = {
                    "messages": [
                        {
                            "role": "user",
                            "content": content
                        },
                        {
                            "role": "assistant",
                            "content": json.dumps(sequence_annotations, ensure_ascii=False)
                        }
                    ]
                }
                
                all_training_samples.append(sequence_sample)
                print(f"  ✅ 生成短序列样本: {len(sequence_frame_paths)}帧, {len(sequence_annotations)}个标注")
        
        # 策略3: 长序列样本（5帧）- 按照你的要求
        for start_idx in range(0, min(total_frames-50, 150), 40):
            frame_interval = 10
            sampled_indices = [start_idx + i*frame_interval for i in range(5)]
            
            sequence_annotations = []
            sequence_frame_paths = []
            
            for idx in sampled_indices:
                if idx < len(image_files):
                    frame_file = image_files[idx]
                    frame_id = frame_file.stem
                    sequence_frame_paths.append(str(frame_file))
                    
                    annotations = load_frame_annotation(seq_id, frame_id, seq_info['size'])
                    for ann in annotations:
                        ann_copy = ann.copy()
                        ann_copy['frame_id'] = frame_id
                        sequence_annotations.append(ann_copy)
            
            if sequence_annotations and len(sequence_frame_paths) == 5:
                content = []
                for frame_path in sequence_frame_paths:
                    content.append({
                        "type": "image",
                        "image": frame_path
                    })
                
                content.append({
                    "type": "text",
                    "text": f"""你是世界顶级的红外弱小目标检测专家。请精确检测这个长视频序列中的所有弱小目标。

序列详细信息：
- 序列编号：{seq_id}
- 帧数：{len(sequence_frame_paths)}帧
- 抽取策略：每隔{frame_interval}帧抽取（严格按照用户要求）
- 图像尺寸：{seq_info['size']}
- 序列类型：红外热成像视频

弱小目标长序列特征：
- 目标轨迹可能跨越多帧
- 保持目标身份的一致性
- 可能有遮挡和重现
- 运动模式分析
- 目标类别：drone, car, ship, bus, pedestrian, cyclist

专业检测要求：
1. 逐帧检测每一帧中的所有弱小目标
2. 分析目标的运动轨迹和时序关系
3. 边界框要小而精确
4. 为每个检测结果标注对应的frame_id
5. 确保检测的时序一致性

输出格式（严格遵守JSON格式）：
[{{"bbox":[x1,y1,x2,y2],"label":"目标类别","frame_id":"帧号"}}]

现在开始专业长序列检测："""
                })
                
                sequence_sample = {
                    "messages": [
                        {
                            "role": "user",
                            "content": content
                        },
                        {
                            "role": "assistant",
                            "content": json.dumps(sequence_annotations, ensure_ascii=False)
                        }
                    ]
                }
                
                all_training_samples.append(sequence_sample)
                print(f"  ✅ 生成长序列样本: {len(sequence_frame_paths)}帧, {len(sequence_annotations)}个标注")
    
    # 数据增强：随机打乱样本顺序
    random.shuffle(all_training_samples)
    
    # 保存超强训练数据
    output_path = "data/supercharged_training_data.json"
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(all_training_samples, f, ensure_ascii=False, indent=2)
    
    logger.info(f"超强训练数据保存到: {output_path}")
    logger.info(f"总训练样本数: {len(all_training_samples)}")
    
    return True

class SuperchargedDataset:
    """超强数据集"""
    
    def __init__(self, data_path: str, processor):
        self.processor = processor
        with open(data_path, 'r', encoding='utf-8') as f:
            self.data = json.load(f)
        logger.info(f"加载了 {len(self.data)} 个超强训练样本")
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        messages = self.data[idx]
        
        try:
            # 应用聊天模板
            text = self.processor.apply_chat_template(
                messages, tokenize=False, add_generation_prompt=True
            )
            
            # 处理图像 - 最高质量
            images = []
            for content in messages[0]["content"]:
                if content["type"] == "image":
                    img = Image.open(content["image"]).convert('RGB')
                    # 保持最高分辨率
                    img.thumbnail((512, 512), Image.Resampling.LANCZOS)
                    images.append(img)
            
            if not images:
                return None
            
            # 处理输入
            inputs = self.processor(
                text=[text],
                images=images,
                padding=True,
                return_tensors="pt"
            )
            
            inputs["labels"] = inputs["input_ids"].clone()
            return inputs
            
        except Exception as e:
            logger.warning(f"处理样本时出错: {e}")
            return None

def supercharged_collate_fn(batch):
    """超强数据整理函数"""
    batch = [item for item in batch if item is not None]
    if not batch:
        return None
    return batch[0]

class SuperchargedTrainer:
    """超强训练器"""
    
    def __init__(self, model_path: str):
        self.model_path = model_path
        self.device = "cuda"
        
        clear_memory()
        
        logger.info("加载超强模型...")
        
        self.processor = AutoProcessor.from_pretrained(model_path)
        self.model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
            model_path,
            torch_dtype=torch.float16,
            device_map="auto",
            trust_remote_code=True,
            low_cpu_mem_usage=True,
            use_cache=False,
        )
        
        logger.info("超强模型加载完成")
    
    def setup_lora(self):
        """设置超强LoRA - 最大化表达能力"""
        config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            r=32,  # 大幅增加rank
            lora_alpha=64,  # 大幅增加alpha
            target_modules=["q_proj", "v_proj", "k_proj", "o_proj", "gate_proj", "up_proj", "down_proj"],  # 最多目标模块
            lora_dropout=0.05,  # 降低dropout
        )
        
        self.model = get_peft_model(self.model, config)
        self.model.print_trainable_parameters()
        logger.info("超强LoRA配置完成")
    
    def supercharged_train(self, train_data_path: str, output_dir: str):
        """超强训练"""
        logger.info("开始超强微调...")
        
        clear_memory()
        
        # 设置LoRA
        self.setup_lora()
        
        # 创建数据集
        dataset = SuperchargedDataset(train_data_path, self.processor)
        
        # 超强训练参数 - 使用简单训练循环避免显存问题
        # 手动训练循环
        optimizer = torch.optim.AdamW(
            self.model.parameters(),
            lr=5e-5,
            weight_decay=0.01,
            betas=(0.9, 0.999),
            eps=1e-8
        )

        scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
            optimizer,
            T_max=len(dataset)*12
        )

        # 手动训练循环
        self.model.train()

        for epoch in range(12):  # 12个训练轮数
            logger.info(f"超强训练轮次 {epoch + 1}/12")

            epoch_loss = 0
            for i, sample in enumerate(dataset):
                if sample is None:
                    continue

                try:
                    # 移动到设备
                    inputs = {k: v.to(self.device) for k, v in sample.items()}

                    # 前向传播
                    optimizer.zero_grad()
                    outputs = self.model(**inputs)
                    loss = outputs.loss

                    # 反向传播
                    loss.backward()
                    torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                    optimizer.step()
                    scheduler.step()

                    epoch_loss += loss.item()

                    if (i + 1) % 5 == 0:
                        logger.info(f"  样本 {i+1}/{len(dataset)}, 损失: {loss.item():.4f}, LR: {scheduler.get_last_lr()[0]:.6f}")

                    clear_memory()

                except Exception as e:
                    logger.warning(f"处理样本 {i} 时出错: {e}")
                    continue

            avg_loss = epoch_loss / len(dataset)
            logger.info(f"轮次 {epoch + 1} 平均损失: {avg_loss:.4f}")

        return True
        
        # 保存模型
        os.makedirs(output_dir, exist_ok=True)
        self.model.save_pretrained(output_dir)
        self.processor.save_pretrained(output_dir)
        
        logger.info(f"超强微调完成，模型保存到: {output_dir}")
        return True

def main():
    """主函数"""
    print("=== 超强Qwen2.5-VL微调训练系统 ===")
    print("🎯 目标: 最大化检测效果")
    print("📊 策略: 最大训练数据+最强LoRA+最优训练")
    print("🎬 训练方式: 超强LoRA微调")
    print("🔧 技术: 最大LoRA + 最高质量数据 + 最优训练策略")
    print("⏱️  预计时间: 45-60分钟")
    
    # 第1步：生成训练数据
    print("\n第1步：生成超强训练数据...")
    if not generate_supercharged_training_data():
        print("❌ 训练数据生成失败")
        return False
    
    # 第2步：初始化训练器
    print("\n第2步：初始化超强训练器...")
    model_path = "qwen2.5-vl-7b"
    trainer = SuperchargedTrainer(model_path)
    
    # 第3步：开始训练
    print("\n第3步：开始超强微调...")
    output_dir = "output/supercharged_qwen_finetuned"
    success = trainer.supercharged_train("data/supercharged_training_data.json", output_dir)
    
    if success:
        print("\n🎉 超强微调完成！")
        print(f"📁 模型保存到: {output_dir}")
        print("📋 这是最强优化版本")
        print("💡 应该有最佳的检测效果")
    else:
        print("\n❌ 超强微调失败！")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
