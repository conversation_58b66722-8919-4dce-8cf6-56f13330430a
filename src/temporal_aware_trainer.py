#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时序感知微调训练器
真正学习时序信息的微调方案
"""

import os
import sys
import json
import random
import logging
from pathlib import Path
from typing import List, Dict, Any, Tuple
from PIL import Image, ImageEnhance
import torch
from transformers import (
    Qwen2_5_VLForConditionalGeneration, 
    AutoProcessor,
    TrainingArguments,
    Trainer
)
from peft import LoraConfig, get_peft_model, TaskType
from torch.utils.data import Dataset
import re

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TemporalAwareTrainer:
    """时序感知训练器"""
    
    def __init__(self):
        self.sequences = {
            'data01': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data02': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data04': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data06': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data07': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data19': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
            'data23': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
            'data26': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'}
        }
        
        # 加载类别映射
        with open('dataset/class.json', 'r', encoding='utf-8') as f:
            self.class_map = json.load(f)
    
    def get_image_files(self, seq_dir: Path, seq_info: Dict) -> List[Path]:
        """获取图像文件列表"""
        if seq_info['naming'] == 'simple':
            image_files = list(seq_dir.glob(f"*{seq_info['format']}"))
            return sorted(image_files, key=lambda x: int(x.stem))
        else:
            image_files = list(seq_dir.glob(f"*{seq_info['format']}"))
            def complex_sort_key(path):
                numbers = re.findall(r'(\d+)', path.name)
                return int(numbers[-1]) if numbers else 0
            return sorted(image_files, key=complex_sort_key)
    
    def load_sequence_temporal_data(self, seq_id: str, temporal_length: int = 5) -> List[Dict]:
        """加载序列的时序数据"""
        seq_info = self.sequences[seq_id]
        seq_dir = Path(f"dataset/images/{seq_id}")
        labels_dir = Path(f"dataset/labels/{seq_id}")
        
        if not seq_dir.exists() or not labels_dir.exists():
            logger.warning(f"序列 {seq_id} 目录不存在")
            return []
        
        image_files = self.get_image_files(seq_dir, seq_info)
        temporal_samples = []
        
        # 滑动窗口创建时序样本
        for i in range(len(image_files) - temporal_length + 1):
            window_files = image_files[i:i + temporal_length]
            
            # 检查窗口中的标注
            window_annotations = []
            valid_window = True
            
            for j, img_file in enumerate(window_files):
                # 根据命名规则确定标注文件名
                if seq_info['naming'] == 'simple':
                    label_file = labels_dir / f"{img_file.stem}.txt"
                else:
                    label_file = labels_dir / f"{img_file.stem}.txt"
                
                frame_annotations = []
                if label_file.exists():
                    try:
                        with open(label_file, 'r') as f:
                            lines = f.readlines()
                        
                        for line in lines:
                            parts = line.strip().split()
                            if len(parts) >= 5:
                                class_id = int(parts[0])
                                x_center = float(parts[1])
                                y_center = float(parts[2])
                                width = float(parts[3])
                                height = float(parts[4])
                                
                                # 转换为绝对坐标
                                img_width, img_height = seq_info['size']
                                x1 = int((x_center - width/2) * img_width)
                                y1 = int((y_center - height/2) * img_height)
                                x2 = int((x_center + width/2) * img_width)
                                y2 = int((y_center + height/2) * img_height)
                                
                                # 验证弱小目标尺寸
                                bbox_width = x2 - x1
                                bbox_height = y2 - y1
                                
                                if (bbox_width >= 3 and bbox_height >= 3 and 
                                    bbox_width <= 80 and bbox_height <= 80):
                                    
                                    class_name = self.class_map.get(str(class_id), "drone")
                                    frame_annotations.append({
                                        "bbox": [x1, y1, x2, y2],
                                        "label": class_name,
                                        "frame_idx": j
                                    })
                    
                    except Exception as e:
                        logger.warning(f"加载标注失败 {label_file}: {e}")
                
                window_annotations.append({
                    "frame_idx": j,
                    "image_path": str(img_file),
                    "annotations": frame_annotations
                })
            
            # 只保留至少有一帧有标注的窗口
            has_annotations = any(len(frame['annotations']) > 0 for frame in window_annotations)
            
            if has_annotations:
                temporal_samples.append({
                    "sequence_id": seq_id,
                    "start_frame": i,
                    "temporal_window": window_annotations
                })
        
        logger.info(f"序列 {seq_id}: 创建了 {len(temporal_samples)} 个时序样本")
        return temporal_samples
    
    def split_temporal_data(self, test_ratio: float = 0.2) -> Tuple[List[Dict], List[Dict]]:
        """按序列划分时序数据"""
        logger.info("开始划分时序训练数据...")
        
        all_sequences = list(self.sequences.keys())
        random.shuffle(all_sequences)
        
        # 按序列划分
        num_test_sequences = max(1, int(len(all_sequences) * test_ratio))
        test_sequences = all_sequences[:num_test_sequences]
        train_sequences = all_sequences[num_test_sequences:]
        
        logger.info(f"训练序列: {train_sequences}")
        logger.info(f"测试序列: {test_sequences}")
        
        # 加载时序训练数据
        train_data = []
        for seq_id in train_sequences:
            seq_temporal_data = self.load_sequence_temporal_data(seq_id, temporal_length=5)
            train_data.extend(seq_temporal_data)
        
        # 加载时序测试数据
        test_data = []
        for seq_id in test_sequences:
            seq_temporal_data = self.load_sequence_temporal_data(seq_id, temporal_length=5)
            test_data.extend(seq_temporal_data)
        
        logger.info(f"时序训练数据: {len(train_data)} 个样本")
        logger.info(f"时序测试数据: {len(test_data)} 个样本")
        
        return train_data, test_data
    
    def create_temporal_training_samples(self, data: List[Dict]) -> List[Dict]:
        """创建时序训练样本"""
        samples = []
        
        for item in data:
            seq_id = item["sequence_id"]
            temporal_window = item["temporal_window"]
            seq_info = self.sequences[seq_id]
            
            # 构建时序提示词
            temporal_prompt = f"""分析这个时序图像序列中弱小目标的运动轨迹。

序列信息：
- 序列ID: {seq_id}
- 图像尺寸: {seq_info['size']}
- 帧数: {len(temporal_window)}
- 目标特征: 3-80像素的弱小目标

任务要求：
1. 检测每帧中的弱小目标
2. 跟踪目标在时序中的运动
3. 预测目标的运动轨迹

输出格式：
{{
  "temporal_detections": [
    {{"frame_idx": 0, "detections": [{{"bbox":[x1,y1,x2,y2],"label":"类别"}}]}},
    {{"frame_idx": 1, "detections": [{{"bbox":[x1,y1,x2,y2],"label":"类别"}}]}},
    ...
  ],
  "trajectories": [
    {{"track_id": 1, "frames": [0,1,2,3,4], "label": "drone"}}
  ]
}}

开始分析："""
            
            # 构建真实标注
            temporal_detections = []
            trajectories = []
            track_id = 1
            
            for frame_data in temporal_window:
                frame_idx = frame_data["frame_idx"]
                annotations = frame_data["annotations"]
                
                frame_detections = []
                for ann in annotations:
                    frame_detections.append({
                        "bbox": ann["bbox"],
                        "label": ann["label"]
                    })
                
                temporal_detections.append({
                    "frame_idx": frame_idx,
                    "detections": frame_detections
                })
                
                # 简单的轨迹构建（实际应该用更复杂的跟踪算法）
                for ann in annotations:
                    trajectories.append({
                        "track_id": track_id,
                        "frames": [frame_idx],
                        "label": ann["label"]
                    })
                    track_id += 1
            
            ground_truth = {
                "temporal_detections": temporal_detections,
                "trajectories": trajectories
            }
            
            ground_truth_json = json.dumps(ground_truth, ensure_ascii=False)
            
            # 构建多图像输入
            content = []
            for frame_data in temporal_window:
                content.append({
                    "type": "image",
                    "image": frame_data["image_path"]
                })
            
            content.append({
                "type": "text",
                "text": temporal_prompt
            })
            
            sample = {
                "messages": [
                    {
                        "role": "user",
                        "content": content
                    },
                    {
                        "role": "assistant",
                        "content": ground_truth_json
                    }
                ]
            }
            samples.append(sample)
        
        return samples
    
    def prepare_temporal_training_data(self):
        """准备时序训练数据"""
        logger.info("开始准备时序感知训练数据...")
        
        # 设置随机种子
        random.seed(42)
        
        # 划分时序数据
        train_data, test_data = self.split_temporal_data(test_ratio=0.25)
        
        # 创建时序训练样本
        train_samples = self.create_temporal_training_samples(train_data)
        test_samples = self.create_temporal_training_samples(test_data)
        
        logger.info(f"生成时序训练样本: {len(train_samples)} 个")
        logger.info(f"生成时序测试样本: {len(test_samples)} 个")
        
        # 保存时序训练数据
        os.makedirs("data", exist_ok=True)
        
        with open("data/temporal_train_data.json", 'w', encoding='utf-8') as f:
            json.dump(train_samples, f, ensure_ascii=False, indent=2)
        
        with open("data/temporal_test_data.json", 'w', encoding='utf-8') as f:
            json.dump(test_samples, f, ensure_ascii=False, indent=2)
        
        logger.info("时序感知训练数据已保存")
        logger.info(f"时序训练数据: data/temporal_train_data.json ({len(train_samples)} 样本)")
        logger.info(f"时序测试数据: data/temporal_test_data.json ({len(test_samples)} 样本)")
        
        return train_samples, test_samples

class TemporalVLDataset(Dataset):
    """时序视觉语言数据集"""
    
    def __init__(self, data: List[Dict], processor, is_training: bool = True):
        self.data = data
        self.processor = processor
        self.is_training = is_training
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        item = self.data[idx]
        messages = item["messages"]
        
        # 应用聊天模板
        text = self.processor.apply_chat_template(
            messages, tokenize=False, add_generation_prompt=False
        )
        
        # 处理多个图像
        images = []
        for content in messages[0]["content"]:
            if content["type"] == "image":
                try:
                    image = Image.open(content["image"]).convert('RGB')
                    
                    # 训练时进行数据增强
                    if self.is_training and random.random() > 0.7:
                        if random.random() > 0.5:
                            enhancer = ImageEnhance.Contrast(image)
                            image = enhancer.enhance(random.uniform(1.1, 1.4))
                        
                        if random.random() > 0.5:
                            enhancer = ImageEnhance.Sharpness(image)
                            image = enhancer.enhance(random.uniform(1.1, 1.3))
                    
                    # 调整图像尺寸
                    image.thumbnail((448, 448), Image.Resampling.LANCZOS)
                    images.append(image)
                    
                except Exception as e:
                    logger.error(f"图像加载失败 {content['image']}: {e}")
                    # 创建空白图像作为fallback
                    images.append(Image.new('RGB', (448, 448), color='black'))
        
        # 如果没有图像，创建一个空白图像
        if not images:
            images = [Image.new('RGB', (448, 448), color='black')]
        
        # 处理输入（使用第一张图像，后续可以扩展为多图像处理）
        inputs = self.processor(
            text=[text],
            images=[images[0]],  # 目前只使用第一张图像
            padding=True,
            return_tensors="pt"
        )
        
        # 移除batch维度
        for key in inputs:
            inputs[key] = inputs[key].squeeze(0)
        
        # 设置labels用于计算loss
        inputs["labels"] = inputs["input_ids"].clone()
        
        return inputs

def main():
    """主函数"""
    print("=== 时序感知弱小目标微调训练系统 ===")
    print("🎯 特点: 真正学习时序信息")
    print("📊 策略: 多帧时序窗口训练")
    print("🔧 技术: 轨迹跟踪 + 运动预测")
    
    # 创建时序训练器
    trainer = TemporalAwareTrainer()
    
    # 准备时序训练数据
    train_samples, test_samples = trainer.prepare_temporal_training_data()
    
    print(f"\n✅ 时序数据准备完成:")
    print(f"   时序训练样本: {len(train_samples)}")
    print(f"   时序测试样本: {len(test_samples)}")
    print(f"   每个样本包含: 5帧连续图像")
    print(f"   学习内容: 目标检测 + 轨迹跟踪")
    
    print("\n💡 下一步: 运行时序LoRA训练器进行真正的时序微调")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
