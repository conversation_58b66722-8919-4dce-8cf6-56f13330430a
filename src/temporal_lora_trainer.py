#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时序LoRA微调训练器
真正学习时序信息的LoRA微调
"""

import os
import sys
import json
import torch
import logging
from pathlib import Path
from typing import List, Dict
from PIL import Image, ImageEnhance
import random

from transformers import (
    Qwen2_5_VLForConditionalGeneration,
    AutoProcessor,
    TrainingArguments,
    Trainer,
)
from peft import LoraConfig, get_peft_model, TaskType
from torch.utils.data import Dataset

# 添加当前目录到路径
sys.path.append('src')
from temporal_aware_trainer import TemporalVLDataset

# 固定使用GPU卡2
os.environ['CUDA_VISIBLE_DEVICES'] = '2'

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TemporalLoRATrainer:
    """时序LoRA微调训练器"""
    
    def __init__(self, base_model_path: str):
        self.base_model_path = base_model_path
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        
        logger.info(f"使用设备: {self.device}")
        logger.info(f"基础模型: {base_model_path}")
    
    def load_model_and_processor(self):
        """加载模型和处理器"""
        logger.info("加载基础模型和处理器...")
        
        # 加载处理器
        self.processor = AutoProcessor.from_pretrained(self.base_model_path)
        
        # 加载基础模型
        self.model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
            self.base_model_path,
            torch_dtype=torch.float16,
            device_map="auto",
            trust_remote_code=True,
            low_cpu_mem_usage=True
        )
        
        logger.info("基础模型加载完成")
    
    def setup_temporal_lora_config(self):
        """设置时序LoRA配置"""
        logger.info("设置时序LoRA配置...")
        
        # 针对时序学习的LoRA配置
        lora_config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            inference_mode=False,
            r=32,  # 增加rank以学习复杂的时序关系
            lora_alpha=32,  # 平衡的alpha值
            lora_dropout=0.05,  # 较低的dropout保持时序连续性
            target_modules=[
                "q_proj", "k_proj", "v_proj", "o_proj",
                "gate_proj", "up_proj", "down_proj"
            ],
            bias="none",
        )
        
        # 应用LoRA
        self.model = get_peft_model(self.model, lora_config)
        
        # 打印可训练参数
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        total_params = sum(p.numel() for p in self.model.parameters())
        
        logger.info(f"可训练参数: {trainable_params:,}")
        logger.info(f"总参数: {total_params:,}")
        logger.info(f"可训练比例: {100 * trainable_params / total_params:.2f}%")
    
    def load_temporal_training_data(self):
        """加载时序训练数据"""
        logger.info("加载时序训练数据...")
        
        # 检查数据文件是否存在
        train_file = "data/temporal_train_data.json"
        test_file = "data/temporal_test_data.json"
        
        if not os.path.exists(train_file) or not os.path.exists(test_file):
            logger.error("时序训练数据不存在，请先运行 temporal_aware_trainer.py")
            return None, None
        
        # 加载时序训练数据
        with open(train_file, 'r', encoding='utf-8') as f:
            train_data = json.load(f)
        
        with open(test_file, 'r', encoding='utf-8') as f:
            test_data = json.load(f)
        
        logger.info(f"时序训练数据: {len(train_data)} 样本")
        logger.info(f"时序测试数据: {len(test_data)} 样本")
        
        return train_data, test_data
    
    def create_temporal_datasets(self, train_data: List[Dict], test_data: List[Dict]):
        """创建时序数据集"""
        logger.info("创建时序数据集...")
        
        train_dataset = TemporalVLDataset(train_data, self.processor, is_training=True)
        test_dataset = TemporalVLDataset(test_data, self.processor, is_training=False)
        
        logger.info(f"时序训练数据集: {len(train_dataset)} 样本")
        logger.info(f"时序测试数据集: {len(test_dataset)} 样本")
        
        return train_dataset, test_dataset
    
    def setup_temporal_training_arguments(self, output_dir: str):
        """设置时序训练参数"""
        logger.info("设置时序训练参数...")
        
        training_args = TrainingArguments(
            output_dir=output_dir,
            num_train_epochs=4,  # 时序学习需要更多epoch
            per_device_train_batch_size=1,  # 时序数据较复杂，使用小batch
            per_device_eval_batch_size=1,
            gradient_accumulation_steps=8,  # 增加梯度累积
            learning_rate=5e-5,  # 稍高的学习率学习时序关系
            weight_decay=0.01,
            logging_steps=20,
            eval_steps=100,
            save_steps=200,
            eval_strategy="steps",
            save_strategy="steps",
            load_best_model_at_end=True,
            metric_for_best_model="eval_loss",
            greater_is_better=False,
            warmup_steps=100,
            lr_scheduler_type="cosine",
            fp16=True,
            dataloader_pin_memory=False,
            remove_unused_columns=False,
            report_to=None,
            save_total_limit=3,
        )
        
        return training_args
    
    def train_temporal_model(self, output_dir: str = "output/temporal_weak_target_lora", resume_from_checkpoint: str = None):
        """训练时序模型"""
        logger.info("开始时序感知LoRA微调训练...")
        
        # 加载模型和处理器
        self.load_model_and_processor()
        
        # 设置时序LoRA
        self.setup_temporal_lora_config()
        
        # 加载时序数据
        train_data, test_data = self.load_temporal_training_data()
        if train_data is None:
            return False
        
        # 创建时序数据集
        train_dataset, test_dataset = self.create_temporal_datasets(train_data, test_data)
        
        # 设置训练参数
        training_args = self.setup_temporal_training_arguments(output_dir)
        
        # 创建训练器
        trainer = Trainer(
            model=self.model,
            args=training_args,
            train_dataset=train_dataset,
            eval_dataset=test_dataset,
            tokenizer=self.processor.tokenizer,
        )
        
        # 开始训练（支持从checkpoint继续）
        if resume_from_checkpoint:
            logger.info(f"从checkpoint继续时序训练: {resume_from_checkpoint}")
            trainer.train(resume_from_checkpoint=resume_from_checkpoint)
        else:
            logger.info("开始新的时序训练...")
            trainer.train()
        
        # 保存模型
        logger.info("保存时序模型...")
        trainer.save_model()
        
        # 保存处理器
        self.processor.save_pretrained(output_dir)
        
        logger.info(f"时序模型已保存到: {output_dir}")
        
        return True

def main():
    """主函数"""
    print("=== 时序感知LoRA微调训练系统 ===")
    print("🎯 特点: 真正学习时序信息")
    print("📊 策略: 多帧时序窗口训练")
    print("🔧 技术: 轨迹跟踪 + 运动预测")
    
    # 检查时序训练数据
    train_file = "data/temporal_train_data.json"
    test_file = "data/temporal_test_data.json"
    
    if not os.path.exists(train_file) or not os.path.exists(test_file):
        print("\n❌ 时序训练数据不存在")
        print("请先运行: python src/temporal_aware_trainer.py")
        return False
    
    print(f"\n✅ 找到时序训练数据:")
    print(f"   时序训练数据: {train_file}")
    print(f"   时序测试数据: {test_file}")
    
    # 检查是否有checkpoint可以继续
    output_dir = "output/temporal_weak_target_lora"
    checkpoint_dir = None
    
    if os.path.exists(output_dir):
        checkpoints = [d for d in os.listdir(output_dir) if d.startswith('checkpoint-')]
        if checkpoints:
            # 找到最新的checkpoint
            latest_checkpoint = max(checkpoints, key=lambda x: int(x.split('-')[1]))
            checkpoint_dir = os.path.join(output_dir, latest_checkpoint)
            print(f"\n🔄 找到时序checkpoint: {checkpoint_dir}")
            print(f"   将从此处继续时序训练")
        else:
            print(f"\n🆕 开始新的时序训练")
    else:
        print(f"\n🆕 开始新的时序训练")
    
    # 创建时序训练器
    base_model_path = "qwen2.5-vl-7b"
    trainer = TemporalLoRATrainer(base_model_path)
    
    # 开始时序训练
    success = trainer.train_temporal_model(output_dir, resume_from_checkpoint=checkpoint_dir)
    
    if success:
        print(f"\n🎉 时序感知LoRA微调训练完成！")
        print(f"📁 时序模型保存到: {output_dir}")
        print(f"💡 这个模型真正学习了时序信息和目标轨迹")
        print(f"🎯 可以进行基于时序的弱小目标检测和跟踪")
    else:
        print(f"\n❌ 时序训练失败！")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
