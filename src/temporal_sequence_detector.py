#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时序序列Qwen2.5-VL检测器
基于时序序列训练的模型进行检测
"""

import os
import sys
import json
import torch
import logging
from pathlib import Path
from typing import List, Dict, Any
from PIL import Image
from transformers import Qwen2_5_VLForConditionalGeneration, AutoProcessor
from peft import PeftModel
import re
import numpy as np

# 固定使用GPU卡2
os.environ['CUDA_VISIBLE_DEVICES'] = '2'

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TemporalSequenceQwenDetector:
    """时序序列Qwen2.5-VL检测器"""
    
    def __init__(self, base_model_path: str, lora_model_path: str):
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        
        logger.info(f"使用设备: {self.device}")
        logger.info(f"基础模型: {base_model_path}")
        logger.info(f"LoRA模型: {lora_model_path}")
        
        # 加载基础模型
        self.processor = AutoProcessor.from_pretrained(base_model_path)
        self.model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
            base_model_path,
            torch_dtype=torch.float16,
            device_map="auto",
            trust_remote_code=True,
            low_cpu_mem_usage=True
        )
        
        # 加载LoRA微调模型
        logger.info("加载LoRA微调模型...")
        self.model = PeftModel.from_pretrained(self.model, lora_model_path)
        logger.info("时序序列Qwen2.5-VL LoRA微调模型加载完成")
        
        # 序列信息
        self.sequences = {
            'data01': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data02': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data04': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data06': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data07': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data19': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
            'data23': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
            'data26': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'}
        }
        
        # 加载类别映射
        with open('dataset/class.json', 'r', encoding='utf-8') as f:
            self.class_map = json.load(f)
    
    def get_image_files(self, seq_dir: Path, seq_info: Dict) -> List[Path]:
        """获取图像文件列表"""
        if seq_info['naming'] == 'simple':
            image_files = list(seq_dir.glob(f"*{seq_info['format']}"))
            return sorted(image_files, key=lambda x: int(x.stem))
        else:
            image_files = list(seq_dir.glob(f"*{seq_info['format']}"))
            def complex_sort_key(path):
                numbers = re.findall(r'(\d+)', path.name)
                return int(numbers[-1]) if numbers else 0
            return sorted(image_files, key=complex_sort_key)
    
    def temporal_json_parse(self, text: str) -> List[Dict]:
        """时序感知JSON解析"""
        try:
            # 清理文本
            clean_text = text.strip()
            
            # 移除markdown标记
            if clean_text.startswith('```json'):
                clean_text = clean_text[7:]
            elif clean_text.startswith('```'):
                clean_text = clean_text[3:]
            if clean_text.endswith('```'):
                clean_text = clean_text[:-3]
            
            clean_text = clean_text.strip()
            
            # 查找JSON数组
            json_start = clean_text.find('[')
            json_end = clean_text.rfind(']')
            
            if json_start != -1 and json_end != -1 and json_end > json_start:
                json_str = clean_text[json_start:json_end+1]
                
                try:
                    detections = json.loads(json_str)
                    if isinstance(detections, list):
                        return detections
                except:
                    pass
                
                # 修复常见格式问题
                json_str = json_str.replace("'", '"')
                json_str = re.sub(r'(\w+):', r'"\1":', json_str)
                
                try:
                    detections = json.loads(json_str)
                    if isinstance(detections, list):
                        return detections
                except:
                    pass
            
            # 正则表达式提取 - 包含frame_id
            detection_pattern = r'\{[^}]*"bbox":\s*\[(\d+),\s*(\d+),\s*(\d+),\s*(\d+)\][^}]*"label":\s*"([^"]+)"[^}]*"frame_id":\s*"([^"]+)"[^}]*\}'
            matches = re.findall(detection_pattern, text)
            
            if matches:
                detections = []
                for match in matches:
                    try:
                        bbox_coords = [int(match[0]), int(match[1]), int(match[2]), int(match[3])]
                        label = match[4].lower()
                        frame_id = match[5]
                        
                        # 验证bbox有效性
                        if (bbox_coords[2] > bbox_coords[0] and bbox_coords[3] > bbox_coords[1] and
                            all(coord >= 0 for coord in bbox_coords)):
                            detections.append({
                                "bbox": bbox_coords,
                                "label": label,
                                "frame_id": frame_id
                            })
                    except:
                        continue
                return detections
            
            # 简单的bbox提取
            bbox_pattern = r'\[(\d+),\s*(\d+),\s*(\d+),\s*(\d+)\]'
            label_pattern = r'"label":\s*"([^"]+)"'
            frame_pattern = r'"frame_id":\s*"([^"]+)"'
            
            bboxes = re.findall(bbox_pattern, text)
            labels = re.findall(label_pattern, text)
            frames = re.findall(frame_pattern, text)
            
            if bboxes:
                detections = []
                for i, bbox in enumerate(bboxes):
                    label = labels[i] if i < len(labels) else "drone"
                    frame_id = frames[i] if i < len(frames) else "0"
                    
                    try:
                        bbox_coords = [int(bbox[0]), int(bbox[1]), int(bbox[2]), int(bbox[3])]
                        if (bbox_coords[2] > bbox_coords[0] and bbox_coords[3] > bbox_coords[1] and
                            all(coord >= 0 for coord in bbox_coords)):
                            detections.append({
                                "bbox": bbox_coords,
                                "label": label.lower(),
                                "frame_id": frame_id
                            })
                    except:
                        continue
                return detections
            
            return []
            
        except Exception as e:
            logger.warning(f"时序JSON解析失败: {e}")
            return []
    
    def temporal_sequence_detection(self, sequence_frame_files: List[Path], sequence_frame_ids: List[str], seq_id: str) -> List[Dict]:
        """时序序列检测 - 严格按照训练时的方式"""
        try:
            seq_info = self.sequences[seq_id]
            
            # 构建时序序列检测消息
            content = []
            
            # 添加所有帧的图像
            for frame_file in sequence_frame_files:
                content.append({
                    "type": "image",
                    "image": str(frame_file)
                })
            
            # 时序感知的检测提示词 - 与训练时保持一致
            content.append({
                "type": "text",
                "text": f"""你是专业的红外弱小目标时序检测专家。请检测这个视频序列中的弱小目标。

时序序列信息：
- 序列编号：{seq_id}
- 序列长度：{len(sequence_frame_files)}帧
- 帧间隔：每隔10帧抽取
- 帧编号：{sequence_frame_ids}
- 图像尺寸：{seq_info['size']}

弱小目标时序特征：
- 目标在帧间可能移动，形成轨迹
- 目标尺寸小（5-20像素）
- 在红外图像中为亮点或暗点
- 目标类别：drone, car, ship, bus, pedestrian, cyclist
- 目标在时序上保持一定的连续性

时序检测要求：
1. 逐帧检测每一帧中的所有弱小目标
2. 考虑目标的时序运动模式和轨迹连续性
3. 为每个检测结果标注对应的frame_id
4. 边界框要精确紧贴目标
5. 利用时序信息提高检测准确性

输出格式（严格JSON）：
[{{"bbox":[x1,y1,x2,y2],"label":"类别","frame_id":"帧号"}}]

开始时序检测："""
            })
            
            messages = [
                {
                    "role": "user",
                    "content": content
                }
            ]
            
            # 应用聊天模板
            text = self.processor.apply_chat_template(
                messages, tokenize=False, add_generation_prompt=True
            )
            
            # 处理时序图像序列
            images = []
            for frame_file in sequence_frame_files:
                img = Image.open(frame_file).convert('RGB')
                img.thumbnail((320, 320), Image.Resampling.LANCZOS)
                images.append(img)
            
            # 处理输入
            inputs = self.processor(
                text=[text],
                images=images,
                padding=True,
                return_tensors="pt"
            )
            
            # 移动到设备
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # 生成时序检测结果
            with torch.no_grad():
                outputs = self.model.generate(
                    **inputs,
                    max_new_tokens=1024,
                    do_sample=True,
                    temperature=0.3,
                    top_p=0.9,
                    repetition_penalty=1.1,
                    pad_token_id=self.processor.tokenizer.eos_token_id,
                )
            
            # 解码输出
            generated_text = self.processor.decode(
                outputs[0][inputs["input_ids"].shape[1]:], 
                skip_special_tokens=True
            )
            
            # 时序感知解析
            detections = self.temporal_json_parse(generated_text)
            
            # 验证和处理检测结果
            valid_detections = []
            for detection in detections:
                if isinstance(detection, dict) and 'bbox' in detection and 'label' in detection:
                    bbox = detection['bbox']
                    if isinstance(bbox, list) and len(bbox) == 4:
                        try:
                            # 确保坐标在合理范围内
                            img_w, img_h = seq_info['size']
                            bbox[0] = max(0, min(int(bbox[0]), img_w))
                            bbox[1] = max(0, min(int(bbox[1]), img_h))
                            bbox[2] = max(bbox[0], min(int(bbox[2]), img_w))
                            bbox[3] = max(bbox[1], min(int(bbox[3]), img_h))
                            
                            # 验证bbox大小合理性
                            width = bbox[2] - bbox[0]
                            height = bbox[3] - bbox[1]
                            
                            if (width > 0 and height > 0 and 
                                width <= 100 and height <= 100 and
                                width >= 3 and height >= 3):
                                
                                detection['bbox'] = bbox
                                
                                # 处理frame_id
                                if 'frame_id' in detection:
                                    try:
                                        detection['frame_idx'] = int(detection['frame_id'])
                                    except:
                                        detection['frame_idx'] = 0
                                else:
                                    detection['frame_id'] = sequence_frame_ids[0]
                                    detection['frame_idx'] = int(sequence_frame_ids[0])
                                
                                valid_detections.append(detection)
                        except:
                            continue
            
            logger.info(f"时序序列检测: {len(valid_detections)} 个目标")
            return valid_detections
                
        except Exception as e:
            logger.error(f"时序序列检测时出错: {e}")
            return []
    
    def temporal_interpolation(self, key_frame_detections: Dict[int, List[Dict]], all_frame_indices: List[int]) -> Dict[int, List[Dict]]:
        """基于时序模型的插值 - 利用学到的时序关系"""
        all_detections = {}
        
        # 复制关键帧检测结果
        for frame_idx in key_frame_detections:
            all_detections[frame_idx] = key_frame_detections[frame_idx]
        
        # 构建轨迹
        trajectories = self._build_temporal_trajectories(key_frame_detections)
        
        # 对每个缺失帧进行时序插值
        for frame_idx in all_frame_indices:
            if frame_idx not in all_detections:
                interpolated_detections = []
                
                # 基于轨迹的时序插值
                for trajectory in trajectories:
                    interpolated_det = self._temporal_interpolate_from_trajectory(trajectory, frame_idx)
                    if interpolated_det:
                        interpolated_detections.append(interpolated_det)
                
                all_detections[frame_idx] = interpolated_detections
        
        return all_detections
    
    def _build_temporal_trajectories(self, key_frame_detections: Dict[int, List[Dict]]) -> List[List[Dict]]:
        """构建时序轨迹"""
        trajectories = []
        used_detections = set()
        
        sorted_frames = sorted(key_frame_detections.keys())
        
        for i, frame_idx in enumerate(sorted_frames):
            detections = key_frame_detections[frame_idx]
            
            for j, det in enumerate(detections):
                if (frame_idx, j) in used_detections:
                    continue
                
                # 开始新轨迹
                trajectory = [{'frame_idx': frame_idx, 'detection': det}]
                used_detections.add((frame_idx, j))
                
                # 向后寻找匹配的检测 - 考虑时序连续性
                current_det = det
                for next_frame_idx in sorted_frames[i+1:]:
                    next_detections = key_frame_detections[next_frame_idx]
                    best_match = None
                    best_score = float('inf')
                    best_idx = -1
                    
                    for k, next_det in enumerate(next_detections):
                        if (next_frame_idx, k) in used_detections:
                            continue
                        
                        # 计算时序匹配分数
                        score = self._calculate_temporal_match_score(current_det, next_det, frame_idx, next_frame_idx)
                        
                        if score < best_score and score < 100:  # 时序匹配阈值
                            best_score = score
                            best_match = next_det
                            best_idx = k
                    
                    if best_match:
                        trajectory.append({'frame_idx': next_frame_idx, 'detection': best_match})
                        used_detections.add((next_frame_idx, best_idx))
                        current_det = best_match
                    else:
                        break
                
                if len(trajectory) >= 2:
                    trajectories.append(trajectory)
        
        return trajectories
    
    def _calculate_temporal_match_score(self, det1: Dict, det2: Dict, frame1: int, frame2: int) -> float:
        """计算时序匹配分数"""
        bbox1 = det1['bbox']
        bbox2 = det2['bbox']
        
        # 中心点距离
        center1 = [(bbox1[0] + bbox1[2]) / 2, (bbox1[1] + bbox1[3]) / 2]
        center2 = [(bbox2[0] + bbox2[2]) / 2, (bbox2[1] + bbox2[3]) / 2]
        
        distance = ((center1[0] - center2[0]) ** 2 + (center1[1] - center2[1]) ** 2) ** 0.5
        
        # 时间间隔
        time_gap = abs(frame2 - frame1)
        
        # 尺寸变化
        size1 = (bbox1[2] - bbox1[0]) * (bbox1[3] - bbox1[1])
        size2 = (bbox2[2] - bbox2[0]) * (bbox2[3] - bbox2[1])
        size_ratio = max(size1, size2) / (min(size1, size2) + 1e-6)
        
        # 标签匹配
        label_penalty = 0 if det1['label'] == det2['label'] else 50
        
        # 综合分数
        score = distance + time_gap * 2 + (size_ratio - 1) * 10 + label_penalty
        
        return score
    
    def _temporal_interpolate_from_trajectory(self, trajectory: List[Dict], target_frame: int) -> Dict:
        """基于轨迹的时序插值"""
        if len(trajectory) < 2:
            return None
        
        # 找到目标帧的前后帧
        prev_point = None
        next_point = None
        
        for point in trajectory:
            if point['frame_idx'] < target_frame:
                prev_point = point
            elif point['frame_idx'] > target_frame and next_point is None:
                next_point = point
                break
        
        if prev_point and next_point:
            # 时序线性插值
            prev_frame = prev_point['frame_idx']
            next_frame = next_point['frame_idx']
            prev_det = prev_point['detection']
            next_det = next_point['detection']
            
            weight = (target_frame - prev_frame) / (next_frame - prev_frame)
            
            prev_bbox = prev_det['bbox']
            next_bbox = next_det['bbox']
            
            interpolated_bbox = [
                int(prev_bbox[0] + (next_bbox[0] - prev_bbox[0]) * weight),
                int(prev_bbox[1] + (next_bbox[1] - prev_bbox[1]) * weight),
                int(prev_bbox[2] + (next_bbox[2] - prev_bbox[2]) * weight),
                int(prev_bbox[3] + (next_bbox[3] - prev_bbox[3]) * weight)
            ]
            
            return {
                "bbox": interpolated_bbox,
                "label": prev_det['label'],
                "frame_id": str(target_frame),
                "frame_idx": target_frame,
                "interpolated": True,
                "temporal_based": True
            }
        
        elif prev_point:
            # 时序外推
            det = prev_point['detection']
            return {
                "bbox": det['bbox'].copy(),
                "label": det['label'],
                "frame_id": str(target_frame),
                "frame_idx": target_frame,
                "interpolated": True,
                "temporal_extrapolated": True
            }
        
        return None
    
    def load_ground_truth(self, sequence_id: str, frame_indices: List[int]) -> Dict[int, List[Dict]]:
        """加载真实标注"""
        seq_info = self.sequences[sequence_id]
        labels_dir = Path(f"dataset/labels/{sequence_id}")
        ground_truth = {}
        
        for frame_idx in frame_indices:
            frame_id = str(frame_idx)
            label_file = labels_dir / f"{frame_id}.txt"
            
            annotations = []
            if label_file.exists():
                try:
                    with open(label_file, 'r') as f:
                        for line in f:
                            parts = line.strip().split()
                            if len(parts) >= 5:
                                class_id = int(parts[0])
                                x_center = float(parts[1])
                                y_center = float(parts[2])
                                width = float(parts[3])
                                height = float(parts[4])
                                
                                # 转换为绝对坐标
                                img_width, img_height = seq_info['size']
                                x1 = int((x_center - width/2) * img_width)
                                y1 = int((y_center - height/2) * img_height)
                                x2 = int((x_center + width/2) * img_width)
                                y2 = int((y_center + height/2) * img_height)
                                
                                class_name = self.class_map.get(str(class_id), "unknown")
                                
                                annotations.append({
                                    "bbox": [x1, y1, x2, y2],
                                    "label": class_name,
                                    "frame_id": frame_id,
                                    "frame_idx": frame_idx
                                })
                except Exception as e:
                    logger.warning(f"加载标注文件失败 {label_file}: {e}")
            
            ground_truth[frame_idx] = annotations
        
        return ground_truth
    
    def calculate_metrics(self, predictions: Dict[int, List[Dict]], ground_truth: Dict[int, List[Dict]], iou_threshold: float = 0.5) -> Dict:
        """计算检测指标"""
        def calculate_iou(box1, box2):
            x1 = max(box1[0], box2[0])
            y1 = max(box1[1], box2[1])
            x2 = min(box1[2], box2[2])
            y2 = min(box1[3], box2[3])
            
            if x2 <= x1 or y2 <= y1:
                return 0.0
            
            intersection = (x2 - x1) * (y2 - y1)
            area1 = (box1[2] - box1[0]) * (box1[3] - box1[1])
            area2 = (box2[2] - box2[0]) * (box2[3] - box2[1])
            union = area1 + area2 - intersection
            
            return intersection / union if union > 0 else 0.0
        
        total_tp = 0
        total_fp = 0
        total_fn = 0
        
        for frame_idx in ground_truth:
            gt_boxes = ground_truth[frame_idx]
            pred_boxes = predictions.get(frame_idx, [])
            
            matched_gt = set()
            
            for pred in pred_boxes:
                if not isinstance(pred, dict) or 'bbox' not in pred:
                    continue
                    
                best_iou = 0
                best_gt_idx = -1
                
                for gt_idx, gt in enumerate(gt_boxes):
                    if gt_idx in matched_gt:
                        continue
                    
                    if not isinstance(gt, dict) or 'bbox' not in gt:
                        continue
                    
                    iou = calculate_iou(pred['bbox'], gt['bbox'])
                    if iou > best_iou and iou >= iou_threshold:
                        best_iou = iou
                        best_gt_idx = gt_idx
                
                if best_gt_idx >= 0:
                    total_tp += 1
                    matched_gt.add(best_gt_idx)
                else:
                    total_fp += 1
            
            total_fn += len(gt_boxes) - len(matched_gt)
        
        precision = total_tp / (total_tp + total_fp) if (total_tp + total_fp) > 0 else 0
        recall = total_tp / (total_tp + total_fn) if (total_tp + total_fn) > 0 else 0
        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
        
        return {
            "precision": precision,
            "recall": recall,
            "f1": f1,
            "tp": total_tp,
            "fp": total_fp,
            "fn": total_fn
        }
    
    def detect_sequence_with_temporal_method(self, sequence_id: str, test_ratio: float = 0.05) -> Dict:
        """使用时序方法检测序列 - 严格按照'每隔10帧抽取'"""
        logger.info(f"开始时序检测序列 {sequence_id}...")
        
        seq_info = self.sequences[sequence_id]
        seq_dir = Path(f"dataset/images/{sequence_id}")
        
        if not seq_dir.exists():
            logger.error(f"序列目录不存在: {seq_dir}")
            return {}
        
        image_files = self.get_image_files(seq_dir, seq_info)
        if not image_files:
            logger.error(f"序列 {sequence_id} 中没有找到图像文件")
            return {}
        
        # 计算测试帧数（前5%）
        total_frames = len(image_files)
        test_frames = max(int(total_frames * test_ratio), 50)
        test_frames = min(test_frames, total_frames)
        test_image_files = image_files[:test_frames]
        
        logger.info(f"序列 {sequence_id}: 总帧数 {total_frames}, 测试帧数 {test_frames}")
        
        # 严格按照"每隔10帧抽取"
        frame_interval = 10
        key_frame_indices = list(range(0, test_frames, frame_interval))
        all_frame_indices = list(range(test_frames))
        
        logger.info(f"关键帧数: {len(key_frame_indices)} (每隔{frame_interval}帧)")
        
        # 时序序列检测
        logger.info(f"开始时序序列检测...")
        key_frame_detections = {}
        
        # 将关键帧组成序列进行检测
        sequence_length = 5  # 5帧序列
        for i in range(0, len(key_frame_indices), sequence_length):
            sequence_indices = key_frame_indices[i:i+sequence_length]
            if len(sequence_indices) >= 3:  # 至少3帧
                sequence_frame_files = [test_image_files[idx] for idx in sequence_indices]
                sequence_frame_ids = [str(idx) for idx in sequence_indices]
                
                # 时序序列检测
                detections = self.temporal_sequence_detection(sequence_frame_files, sequence_frame_ids, sequence_id)
                
                # 将检测结果分配到对应帧
                for detection in detections:
                    if 'frame_idx' in detection:
                        frame_idx = detection['frame_idx']
                        if frame_idx not in key_frame_detections:
                            key_frame_detections[frame_idx] = []
                        key_frame_detections[frame_idx].append(detection)
        
        # 时序插值补全所有帧
        logger.info("进行时序插值补全...")
        all_detections = self.temporal_interpolation(key_frame_detections, all_frame_indices)
        
        # 加载真实标注
        logger.info("加载真实标注...")
        ground_truth = self.load_ground_truth(sequence_id, all_frame_indices)
        
        # 计算指标
        logger.info("计算检测指标...")
        metrics = self.calculate_metrics(all_detections, ground_truth)
        
        # 统计结果
        total_detections = sum(len(dets) for dets in all_detections.values())
        direct_detections = sum(len(dets) for frame_idx, dets in all_detections.items() 
                               if frame_idx in key_frame_detections)
        interpolated_detections = total_detections - direct_detections
        
        result = {
            "sequence_id": sequence_id,
            "total_frames": test_frames,
            "key_frames": len(key_frame_indices),
            "total_detections": total_detections,
            "direct_detections": direct_detections,
            "interpolated_detections": interpolated_detections,
            "metrics": metrics,
            "detections": all_detections
        }
        
        logger.info(f"序列 {sequence_id} 时序检测完成:")
        logger.info(f"  总检测数: {total_detections}")
        logger.info(f"  直接检测: {direct_detections}")
        logger.info(f"  插值检测: {interpolated_detections}")
        logger.info(f"  精确度: {metrics['precision']:.3f}")
        logger.info(f"  召回率: {metrics['recall']:.3f}")
        logger.info(f"  F1分数: {metrics['f1']:.3f}")
        
        return result

def main():
    """主函数"""
    print("=== 时序序列Qwen2.5-VL检测与指标计算系统 ===")
    print("🎯 目标序列: data01, data02, data04, data06, data07, data19, data23, data26")
    print("📊 检测策略: 时序序列检测+时序插值")
    print("🔄 插值策略: 基于时序轨迹的插值")
    print("📈 指标计算: 使用真实labels计算指标")
    print("🤖 模型: 使用时序序列微调后的Qwen2.5-VL模型")
    print("🔧 技术: 时序感知检测+轨迹建模+时序插值")
    
    # 检查模型是否存在
    base_model_path = "qwen2.5-vl-7b"
    lora_model_path = "output/temporal_sequence_qwen_finetuned"
    
    if not os.path.exists(lora_model_path):
        print(f"\n❌ 未找到时序序列微调后的模型: {lora_model_path}")
        print("请先运行时序序列训练")
        return False
    
    print(f"\n✅ 找到时序序列微调后的模型: {lora_model_path}")
    
    # 初始化检测器
    print("\n初始化时序序列Qwen2.5-VL检测器...")
    detector = TemporalSequenceQwenDetector(base_model_path, lora_model_path)
    
    # 开始检测
    print("\n开始时序序列Qwen2.5-VL检测与指标计算...")
    
    sequences = ['data01', 'data02', 'data04', 'data06', 'data07', 'data19', 'data23', 'data26']
    all_results = {}
    
    for sequence_id in sequences:
        print(f"\n{'='*60}")
        print(f"时序检测序列: {sequence_id}")
        print(f"{'='*60}")
        
        result = detector.detect_sequence_with_temporal_method(sequence_id)
        all_results[sequence_id] = result
    
    # 保存完整结果
    os.makedirs("results", exist_ok=True)
    with open("results/temporal_sequence_qwen_detection_results.json", 'w', encoding='utf-8') as f:
        json.dump(all_results, f, ensure_ascii=False, indent=2)
    
    print("\n🎉 时序序列Qwen2.5-VL检测完成！")
    print("📊 检测结果已保存到 results/temporal_sequence_qwen_detection_results.json")
    
    # 统计总体结果
    print("\n📈 总体检测统计:")
    total_detections = 0
    total_direct = 0
    total_interpolated = 0
    overall_metrics = {"tp": 0, "fp": 0, "fn": 0}
    
    for seq_id, result in all_results.items():
        if result:
            total_detections += result['total_detections']
            total_direct += result['direct_detections']
            total_interpolated += result['interpolated_detections']
            
            metrics = result['metrics']
            overall_metrics['tp'] += metrics['tp']
            overall_metrics['fp'] += metrics['fp']
            overall_metrics['fn'] += metrics['fn']
            
            print(f"   {seq_id}: {result['total_detections']} 个检测 "
                  f"(直接: {result['direct_detections']}, 插值: {result['interpolated_detections']}) "
                  f"P: {metrics['precision']:.3f}, R: {metrics['recall']:.3f}, F1: {metrics['f1']:.3f}")
    
    # 计算总体指标
    overall_precision = overall_metrics['tp'] / (overall_metrics['tp'] + overall_metrics['fp']) if (overall_metrics['tp'] + overall_metrics['fp']) > 0 else 0
    overall_recall = overall_metrics['tp'] / (overall_metrics['tp'] + overall_metrics['fn']) if (overall_metrics['tp'] + overall_metrics['fn']) > 0 else 0
    overall_f1 = 2 * overall_precision * overall_recall / (overall_precision + overall_recall) if (overall_precision + overall_recall) > 0 else 0
    
    print(f"\n📊 总体统计:")
    print(f"   总检测数: {total_detections}")
    print(f"   直接检测: {total_direct}")
    print(f"   插值检测: {total_interpolated}")
    print(f"   总体精确度: {overall_precision:.3f}")
    print(f"   总体召回率: {overall_recall:.3f}")
    print(f"   总体F1分数: {overall_f1:.3f}")
    
    print("\n💡 这是使用时序序列微调模型的检测结果:")
    print("包括：时序感知检测 + 轨迹建模 + 时序插值 + 真实指标计算")
    print("🎯 完全基于时序序列，严格按照'每隔10帧抽取'要求")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
