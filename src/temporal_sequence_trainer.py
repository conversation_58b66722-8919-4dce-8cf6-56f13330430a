#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时序序列Qwen2.5-VL微调训练器
完全基于时序序列训练，严格按照"每隔10帧抽取"的要求
"""

import os
import sys
import json
import torch
import logging
from pathlib import Path
from typing import List, Dict, Any
from PIL import Image
from transformers import (
    Qwen2_5_VLForConditionalGeneration,
    AutoProcessor,
)
from peft import LoraConfig, get_peft_model, TaskType
import gc
import re
import random
import numpy as np

# 强制使用GPU卡2
os.environ['CUDA_VISIBLE_DEVICES'] = '2'
torch.cuda.set_device(0)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def clear_memory():
    """清理显存"""
    gc.collect()
    torch.cuda.empty_cache()

def get_image_files(seq_dir: Path, seq_info: Dict) -> List[Path]:
    """获取图像文件列表"""
    if seq_info['naming'] == 'simple':
        image_files = list(seq_dir.glob(f"*{seq_info['format']}"))
        return sorted(image_files, key=lambda x: int(x.stem))
    else:
        image_files = list(seq_dir.glob(f"*{seq_info['format']}"))
        def complex_sort_key(path):
            numbers = re.findall(r'(\d+)', path.name)
            return int(numbers[-1]) if numbers else 0
        return sorted(image_files, key=complex_sort_key)

def load_frame_annotation(seq_id, frame_id, size):
    """加载帧标注"""
    labels_dir = Path(f"dataset/labels/{seq_id}")
    label_file = labels_dir / f"{frame_id}.txt"
    
    if not label_file.exists():
        return []
    
    with open('dataset/class.json', 'r', encoding='utf-8') as f:
        class_map = json.load(f)
    
    annotations = []
    try:
        with open(label_file, 'r') as f:
            for line in f:
                parts = line.strip().split()
                if len(parts) >= 5:
                    class_id = int(parts[0])
                    x_center = float(parts[1])
                    y_center = float(parts[2])
                    width = float(parts[3])
                    height = float(parts[4])
                    
                    # 转换为绝对坐标
                    img_width, img_height = size
                    x1 = int((x_center - width/2) * img_width)
                    y1 = int((y_center - height/2) * img_height)
                    x2 = int((x_center + width/2) * img_width)
                    y2 = int((y_center + height/2) * img_height)
                    
                    # 确保坐标有效
                    x1 = max(0, min(x1, img_width))
                    y1 = max(0, min(y1, img_height))
                    x2 = max(x1, min(x2, img_width))
                    y2 = max(y1, min(y2, img_height))
                    
                    # 只保留有效的bbox
                    if x2 > x1 and y2 > y1:
                        class_name = class_map.get(str(class_id), "unknown")
                        annotations.append({
                            "bbox": [x1, y1, x2, y2],
                            "label": class_name,
                            "frame_id": frame_id
                        })
    except Exception as e:
        logger.warning(f"加载标注文件失败 {label_file}: {e}")
    
    return annotations

def generate_temporal_sequence_training_data():
    """生成纯时序序列训练数据 - 严格按照"每隔10帧抽取"要求"""
    logger.info("生成时序序列训练数据...")
    
    sequences = {
        'data01': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
        'data02': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
        'data04': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
        'data06': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
        'data07': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
    }
    
    all_training_samples = []
    
    for seq_id, seq_info in sequences.items():
        print(f"处理序列 {seq_id}...")
        
        images_dir = Path(f"dataset/images/{seq_id}")
        image_files = get_image_files(images_dir, seq_info)
        
        # 严格按照"每隔10帧抽取"生成时序序列样本
        frame_interval = 10  # 严格按照你的要求
        
        # 生成不同长度的时序序列
        sequence_lengths = [3, 5, 7]  # 3帧、5帧、7帧序列
        
        for seq_length in sequence_lengths:
            # 滑动窗口生成序列
            for start_idx in range(0, len(image_files) - (seq_length-1)*frame_interval, frame_interval*2):
                # 按照间隔抽取帧
                sampled_indices = [start_idx + i*frame_interval for i in range(seq_length)]
                
                # 确保所有帧都存在
                if all(idx < len(image_files) for idx in sampled_indices):
                    sequence_annotations = []
                    sequence_frame_paths = []
                    sequence_frame_ids = []
                    
                    # 收集序列中每帧的信息
                    valid_sequence = True
                    for idx in sampled_indices:
                        frame_file = image_files[idx]
                        frame_id = frame_file.stem
                        sequence_frame_paths.append(str(frame_file))
                        sequence_frame_ids.append(frame_id)
                        
                        # 加载该帧的标注
                        annotations = load_frame_annotation(seq_id, frame_id, seq_info['size'])
                        for ann in annotations:
                            ann_copy = ann.copy()
                            ann_copy['frame_id'] = frame_id
                            sequence_annotations.append(ann_copy)
                    
                    # 只有当序列中至少有一些标注时才生成训练样本
                    if sequence_annotations:
                        # 构建时序序列训练样本
                        content = []
                        
                        # 添加所有帧的图像
                        for frame_path in sequence_frame_paths:
                            content.append({
                                "type": "image",
                                "image": frame_path
                            })
                        
                        # 时序感知的提示词
                        content.append({
                            "type": "text",
                            "text": f"""你是专业的红外弱小目标时序检测专家。请检测这个视频序列中的弱小目标。

时序序列信息：
- 序列编号：{seq_id}
- 序列长度：{seq_length}帧
- 帧间隔：每隔{frame_interval}帧抽取
- 帧编号：{sequence_frame_ids}
- 图像尺寸：{seq_info['size']}

弱小目标时序特征：
- 目标在帧间可能移动，形成轨迹
- 目标尺寸小（5-20像素）
- 在红外图像中为亮点或暗点
- 目标类别：drone, car, ship, bus, pedestrian, cyclist
- 目标在时序上保持一定的连续性

时序检测要求：
1. 逐帧检测每一帧中的所有弱小目标
2. 考虑目标的时序运动模式和轨迹连续性
3. 为每个检测结果标注对应的frame_id
4. 边界框要精确紧贴目标
5. 利用时序信息提高检测准确性

输出格式（严格JSON）：
[{{"bbox":[x1,y1,x2,y2],"label":"类别","frame_id":"帧号"}}]

开始时序检测："""
                        })
                        
                        # 创建时序序列训练样本
                        sequence_sample = {
                            "messages": [
                                {
                                    "role": "user",
                                    "content": content
                                },
                                {
                                    "role": "assistant",
                                    "content": json.dumps(sequence_annotations, ensure_ascii=False)
                                }
                            ]
                        }
                        
                        all_training_samples.append(sequence_sample)
                        print(f"  ✅ 生成{seq_length}帧时序样本: 帧{sequence_frame_ids}, {len(sequence_annotations)}个标注")
    
    # 保存时序序列训练数据
    output_path = "data/temporal_sequence_training_data.json"
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(all_training_samples, f, ensure_ascii=False, indent=2)
    
    logger.info(f"时序序列训练数据保存到: {output_path}")
    logger.info(f"总训练样本数: {len(all_training_samples)}")
    
    return True

class TemporalSequenceTrainer:
    """时序序列训练器"""
    
    def __init__(self, model_path: str):
        self.model_path = model_path
        self.device = "cuda"
        
        clear_memory()
        
        logger.info("加载时序序列模型...")
        
        self.processor = AutoProcessor.from_pretrained(model_path)
        self.model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
            model_path,
            torch_dtype=torch.float16,
            device_map="auto",
            trust_remote_code=True,
            low_cpu_mem_usage=True,
            use_cache=False,
        )
        
        logger.info("时序序列模型加载完成")
    
    def setup_lora(self):
        """设置时序感知LoRA"""
        config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            r=20,  # 适中的rank，专注时序建模
            lora_alpha=40,
            target_modules=["q_proj", "v_proj", "k_proj", "o_proj"],  # 核心注意力模块
            lora_dropout=0.1,
        )
        
        self.model = get_peft_model(self.model, config)
        self.model.print_trainable_parameters()
        logger.info("时序感知LoRA配置完成")
    
    def temporal_sequence_train(self, train_data_path: str, output_dir: str):
        """时序序列训练"""
        logger.info("开始时序序列微调...")
        
        clear_memory()
        
        # 设置LoRA
        self.setup_lora()
        
        # 加载训练数据
        with open(train_data_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 时序优化的训练参数
        optimizer = torch.optim.AdamW(
            self.model.parameters(), 
            lr=3e-5,  # 适中学习率
            weight_decay=0.01,
            betas=(0.9, 0.999),
            eps=1e-8
        )
        
        scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
            optimizer, 
            T_max=len(data)*8
        )
        
        # 训练循环
        self.model.train()
        
        for epoch in range(8):  # 充分训练时序模式
            logger.info(f"时序序列训练轮次 {epoch + 1}/8")
            
            epoch_loss = 0
            valid_samples = 0
            
            for i, sample in enumerate(data):
                messages = sample["messages"]
                
                try:
                    # 处理输入
                    text = self.processor.apply_chat_template(
                        messages, tokenize=False, add_generation_prompt=True
                    )
                    
                    # 处理时序图像序列
                    images = []
                    for content in messages[0]["content"]:
                        if content["type"] == "image":
                            img = Image.open(content["image"]).convert('RGB')
                            # 保持合适分辨率用于时序建模
                            img.thumbnail((320, 320), Image.Resampling.LANCZOS)
                            images.append(img)
                    
                    if len(images) < 2:  # 确保是时序序列
                        continue
                    
                    # 处理输入
                    inputs = self.processor(
                        text=[text],
                        images=images,
                        padding=True,
                        return_tensors="pt"
                    )
                    
                    # 移动到设备
                    inputs = {k: v.to(self.device) for k, v in inputs.items()}
                    inputs["labels"] = inputs["input_ids"].clone()
                    
                    # 前向传播
                    optimizer.zero_grad()
                    outputs = self.model(**inputs)
                    loss = outputs.loss
                    
                    # 反向传播
                    loss.backward()
                    torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                    optimizer.step()
                    scheduler.step()
                    
                    epoch_loss += loss.item()
                    valid_samples += 1
                    
                    if (i + 1) % 3 == 0:
                        logger.info(f"  样本 {i+1}/{len(data)}, 损失: {loss.item():.4f}, 序列长度: {len(images)}, LR: {scheduler.get_last_lr()[0]:.6f}")
                    
                    clear_memory()
                    
                except Exception as e:
                    logger.warning(f"处理时序样本 {i} 时出错: {e}")
                    continue
            
            if valid_samples > 0:
                avg_loss = epoch_loss / valid_samples
                logger.info(f"轮次 {epoch + 1} 平均损失: {avg_loss:.4f}")
        
        # 保存模型
        os.makedirs(output_dir, exist_ok=True)
        self.model.save_pretrained(output_dir)
        self.processor.save_pretrained(output_dir)
        
        logger.info(f"时序序列微调完成，模型保存到: {output_dir}")
        return True

def main():
    """主函数"""
    print("=== 时序序列Qwen2.5-VL微调训练系统 ===")
    print("🎯 目标: 学习时序目标检测和轨迹建模")
    print("📊 策略: 纯时序序列训练，严格按照'每隔10帧抽取'")
    print("🎬 训练方式: 时序感知LoRA微调")
    print("🔧 技术: 时序序列建模 + 轨迹连续性学习")
    print("⏱️  预计时间: 25-35分钟")
    
    # 第1步：生成训练数据
    print("\n第1步：生成时序序列训练数据...")
    if not generate_temporal_sequence_training_data():
        print("❌ 训练数据生成失败")
        return False
    
    # 第2步：初始化训练器
    print("\n第2步：初始化时序序列训练器...")
    model_path = "qwen2.5-vl-7b"
    trainer = TemporalSequenceTrainer(model_path)
    
    # 第3步：开始训练
    print("\n第3步：开始时序序列微调...")
    output_dir = "output/temporal_sequence_qwen_finetuned"
    success = trainer.temporal_sequence_train("data/temporal_sequence_training_data.json", output_dir)
    
    if success:
        print("\n🎉 时序序列微调完成！")
        print(f"📁 模型保存到: {output_dir}")
        print("📋 这是时序感知版本，专注于轨迹建模")
        print("💡 应该能更好地理解目标运动和时序关系")
    else:
        print("\n❌ 时序序列微调失败！")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
