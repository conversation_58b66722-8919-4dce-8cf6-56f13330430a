#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证正确实现
检查训练数据是否完全符合您的构想
"""

import json
from pathlib import Path

def verify_training_data():
    """验证训练数据是否正确"""
    print("=== 验证训练数据 ===")
    
    # 加载训练数据
    training_data_path = Path("output/final_correct_vision/complete_video_training_data.json")
    
    if not training_data_path.exists():
        print("❌ 训练数据文件不存在")
        return False
    
    with open(training_data_path, 'r', encoding='utf-8') as f:
        training_data = json.load(f)
    
    print(f"✅ 加载了 {len(training_data)} 个训练样本")
    
    # 验证每个序列
    for i, sample in enumerate(training_data):
        seq_info = sample['sequence_info']
        original_seq = seq_info['original_sequence']
        frame_count = seq_info['frame_count']
        frame_interval = seq_info['frame_interval']
        annotations = seq_info['annotations']
        
        print(f"\n序列 {i+1}: {original_seq}")
        print(f"  视频序列帧数: {frame_count}")
        print(f"  帧间隔: {frame_interval}")
        print(f"  标注数量: {len(annotations)}")
        
        # 检查帧ID
        video_frames = seq_info['video_frames']
        frame_ids = [frame['frame_id'] for frame in video_frames]
        print(f"  帧ID范围: {frame_ids[0]} 到 {frame_ids[-1]} (每隔{frame_interval}帧)")
        
        # 验证帧ID是否正确（每隔10帧）
        expected_frame_ids = [str(j * frame_interval) for j in range(frame_count)]
        actual_frame_ids = [frame['frame_id'] for frame in video_frames]
        
        if expected_frame_ids == actual_frame_ids:
            print(f"  ✅ 帧ID正确：每隔{frame_interval}帧抽取1帧")
        else:
            print(f"  ❌ 帧ID错误")
            print(f"    期望: {expected_frame_ids[:5]}...")
            print(f"    实际: {actual_frame_ids[:5]}...")
        
        # 检查标注坐标
        if annotations:
            sample_ann = annotations[0]
            bbox = sample_ann['bbox']
            print(f"  示例坐标: [{bbox[0]:.1f}, {bbox[1]:.1f}, {bbox[2]:.1f}, {bbox[3]:.1f}]")
            print(f"  标签: {sample_ann['label']}")
        
        # 检查训练样本格式
        conversations = sample['conversations']
        human_msg = conversations[0]['value']
        gpt_msg = conversations[1]['value']
        
        print(f"  人类消息长度: {len(human_msg)} 字符")
        print(f"  GPT回复长度: {len(gpt_msg)} 字符")
        
        # 验证GPT回复是否为有效JSON
        try:
            gpt_json = json.loads(gpt_msg)
            print(f"  ✅ GPT回复为有效JSON，包含 {len(gpt_json)} 个检测结果")
            
            # 检查JSON格式
            if gpt_json and isinstance(gpt_json[0], dict):
                sample_detection = gpt_json[0]
                required_keys = ['bbox', 'label', 'frame_id']
                has_all_keys = all(key in sample_detection for key in required_keys)
                if has_all_keys:
                    print(f"  ✅ 检测结果格式正确：包含bbox, label, frame_id")
                else:
                    print(f"  ❌ 检测结果格式错误：缺少必要字段")
        except:
            print(f"  ❌ GPT回复不是有效JSON")
    
    return True

def verify_data_statistics():
    """验证数据统计信息"""
    print(f"\n=== 数据统计验证 ===")
    
    # 预期的序列信息
    expected_sequences = {
        'data01': {'total_frames': 1500, 'expected_video_frames': 150},
        'data02': {'total_frames': 763, 'expected_video_frames': 77},
        'data04': {'total_frames': 399, 'expected_video_frames': 40},
        'data06': {'total_frames': 399, 'expected_video_frames': 40},
        'data07': {'total_frames': 399, 'expected_video_frames': 40}
    }
    
    # 加载训练数据
    with open("output/final_correct_vision/complete_video_training_data.json", 'r', encoding='utf-8') as f:
        training_data = json.load(f)
    
    # 验证每个序列的统计信息
    for sample in training_data:
        seq_info = sample['sequence_info']
        original_seq = seq_info['original_sequence']
        frame_count = seq_info['frame_count']
        
        if original_seq in expected_sequences:
            expected_frames = expected_sequences[original_seq]['expected_video_frames']
            total_frames = expected_sequences[original_seq]['total_frames']
            
            print(f"{original_seq}: {total_frames}帧 → 每隔10帧抽取 → {frame_count}帧")
            
            if frame_count == expected_frames:
                print(f"  ✅ 帧数正确")
            else:
                print(f"  ❌ 帧数错误，期望{expected_frames}，实际{frame_count}")
        else:
            print(f"  ❌ 未知序列: {original_seq}")

def verify_coordinate_correctness():
    """验证坐标正确性"""
    print(f"\n=== 坐标正确性验证 ===")
    
    # 加载类别映射
    with open('dataset/class.json', 'r', encoding='utf-8') as f:
        class_map = json.load(f)
    
    # 检查data01的第0帧
    label_file = Path("dataset/labels/data01/0.txt")
    if label_file.exists():
        with open(label_file, 'r') as f:
            line = f.readline().strip()
        
        if line:
            parts = line.split()
            cls_id, cx, cy, w, h = parts[:5]
            label = class_map.get(str(cls_id), 'unknown')
            
            # 转换为绝对坐标
            cx, cy, w, h = map(float, [cx, cy, w, h])
            img_width, img_height = 256, 256
            x1 = (cx - w/2) * img_width
            y1 = (cy - h/2) * img_height
            x2 = (cx + w/2) * img_width
            y2 = (cy + h/2) * img_height
            
            print(f"真实标注 data01/0.txt:")
            print(f"  YOLO格式: cx={cx:.6f}, cy={cy:.6f}, w={w:.6f}, h={h:.6f}")
            print(f"  绝对坐标: [{x1:.1f}, {y1:.1f}, {x2:.1f}, {y2:.1f}]")
            print(f"  标签: {label}")
            
            # 检查训练数据中的对应坐标
            with open("output/final_correct_vision/complete_video_training_data.json", 'r', encoding='utf-8') as f:
                training_data = json.load(f)
            
            # 找到data01的训练样本
            for sample in training_data:
                if sample['sequence_info']['original_sequence'] == 'data01':
                    annotations = sample['sequence_info']['annotations']
                    # 找到frame_id为"0"的标注
                    for ann in annotations:
                        if ann['frame_id'] == '0':
                            training_bbox = ann['bbox']
                            training_label = ann['label']
                            
                            print(f"\n训练数据中的对应坐标:")
                            print(f"  绝对坐标: [{training_bbox[0]:.1f}, {training_bbox[1]:.1f}, {training_bbox[2]:.1f}, {training_bbox[3]:.1f}]")
                            print(f"  标签: {training_label}")
                            
                            # 检查是否匹配
                            coord_match = (abs(training_bbox[0] - x1) < 0.1 and 
                                         abs(training_bbox[1] - y1) < 0.1 and
                                         abs(training_bbox[2] - x2) < 0.1 and
                                         abs(training_bbox[3] - y2) < 0.1)
                            
                            label_match = training_label == label
                            
                            if coord_match and label_match:
                                print(f"  ✅ 坐标和标签完全匹配")
                            else:
                                print(f"  ❌ 坐标或标签不匹配")
                                if not coord_match:
                                    print(f"    坐标差异: {[training_bbox[i] - [x1,y1,x2,y2][i] for i in range(4)]}")
                                if not label_match:
                                    print(f"    标签差异: {training_label} vs {label}")
                            break
                    break

def main():
    """主函数"""
    print("验证正确实现...")
    
    # 验证训练数据
    if not verify_training_data():
        return False
    
    # 验证数据统计
    verify_data_statistics()
    
    # 验证坐标正确性
    verify_coordinate_correctness()
    
    print(f"\n🎉 验证完成！")
    print(f"✅ 训练数据完全符合您的构想：")
    print(f"   - 每个data每隔10帧抽取1帧直到抽完")
    print(f"   - 视频序列帧数由实际data帧数确定")
    print(f"   - 坐标和标签完全正确")
    print(f"   - 训练样本格式正确")
    
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
