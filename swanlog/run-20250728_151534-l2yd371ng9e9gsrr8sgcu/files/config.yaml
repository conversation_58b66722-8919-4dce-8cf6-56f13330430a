FRAMEWORK:
  desc: ''
  sort: 0
  value: 🤗transformers
_name_or_path:
  desc: ''
  sort: 58
  value: qwen2.5-vl-7b
accelerator_config:
  desc: ''
  sort: 160
  value:
    dispatch_batches: null
    even_batches: true
    gradient_accumulation_kwargs: null
    non_blocking: false
    split_batches: false
    use_seedable_sampler: true
adafactor:
  desc: ''
  sort: 165
  value: false
adam_beta1:
  desc: ''
  sort: 99
  value: 0.9
adam_beta2:
  desc: ''
  sort: 100
  value: 0.999
adam_epsilon:
  desc: ''
  sort: 101
  value: 1.0e-08
add_cross_attention:
  desc: ''
  sort: 18
  value: false
architectures:
  desc: ''
  sort: 45
  value:
  - Qwen2_5_VLForConditionalGeneration
attention_dropout:
  desc: ''
  sort: 60
  value: 0.0
auto_find_batch_size:
  desc: ''
  sort: 194
  value: false
average_tokens_across_devices:
  desc: ''
  sort: 211
  value: false
bad_words_ids:
  desc: ''
  sort: 35
  value: null
batch_eval_metrics:
  desc: ''
  sort: 206
  value: false
begin_suppress_tokens:
  desc: ''
  sort: 44
  value: null
bf16:
  desc: ''
  sort: 131
  value: false
bf16_full_eval:
  desc: ''
  sort: 135
  value: false
bos_token_id:
  desc: ''
  sort: 51
  value: 151643
chunk_size_feed_forward:
  desc: ''
  sort: 14
  value: 0
cross_attention_hidden_size:
  desc: ''
  sort: 17
  value: null
data_seed:
  desc: ''
  sort: 128
  value: null
dataloader_drop_last:
  desc: ''
  sort: 143
  value: true
dataloader_num_workers:
  desc: ''
  sort: 145
  value: 0
dataloader_persistent_workers:
  desc: ''
  sort: 173
  value: false
dataloader_pin_memory:
  desc: ''
  sort: 172
  value: true
dataloader_prefetch_factor:
  desc: ''
  sort: 146
  value: null
ddp_backend:
  desc: ''
  sort: 139
  value: null
ddp_broadcast_buffers:
  desc: ''
  sort: 171
  value: null
ddp_bucket_cap_mb:
  desc: ''
  sort: 170
  value: null
ddp_find_unused_parameters:
  desc: ''
  sort: 169
  value: null
ddp_timeout:
  desc: ''
  sort: 198
  value: 1800
debug:
  desc: ''
  sort: 142
  value: []
decoder_start_token_id:
  desc: ''
  sort: 55
  value: null
deepspeed:
  desc: ''
  sort: 161
  value: null
disable_tqdm:
  desc: ''
  sort: 149
  value: false
diversity_penalty:
  desc: ''
  sort: 26
  value: 0.0
do_eval:
  desc: ''
  sort: 85
  value: false
do_predict:
  desc: ''
  sort: 86
  value: false
do_sample:
  desc: ''
  sort: 22
  value: false
do_train:
  desc: ''
  sort: 84
  value: false
early_stopping:
  desc: ''
  sort: 23
  value: false
encoder_no_repeat_ngram_size:
  desc: ''
  sort: 34
  value: 0
eos_token_id:
  desc: ''
  sort: 53
  value: 151645
eval_accumulation_steps:
  desc: ''
  sort: 94
  value: null
eval_delay:
  desc: ''
  sort: 95
  value: 0
eval_do_concat_batches:
  desc: ''
  sort: 188
  value: true
eval_on_start:
  desc: ''
  sort: 207
  value: false
eval_steps:
  desc: ''
  sort: 144
  value: null
eval_strategy:
  desc: ''
  sort: 87
  value: 'no'
eval_use_gather_object:
  desc: ''
  sort: 210
  value: false
exponential_decay_length_penalty:
  desc: ''
  sort: 42
  value: null
finetuning_task:
  desc: ''
  sort: 46
  value: null
forced_bos_token_id:
  desc: ''
  sort: 39
  value: null
forced_eos_token_id:
  desc: ''
  sort: 40
  value: null
fp16:
  desc: ''
  sort: 132
  value: true
fp16_backend:
  desc: ''
  sort: 189
  value: auto
fp16_full_eval:
  desc: ''
  sort: 136
  value: false
fp16_opt_level:
  desc: ''
  sort: 133
  value: O1
fsdp:
  desc: ''
  sort: 156
  value: []
fsdp_config:
  desc: ''
  sort: 158
  value:
    min_num_params: 0
    xla: false
    xla_fsdp_grad_ckpt: false
    xla_fsdp_v2: false
fsdp_min_num_params:
  desc: ''
  sort: 157
  value: 0
fsdp_transformer_layer_cls_to_wrap:
  desc: ''
  sort: 159
  value: null
full_determinism:
  desc: ''
  sort: 195
  value: false
gradient_accumulation_steps:
  desc: ''
  sort: 93
  value: 8
gradient_checkpointing:
  desc: ''
  sort: 184
  value: false
gradient_checkpointing_kwargs:
  desc: ''
  sort: 185
  value: null
greater_is_better:
  desc: ''
  sort: 154
  value: null
group_by_length:
  desc: ''
  sort: 166
  value: false
half_precision_backend:
  desc: ''
  sort: 134
  value: auto
hidden_act:
  desc: ''
  sort: 64
  value: silu
hidden_size:
  desc: ''
  sort: 65
  value: 3584
hub_always_push:
  desc: ''
  sort: 182
  value: false
hub_model_id:
  desc: ''
  sort: 178
  value: null
hub_private_repo:
  desc: ''
  sort: 181
  value: null
hub_revision:
  desc: ''
  sort: 183
  value: null
hub_strategy:
  desc: ''
  sort: 179
  value: every_save
hub_token:
  desc: ''
  sort: 180
  value: <HUB_TOKEN>
id2label:
  desc: ''
  sort: 47
  value:
    '0': LABEL_0
    '1': LABEL_1
ignore_data_skip:
  desc: ''
  sort: 155
  value: false
image_token_id:
  desc: ''
  sort: 4
  value: 151655
include_for_metrics:
  desc: ''
  sort: 187
  value: []
include_inputs_for_metrics:
  desc: ''
  sort: 186
  value: false
include_num_input_tokens_seen:
  desc: ''
  sort: 203
  value: false
include_tokens_per_second:
  desc: ''
  sort: 202
  value: false
initializer_range:
  desc: ''
  sort: 66
  value: 0.02
intermediate_size:
  desc: ''
  sort: 67
  value: 18944
is_decoder:
  desc: ''
  sort: 16
  value: false
is_encoder_decoder:
  desc: ''
  sort: 15
  value: false
jit_mode_eval:
  desc: ''
  sort: 129
  value: false
label2id:
  desc: ''
  sort: 48
  value:
    LABEL_0: 0
    LABEL_1: 1
label_names:
  desc: ''
  sort: 151
  value: null
label_smoothing_factor:
  desc: ''
  sort: 162
  value: 0.0
learning_rate:
  desc: ''
  sort: 97
  value: 2.0e-05
length_column_name:
  desc: ''
  sort: 167
  value: length
length_penalty:
  desc: ''
  sort: 32
  value: 1.0
liger_kernel_config:
  desc: ''
  sort: 209
  value: null
load_best_model_at_end:
  desc: ''
  sort: 152
  value: false
local_rank:
  desc: ''
  sort: 138
  value: 0
log_level:
  desc: ''
  sort: 109
  value: passive
log_level_replica:
  desc: ''
  sort: 110
  value: warning
log_on_each_node:
  desc: ''
  sort: 111
  value: true
logging_dir:
  desc: ''
  sort: 112
  value: output/complete_video_sequence_lora/runs/Jul28_15-15-31_3e081f3960ca
logging_first_step:
  desc: ''
  sort: 114
  value: false
logging_nan_inf_filter:
  desc: ''
  sort: 116
  value: true
logging_steps:
  desc: ''
  sort: 115
  value: 5
logging_strategy:
  desc: ''
  sort: 113
  value: steps
lr_scheduler_kwargs:
  desc: ''
  sort: 106
  value: {}
lr_scheduler_type:
  desc: ''
  sort: 105
  value: cosine
max_grad_norm:
  desc: ''
  sort: 102
  value: 1.0
max_length:
  desc: ''
  sort: 20
  value: 20
max_position_embeddings:
  desc: ''
  sort: 68
  value: 128000
max_steps:
  desc: ''
  sort: 104
  value: -1
max_window_layers:
  desc: ''
  sort: 69
  value: 28
metric_for_best_model:
  desc: ''
  sort: 153
  value: null
min_length:
  desc: ''
  sort: 21
  value: 0
model_num_parameters:
  desc: ''
  sort: 212
  value: 8312351744
model_type:
  desc: ''
  sort: 70
  value: qwen2_5_vl
mp_parameters:
  desc: ''
  sort: 193
  value: ''
neftune_noise_alpha:
  desc: ''
  sort: 204
  value: null
no_cuda:
  desc: ''
  sort: 124
  value: false
no_repeat_ngram_size:
  desc: ''
  sort: 33
  value: 0
num_attention_heads:
  desc: ''
  sort: 71
  value: 28
num_beam_groups:
  desc: ''
  sort: 25
  value: 1
num_beams:
  desc: ''
  sort: 24
  value: 1
num_hidden_layers:
  desc: ''
  sort: 72
  value: 28
num_key_value_heads:
  desc: ''
  sort: 73
  value: 4
num_return_sequences:
  desc: ''
  sort: 36
  value: 1
num_train_epochs:
  desc: ''
  sort: 103
  value: 5
optim:
  desc: ''
  sort: 163
  value: adamw_torch
optim_args:
  desc: ''
  sort: 164
  value: null
optim_target_modules:
  desc: ''
  sort: 205
  value: null
output_attentions:
  desc: ''
  sort: 81
  value: false
output_dir:
  desc: ''
  sort: 82
  value: output/complete_video_sequence_lora
output_hidden_states:
  desc: ''
  sort: 7
  value: false
output_scores:
  desc: ''
  sort: 37
  value: false
overwrite_output_dir:
  desc: ''
  sort: 83
  value: false
pad_token_id:
  desc: ''
  sort: 52
  value: null
past_index:
  desc: ''
  sort: 147
  value: -1
peft_config:
  desc: ''
  sort: 1
  value:
    default: 'LoraConfig(task_type=<TaskType.CAUSAL_LM: ''CAUSAL_LM''>, peft_type=<PeftType.LORA:
      ''LORA''>, auto_mapping=None, base_model_name_or_path=''qwen2.5-vl-7b'', revision=None,
      inference_mode=False, r=32, target_modules={''k_proj'', ''q_proj'', ''o_proj'',
      ''v_proj''}, exclude_modules=None, lora_alpha=64, lora_dropout=0.05, fan_in_fan_out=False,
      bias=''none'', use_rslora=False, modules_to_save=None, init_lora_weights=True,
      layers_to_transform=None, layers_pattern=None, rank_pattern={}, alpha_pattern={},
      megatron_config=None, megatron_core=''megatron.core'', trainable_token_indices=None,
      loftq_config={}, eva_config=None, corda_config=None, use_dora=False, use_qalora=False,
      qalora_group_size=16, layer_replication=None, runtime_config=LoraRuntimeConfig(ephemeral_gpu_offload=False),
      lora_bias=False)'
per_device_eval_batch_size:
  desc: ''
  sort: 90
  value: 8
per_device_train_batch_size:
  desc: ''
  sort: 89
  value: 1
per_gpu_eval_batch_size:
  desc: ''
  sort: 92
  value: null
per_gpu_train_batch_size:
  desc: ''
  sort: 91
  value: null
prediction_loss_only:
  desc: ''
  sort: 88
  value: false
prefix:
  desc: ''
  sort: 50
  value: null
problem_type:
  desc: ''
  sort: 57
  value: null
pruned_heads:
  desc: ''
  sort: 12
  value: {}
push_to_hub:
  desc: ''
  sort: 176
  value: false
push_to_hub_model_id:
  desc: ''
  sort: 190
  value: null
push_to_hub_organization:
  desc: ''
  sort: 191
  value: null
push_to_hub_token:
  desc: ''
  sort: 192
  value: <PUSH_TO_HUB_TOKEN>
ray_scope:
  desc: ''
  sort: 197
  value: last
remove_invalid_values:
  desc: ''
  sort: 41
  value: false
remove_unused_columns:
  desc: ''
  sort: 150
  value: false
repetition_penalty:
  desc: ''
  sort: 31
  value: 1.0
report_to:
  desc: ''
  sort: 168
  value:
  - swanlab
restore_callback_states_from_checkpoint:
  desc: ''
  sort: 123
  value: false
resume_from_checkpoint:
  desc: ''
  sort: 177
  value: null
return_dict:
  desc: ''
  sort: 6
  value: true
return_dict_in_generate:
  desc: ''
  sort: 38
  value: false
rms_norm_eps:
  desc: ''
  sort: 74
  value: 1.0e-06
rope_scaling:
  desc: ''
  sort: 79
  value:
    mrope_section:
    - 16
    - 24
    - 24
    rope_type: default
    type: default
rope_theta:
  desc: ''
  sort: 75
  value: 1000000.0
run_name:
  desc: ''
  sort: 148
  value: output/complete_video_sequence_lora
save_on_each_node:
  desc: ''
  sort: 121
  value: false
save_only_model:
  desc: ''
  sort: 122
  value: false
save_safetensors:
  desc: ''
  sort: 120
  value: true
save_steps:
  desc: ''
  sort: 118
  value: 20
save_strategy:
  desc: ''
  sort: 117
  value: steps
save_total_limit:
  desc: ''
  sort: 119
  value: 3
seed:
  desc: ''
  sort: 127
  value: 42
sep_token_id:
  desc: ''
  sort: 54
  value: null
skip_memory_metrics:
  desc: ''
  sort: 174
  value: true
sliding_window:
  desc: ''
  sort: 76
  value: 32768
suppress_tokens:
  desc: ''
  sort: 43
  value: null
task_specific_params:
  desc: ''
  sort: 56
  value: null
temperature:
  desc: ''
  sort: 27
  value: 1.0
text_config:
  desc: ''
  sort: 3
  value:
    _name_or_path: ''
    add_cross_attention: false
    architectures:
    - Qwen2_5_VLForConditionalGeneration
    attention_dropout: 0.0
    bad_words_ids: null
    begin_suppress_tokens: null
    bos_token_id: 151643
    chunk_size_feed_forward: 0
    cross_attention_hidden_size: null
    decoder_start_token_id: null
    diversity_penalty: 0.0
    do_sample: false
    early_stopping: false
    encoder_no_repeat_ngram_size: 0
    eos_token_id: 151645
    exponential_decay_length_penalty: null
    finetuning_task: null
    forced_bos_token_id: null
    forced_eos_token_id: null
    hidden_act: silu
    hidden_size: 3584
    id2label:
      '0': LABEL_0
      '1': LABEL_1
    image_token_id: null
    initializer_range: 0.02
    intermediate_size: 18944
    is_decoder: false
    is_encoder_decoder: false
    label2id:
      LABEL_0: 0
      LABEL_1: 1
    layer_types:
    - full_attention
    - full_attention
    - full_attention
    - full_attention
    - full_attention
    - full_attention
    - full_attention
    - full_attention
    - full_attention
    - full_attention
    - full_attention
    - full_attention
    - full_attention
    - full_attention
    - full_attention
    - full_attention
    - full_attention
    - full_attention
    - full_attention
    - full_attention
    - full_attention
    - full_attention
    - full_attention
    - full_attention
    - full_attention
    - full_attention
    - full_attention
    - full_attention
    length_penalty: 1.0
    max_length: 20
    max_position_embeddings: 128000
    max_window_layers: 28
    min_length: 0
    model_type: qwen2_5_vl_text
    no_repeat_ngram_size: 0
    num_attention_heads: 28
    num_beam_groups: 1
    num_beams: 1
    num_hidden_layers: 28
    num_key_value_heads: 4
    num_return_sequences: 1
    output_attentions: false
    output_hidden_states: false
    output_scores: false
    pad_token_id: null
    prefix: null
    problem_type: null
    pruned_heads: {}
    remove_invalid_values: false
    repetition_penalty: 1.0
    return_dict: true
    return_dict_in_generate: false
    rms_norm_eps: 1.0e-06
    rope_scaling:
      mrope_section:
      - 16
      - 24
      - 24
      rope_type: default
      type: default
    rope_theta: 1000000.0
    sep_token_id: null
    sliding_window: null
    suppress_tokens: null
    task_specific_params: null
    temperature: 1.0
    tf_legacy_loss: false
    tie_encoder_decoder: false
    tie_word_embeddings: false
    tokenizer_class: null
    top_k: 50
    top_p: 1.0
    torch_dtype: float16
    torchscript: false
    typical_p: 1.0
    use_bfloat16: false
    use_cache: true
    use_sliding_window: false
    video_token_id: null
    vision_end_token_id: 151653
    vision_start_token_id: 151652
    vision_token_id: 151654
    vocab_size: 152064
tf32:
  desc: ''
  sort: 137
  value: null
tf_legacy_loss:
  desc: ''
  sort: 11
  value: false
tie_encoder_decoder:
  desc: ''
  sort: 19
  value: false
tie_word_embeddings:
  desc: ''
  sort: 13
  value: false
tokenizer_class:
  desc: ''
  sort: 49
  value: null
top_k:
  desc: ''
  sort: 28
  value: 50
top_p:
  desc: ''
  sort: 29
  value: 1.0
torch_compile:
  desc: ''
  sort: 199
  value: false
torch_compile_backend:
  desc: ''
  sort: 200
  value: null
torch_compile_mode:
  desc: ''
  sort: 201
  value: null
torch_dtype:
  desc: ''
  sort: 9
  value: float16
torch_empty_cache_steps:
  desc: ''
  sort: 96
  value: null
torchdynamo:
  desc: ''
  sort: 196
  value: null
torchscript:
  desc: ''
  sort: 8
  value: false
tpu_metrics_debug:
  desc: ''
  sort: 141
  value: false
tpu_num_cores:
  desc: ''
  sort: 140
  value: null
transformers_version:
  desc: ''
  sort: 59
  value: 4.53.2
typical_p:
  desc: ''
  sort: 30
  value: 1.0
use_bfloat16:
  desc: ''
  sort: 10
  value: false
use_cache:
  desc: ''
  sort: 77
  value: false
use_cpu:
  desc: ''
  sort: 125
  value: false
use_ipex:
  desc: ''
  sort: 130
  value: false
use_legacy_prediction_loop:
  desc: ''
  sort: 175
  value: false
use_liger_kernel:
  desc: ''
  sort: 208
  value: false
use_mps_device:
  desc: ''
  sort: 126
  value: false
use_sliding_window:
  desc: ''
  sort: 78
  value: false
video_token_id:
  desc: ''
  sort: 5
  value: 151656
vision_config:
  desc: ''
  sort: 2
  value:
    _name_or_path: ''
    add_cross_attention: false
    architectures: null
    bad_words_ids: null
    begin_suppress_tokens: null
    bos_token_id: null
    chunk_size_feed_forward: 0
    cross_attention_hidden_size: null
    decoder_start_token_id: null
    depth: 32
    diversity_penalty: 0.0
    do_sample: false
    early_stopping: false
    encoder_no_repeat_ngram_size: 0
    eos_token_id: null
    exponential_decay_length_penalty: null
    finetuning_task: null
    forced_bos_token_id: null
    forced_eos_token_id: null
    fullatt_block_indexes:
    - 7
    - 15
    - 23
    - 31
    hidden_act: silu
    hidden_size: 1280
    id2label:
      '0': LABEL_0
      '1': LABEL_1
    in_channels: 3
    in_chans: 3
    initializer_range: 0.02
    intermediate_size: 3420
    is_decoder: false
    is_encoder_decoder: false
    label2id:
      LABEL_0: 0
      LABEL_1: 1
    length_penalty: 1.0
    max_length: 20
    min_length: 0
    model_type: qwen2_5_vl
    no_repeat_ngram_size: 0
    num_beam_groups: 1
    num_beams: 1
    num_heads: 16
    num_return_sequences: 1
    out_hidden_size: 3584
    output_attentions: false
    output_hidden_states: false
    output_scores: false
    pad_token_id: null
    patch_size: 14
    prefix: null
    problem_type: null
    pruned_heads: {}
    remove_invalid_values: false
    repetition_penalty: 1.0
    return_dict: true
    return_dict_in_generate: false
    sep_token_id: null
    spatial_merge_size: 2
    spatial_patch_size: 14
    suppress_tokens: null
    task_specific_params: null
    temperature: 1.0
    temporal_patch_size: 2
    tf_legacy_loss: false
    tie_encoder_decoder: false
    tie_word_embeddings: true
    tokenizer_class: null
    tokens_per_second: 2
    top_k: 50
    top_p: 1.0
    torch_dtype: float16
    torchscript: false
    typical_p: 1.0
    use_bfloat16: false
    window_size: 112
vision_end_token_id:
  desc: ''
  sort: 62
  value: 151653
vision_start_token_id:
  desc: ''
  sort: 61
  value: 151652
vision_token_id:
  desc: ''
  sort: 63
  value: 151654
vocab_size:
  desc: ''
  sort: 80
  value: 152064
warmup_ratio:
  desc: ''
  sort: 107
  value: 0.0
warmup_steps:
  desc: ''
  sort: 108
  value: 20
weight_decay:
  desc: ''
  sort: 98
  value: 0.01
