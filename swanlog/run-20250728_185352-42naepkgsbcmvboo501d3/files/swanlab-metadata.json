{"memory": "504", "cpu": {"brand": "AMD EPYC 7532 32-Core Processor", "cores": 128}, "gpu": {"nvidia": {"driver": "550.163.01", "cores": 4, "type": ["NVIDIA A100-SXM4-80GB", "NVIDIA A100-SXM4-80GB", "NVIDIA A100-SXM4-80GB", "NVIDIA A100-SXM4-80GB"], "memory": ["80", "80", "80", "80"], "cuda": null, "architecture": ["Ampere", "Ampere", "Ampere", "Ampere"], "cudacores": [6912, 6912, 6912, 6912]}}, "os": "Linux-6.11.0-25-generic-x86_64-with-glibc2.31", "os_pretty_name": "Ubuntu 20.04.3 LTS", "hostname": "3e081f3960ca", "pid": 3509006, "cwd": "/home/<USER>/weak_target_finetune", "python": "3.10.18", "python_verbose": "3.10.18 (main, Jun  5 2025, 13:14:17) [GCC 11.2.0]", "executable": "/home/<USER>/miniconda3/envs/qwen/bin/python", "command": "train.py", "git_remote": null, "git_info": [null, null], "swanlab": {"version": "0.6.7", "_monitor": 5, "logdir": "/home/<USER>/weak_target_finetune/swanlog/run-20250728_185352-42naepkgsbcmvboo501d3"}}