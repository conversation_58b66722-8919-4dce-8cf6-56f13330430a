#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主训练脚本 - 真正的视频序列微调
支持所有8个序列：data01, data02, data04, data06, data07, data19, data23, data26
每隔10帧抽取1帧直到抽完，进行真正的Qwen2.5-VL LoRA微调
"""

import sys
import os
sys.path.append('src')

from lightweight_qwen_trainer import main as lightweight_qwen_trainer_main

def main():
    print("=== 超轻量级Qwen2.5-VL微调训练系统 ===")
    print("🎯 目标: 在显存不足情况下完成微调")
    print("📊 微调策略: 只用2个序列，每序列2帧")
    print("🎬 训练方式: 超轻量级LoRA微调")
    print("🔧 模型架构: Qwen2.5-VL-7B + 8bit量化 + 极小LoRA")
    print("⚡ 显存优化: 专为显存不足设计")

    print("\n🚀 注意: 这是为显存不足环境设计的最小化微调")
    print("💾 使用2个序列样本，每个序列2帧，3个训练轮数")
    print("⏱️  预计训练时间: 5-10分钟")

    print("\n开始Qwen2.5-VL视频微调训练...")
    success = lightweight_qwen_trainer_main()

    if success:
        print("\n🎉 超轻量级Qwen2.5-VL微调训练完成！")
        print("📁 微调后的模型已保存到 output/lightweight_qwen_finetuned/")
        print("📋 这是为显存不足环境设计的最小化微调")
        print("💡 虽然简化但仍然可以验证微调流程")
    else:
        print("\n❌ 超轻量级Qwen2.5-VL微调失败！请检查错误信息")

    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
