#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主训练脚本 - 真正的视频序列微调
支持所有8个序列：data01, data02, data04, data06, data07, data19, data23, data26
每隔10帧抽取1帧直到抽完，进行真正的Qwen2.5-VL LoRA微调
"""

import sys
import os
sys.path.append('src')

from supercharged_qwen_trainer import main as supercharged_qwen_trainer_main

def main():
    print("=== 超强Qwen2.5-VL微调训练系统 ===")
    print("🎯 目标: 最大化检测效果")
    print("📊 微调策略: 最大训练数据+最强LoRA+最优训练")
    print("🎬 训练方式: 超强LoRA微调")
    print("🔧 模型架构: Qwen2.5-VL-7B + 最大LoRA + 最高质量数据")
    print("⚡ 显存优化: 手动训练循环 + 梯度裁剪")

    print("\n🚀 注意: 这是最强优化版本，最大化提升效果")
    print("💾 使用最大训练数据，多尺度序列训练，12个训练轮数")
    print("⏱️  预计训练时间: 45-60分钟")

    print("\n开始Qwen2.5-VL视频微调训练...")
    success = supercharged_qwen_trainer_main()

    if success:
        print("\n🎉 超强Qwen2.5-VL微调训练完成！")
        print("📁 微调后的模型已保存到 output/supercharged_qwen_finetuned/")
        print("📋 这是最强优化版本，应该有最佳的检测效果")
        print("💡 下一步: 运行超强检测器验证效果")
    else:
        print("\n❌ 超强Qwen2.5-VL微调失败！请检查错误信息")

    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
