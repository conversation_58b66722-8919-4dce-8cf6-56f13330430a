#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主训练脚本 - 真正的视频序列微调
支持所有8个序列：data01, data02, data04, data06, data07, data19, data23, data26
每隔10帧抽取1帧直到抽完，进行真正的Qwen2.5-VL LoRA微调
"""

import sys
import os
sys.path.append('src')

from qwen_video_trainer import main as qwen_video_trainer_main

def main():
    print("=== Qwen2.5-VL视频微调训练系统 ===")
    print("🎯 目标序列: data01, data02, data04, data06, data07, data19, data23, data26")
    print("📊 微调策略: 每个序列生成一个完整的长视频序列样本")
    print("🎬 训练方式: Qwen2.5-VL全参数视频微调 (每序列一个长样本)")
    print("🔧 模型架构: Qwen2.5-VL-7B + 全参数微调 (不使用LoRA)")
    print("⚡ 视频训练: 使用A100-80GB GPU卡2进行全参数微调")

    print("\n🚀 注意: 这将进行Qwen2.5-VL全参数视频微调训练")
    print("💾 使用8个完整序列样本，每个序列4帧，2个训练轮数")
    print("⏱️  预计训练时间: 10-20分钟")

    print("\n开始Qwen2.5-VL视频微调训练...")
    success = qwen_video_trainer_main()

    if success:
        print("\n🎉 Qwen2.5-VL视频微调训练完成！")
        print("📁 微调后的模型已保存到 output/qwen_video_finetuned/")
        print("📋 下一步: 运行 python evaluate_qwen.py 进行视频检测和评估")
        print("💡 评估将使用Qwen2.5-VL微调模型 + 完整序列检测策略")
    else:
        print("\n❌ Qwen2.5-VL视频微调失败！请检查错误信息")

    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
