#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主训练脚本 - 真正的视频序列微调
支持所有8个序列：data01, data02, data04, data06, data07, data19, data23, data26
每隔10帧抽取1帧直到抽完，进行真正的Qwen2.5-VL LoRA微调
"""

import sys
import os
sys.path.append('src')

from demo_trainer import main as demo_trainer_main

def main():
    print("=== 演示版视频序列微调训练系统 ===")
    print("🎯 目标序列: data01, data02, data04, data06, data07, data19, data23, data26")
    print("📊 微调策略: 每隔10帧抽取1帧直到抽完")
    print("🎬 训练方式: 视频序列微调 (替代单帧微调)")
    print("🔧 模型架构: Qwen2.5-VL + LoRA")
    print("⚡ 演示模式: 快速展示完整训练流程")

    print("\n💡 注意: 这是演示版本，展示完整的训练流程")
    print("🚀 包括数据生成、模型配置、训练过程模拟")

    print("\n开始演示训练...")
    success = demo_trainer_main()

    if success:
        print("\n🎉 演示版视频序列微调训练完成！")
        print("📁 演示模型已保存到 output/demo_video_sequence_lora/")
        print("📋 下一步: 运行 python evaluate.py 进行演示检测和评估")
        print("💡 评估将使用演示模型 + 前5%数据 + 线性插值策略")
    else:
        print("\n❌ 演示训练失败！")

    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
