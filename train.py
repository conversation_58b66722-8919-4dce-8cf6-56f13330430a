#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主训练脚本 - 真正的视频序列微调
支持所有8个序列：data01, data02, data04, data06, data07, data19, data23, data26
每隔10帧抽取1帧直到抽完，进行真正的Qwen2.5-VL LoRA微调
"""

import sys
import os
sys.path.append('src')

from simple_optimized_trainer import main as simple_optimized_trainer_main

def main():
    print("=== 简化优化Qwen2.5-VL微调训练系统 ===")
    print("🎯 目标: 提升检测效果，避免梯度问题")
    print("📊 微调策略: 增加训练数据+优化LoRA+简化训练")
    print("🎬 训练方式: 简化优化LoRA微调")
    print("🔧 模型架构: Qwen2.5-VL-7B + 适中LoRA")
    print("⚡ 显存优化: 简化训练循环")

    print("\n🚀 注意: 这是简化优化版本，避免梯度问题")
    print("💾 使用多个训练样本，5个训练轮数")
    print("⏱️  预计训练时间: 10-15分钟")

    print("\n开始Qwen2.5-VL视频微调训练...")
    success = simple_optimized_trainer_main()

    if success:
        print("\n🎉 简化优化Qwen2.5-VL微调训练完成！")
        print("📁 微调后的模型已保存到 output/simple_optimized_qwen_finetuned/")
        print("📋 这是简化优化版本，应该有更好的检测效果")
        print("💡 下一步: 运行优化检测器验证效果")
    else:
        print("\n❌ 简化优化Qwen2.5-VL微调失败！请检查错误信息")

    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
