#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主训练脚本 - 真正的视频序列微调
支持所有8个序列：data01, data02, data04, data06, data07, data19, data23, data26
每隔10帧抽取1帧直到抽完，进行真正的Qwen2.5-VL LoRA微调
"""

import sys
import os
sys.path.append('src')

from real_video_trainer import main as real_trainer_main

def main():
    print("=== 真正的视频序列微调训练系统 ===")
    print("🎯 目标序列: data01, data02, data04, data06, data07, data19, data23, data26")
    print("📊 微调策略: 每隔10帧抽取1帧直到抽完")
    print("🎬 训练方式: 视频序列微调 (替代单帧微调)")
    print("🔧 模型架构: Qwen2.5-VL + LoRA")
    print("⚡ 真正训练: 使用PyTorch进行实际的模型微调")

    print("\n⚠️  注意: 这将进行真正的模型微调训练，可能需要较长时间...")
    print("💾 确保有足够的GPU内存和存储空间")

    print("\n开始真正的微调训练...")
    success = real_trainer_main()

    if success:
        print("\n🎉 真正的视频序列微调训练完成！")
        print("📁 微调后的模型已保存到 output/video_sequence_lora/")
        print("📋 下一步: 运行 python evaluate.py 进行检测和评估")
        print("💡 评估将使用微调后的模型 + 前5%数据 + 线性插值策略")
    else:
        print("\n❌ 微调训练失败！请检查GPU内存和配置")

    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
