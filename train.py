#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主训练脚本 - 真正的视频序列微调
支持所有8个序列：data01, data02, data04, data06, data07, data19, data23, data26
每隔10帧抽取1帧直到抽完，进行真正的Qwen2.5-VL LoRA微调
"""

import sys
import os
sys.path.append('src')

from lightweight_trainer import main as lightweight_trainer_main

def main():
    print("=== 轻量级真实视频序列微调训练系统 ===")
    print("🎯 目标序列: data01, data02, data04, data06, data07, data19, data23, data26")
    print("📊 微调策略: 每个序列5帧，内存优化版本")
    print("🎬 训练方式: 轻量级视频序列微调 (真实训练)")
    print("🔧 模型架构: Qwen2.5-VL + 轻量级LoRA")
    print("⚡ 真正训练: 使用A100-80GB GPU卡2进行实际微调")

    print("\n🚀 注意: 这将进行真正的模型微调训练")
    print("💾 使用内存优化策略，适配A100-80GB GPU")
    print("⏱️  预计训练时间: 15-30分钟")

    print("\n开始轻量级真实微调训练...")
    success = lightweight_trainer_main()

    if success:
        print("\n🎉 轻量级真实视频序列微调训练完成！")
        print("📁 微调后的模型已保存到 output/lightweight_video_sequence_lora/")
        print("📋 下一步: 运行 python evaluate.py 进行真实检测和评估")
        print("💡 评估将使用真实微调模型 + 前5%数据 + 线性插值策略")
    else:
        print("\n❌ 轻量级训练失败！请检查错误信息")

    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
