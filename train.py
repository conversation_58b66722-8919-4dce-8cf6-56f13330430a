#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主训练脚本 - 真正的视频序列微调
支持所有8个序列：data01, data02, data04, data06, data07, data19, data23, data26
每隔10帧抽取1帧直到抽完，进行真正的Qwen2.5-VL LoRA微调
"""

import sys
import os
sys.path.append('src')

from frame_level_trainer import main as frame_level_trainer_main

def main():
    print("=== 真正的帧级视频序列微调训练系统 ===")
    print("🎯 目标序列: data01, data02, data04, data06, data07, data19, data23, data26")
    print("📊 微调策略: 每隔10帧抽取到最后，每帧独立样本训练")
    print("🎬 训练方式: 帧级视频序列微调 (真正的大规模训练)")
    print("🔧 模型架构: Qwen2.5-VL + 帧级LoRA (rank=16, alpha=32)")
    print("⚡ 真正训练: 使用A100-80GB GPU卡2进行732样本微调")

    print("\n🚀 注意: 这将进行真正的大规模模型微调训练")
    print("💾 使用732个独立帧样本，3个训练轮数")
    print("⏱️  预计训练时间: 30-60分钟")

    print("\n开始真正的帧级深度微调训练...")
    success = frame_level_trainer_main()

    if success:
        print("\n🎉 真正的帧级视频序列微调训练完成！")
        print("📁 微调后的模型已保存到 output/frame_level_video_sequence_lora/")
        print("📋 下一步: 运行 python evaluate.py 进行真正检测和评估")
        print("💡 评估将使用真正微调模型 + 前5%数据 + 线性插值策略")
    else:
        print("\n❌ 真正训练失败！请检查错误信息")

    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
