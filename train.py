#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主训练脚本 - 真正的视频序列微调
支持所有8个序列：data01, data02, data04, data06, data07, data19, data23, data26
每隔10帧抽取1帧直到抽完，进行真正的Qwen2.5-VL LoRA微调
"""

import sys
import os
sys.path.append('src')

from stable_trainer import main as stable_trainer_main

def main():
    print("=== 稳定完整视频序列微调训练系统 ===")
    print("🎯 目标序列: data01, data02, data04, data06, data07, data19, data23, data26")
    print("📊 微调策略: 每隔10帧抽取到最后，每样本3帧稳定训练")
    print("🎬 训练方式: 稳定视频序列微调 (长时间训练)")
    print("🔧 模型架构: Qwen2.5-VL + 稳定LoRA (rank=16, alpha=32)")
    print("⚡ 稳定训练: 使用A100-80GB GPU卡2进行稳定微调")

    print("\n🚀 注意: 这将进行稳定的深度模型微调训练")
    print("💾 使用所有8个序列训练数据，10个训练轮数")
    print("⏱️  预计训练时间: 20-40分钟")

    print("\n开始稳定深度微调训练...")
    success = stable_trainer_main()

    if success:
        print("\n🎉 稳定深度视频序列微调训练完成！")
        print("📁 微调后的模型已保存到 output/stable_video_sequence_lora/")
        print("📋 下一步: 运行 python evaluate.py 进行稳定检测和评估")
        print("💡 评估将使用稳定微调模型 + 前5%数据 + 线性插值策略")
    else:
        print("\n❌ 稳定训练失败！请检查错误信息")

    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
