#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主训练脚本 - 真正的视频序列微调
支持所有8个序列：data01, data02, data04, data06, data07, data19, data23, data26
每隔10帧抽取1帧直到抽完，进行真正的Qwen2.5-VL LoRA微调
"""

import sys
import os
sys.path.append('src')

from real_sequence_trainer import main as real_sequence_trainer_main

def main():
    print("=== 真正的完整序列微调训练系统 ===")
    print("🎯 目标序列: data01, data02, data04, data06, data07, data19, data23, data26")
    print("📊 微调策略: 每个序列生成一个完整的长视频序列样本")
    print("🎬 训练方式: 真正的完整序列微调 (每序列一个长样本)")
    print("🔧 模型架构: Qwen2.5-VL-7B + 完整序列LoRA (rank=8, alpha=16)")
    print("⚡ 完整训练: 使用A100-80GB GPU卡2进行完整序列微调")

    print("\n🚀 注意: 这将进行真正的完整序列模型微调训练")
    print("💾 使用8个完整序列样本，每个序列6帧，5个训练轮数")
    print("⏱️  预计训练时间: 15-30分钟")

    print("\n开始真正的完整序列微调训练...")
    success = real_sequence_trainer_main()

    if success:
        print("\n🎉 真正的完整序列微调训练完成！")
        print("📁 微调后的模型已保存到 output/real_sequence_lora/")
        print("📋 下一步: 运行 python evaluate_real.py 进行完整序列检测和评估")
        print("💡 评估将使用真正完整序列微调模型 + 完整序列检测策略")
    else:
        print("\n❌ 完整序列训练失败！请检查错误信息")

    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
