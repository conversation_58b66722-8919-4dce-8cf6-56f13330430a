#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主训练脚本 - 真正的视频序列微调
支持所有8个序列：data01, data02, data04, data06, data07, data19, data23, data26
每隔10帧抽取1帧直到抽完，进行真正的Qwen2.5-VL LoRA微调
"""

import sys
import os
sys.path.append('src')

from correct_video_trainer import main as correct_video_trainer_main

def main():
    print("=== 正确的视频序列微调训练系统 ===")
    print("🎯 目标序列: data01, data02, data04, data06, data07, data19, data23, data26")
    print("📊 微调策略: 每隔10帧抽取组成视频序列进行训练")
    print("🎬 训练方式: 真正的视频序列微调 (多帧视频输入)")
    print("🔧 模型架构: Qwen2.5-VL + 视频序列LoRA (rank=16, alpha=32)")
    print("⚡ 视频训练: 使用A100-80GB GPU卡2进行视频序列微调")

    print("\n🚀 注意: 这将进行真正的视频序列模型微调训练")
    print("💾 使用视频片段样本，每个片段8帧，5个训练轮数")
    print("⏱️  预计训练时间: 20-40分钟")

    print("\n开始正确的视频序列微调训练...")
    success = correct_video_trainer_main()

    if success:
        print("\n🎉 正确的视频序列微调训练完成！")
        print("📁 微调后的模型已保存到 output/correct_video_sequence_lora/")
        print("📋 下一步: 运行 python evaluate.py 进行视频序列检测和评估")
        print("💡 评估将使用视频序列微调模型 + 视频序列检测策略")
    else:
        print("\n❌ 视频序列训练失败！请检查错误信息")

    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
