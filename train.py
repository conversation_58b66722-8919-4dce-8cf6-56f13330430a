#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主训练脚本 - 真正的视频序列微调
支持所有8个序列：data01, data02, data04, data06, data07, data19, data23, data26
每隔10帧抽取1帧直到抽完，进行真正的Qwen2.5-VL LoRA微调
"""

import sys
import os
sys.path.append('src')

from complete_trainer import main as complete_trainer_main

def main():
    print("=== 完整深度视频序列微调训练系统 ===")
    print("🎯 目标序列: data01, data02, data04, data06, data07, data19, data23, data26")
    print("📊 微调策略: 每隔10帧抽取到最后，总计732帧完整数据")
    print("🎬 训练方式: 完整深度视频序列微调 (长时间训练)")
    print("🔧 模型架构: Qwen2.5-VL + 深度LoRA (rank=32, alpha=64)")
    print("⚡ 深度训练: 使用A100-80GB GPU卡2进行深度微调")

    print("\n🚀 注意: 这将进行完整的深度模型微调训练")
    print("💾 使用所有732帧训练数据，5个训练轮数")
    print("⏱️  预计训练时间: 30-60分钟")

    print("\n开始完整深度微调训练...")
    success = complete_trainer_main()

    if success:
        print("\n🎉 完整深度视频序列微调训练完成！")
        print("📁 微调后的模型已保存到 output/complete_video_sequence_lora/")
        print("📋 下一步: 运行 python evaluate.py 进行完整检测和评估")
        print("💡 评估将使用完整微调模型 + 前5%数据 + 线性插值策略")
    else:
        print("\n❌ 完整训练失败！请检查错误信息")

    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
